-- ===================================================
-- UNIFIED META-AGENT PLATFORM DATABASE SCHEMA
-- ===================================================

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- ===================================================
-- BASE TABLES
-- ===================================================

-- Agent registry table
CREATE TABLE agents (
    agent_id VARCHAR(255) PRIMARY KEY,
    agent_type VARCHAR(100) NOT NULL,
    agent_name VARCHAR(255) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'ACTIVE',
    health_score DECIMAL(5,2) DEFAULT 100.00,
    last_heartbeat TIMESTAMP,
    endpoint_url VARCHAR(500),
    capabilities TEXT[],
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    version INT NOT NULL DEFAULT 1
);

-- Base events table
CREATE TABLE events (
    event_id VARCHAR(255) PRIMARY KEY,
    event_type VARCHAR(100) NOT NULL,
    agent_id VARCHAR(255) NOT NULL,
    severity VARCHAR(20) NOT NULL,
    status VARCHAR(50) NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    description TEXT,
    metadata JSONB,
    correlation_id VARCHAR(255),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (agent_id) REFERENCES agents(agent_id)
);

-- Base executions table
CREATE TABLE executions (
    execution_id VARCHAR(255) PRIMARY KEY,
    execution_type VARCHAR(100) NOT NULL,
    agent_id VARCHAR(255) NOT NULL,
    parent_execution_id VARCHAR(255),
    status VARCHAR(50) NOT NULL,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    duration_ms BIGINT,
    input_data JSONB,
    output_data JSONB,
    error_message TEXT,
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (agent_id) REFERENCES agents(agent_id),
    FOREIGN KEY (parent_execution_id) REFERENCES executions(execution_id)
);

-- ===================================================
-- KNOWLEDGE BASE AGENT TABLES
-- ===================================================

CREATE TABLE knowledge_items (
    item_id VARCHAR(255) PRIMARY KEY,
    title VARCHAR(500) NOT NULL,
    content TEXT NOT NULL,
    content_type VARCHAR(100) NOT NULL,
    source_id VARCHAR(255),
    confidence_score DECIMAL(5,2),
    validity_period INTERVAL,
    tags TEXT[],
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    version INT NOT NULL DEFAULT 1
);

CREATE TABLE knowledge_sources (
    source_id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100) NOT NULL,
    url VARCHAR(1000),
    reliability_score DECIMAL(5,2),
    last_updated TIMESTAMP,
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE knowledge_entities (
    entity_id VARCHAR(255) PRIMARY KEY,
    item_id VARCHAR(255) NOT NULL,
    entity_type VARCHAR(100) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    confidence DECIMAL(5,2),
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (item_id) REFERENCES knowledge_items(item_id) ON DELETE CASCADE
);

CREATE TABLE knowledge_relationships (
    relationship_id VARCHAR(255) PRIMARY KEY,
    source_entity_id VARCHAR(255) NOT NULL,
    target_entity_id VARCHAR(255) NOT NULL,
    relationship_type VARCHAR(100) NOT NULL,
    strength DECIMAL(5,2),
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (source_entity_id) REFERENCES knowledge_entities(entity_id) ON DELETE CASCADE,
    FOREIGN KEY (target_entity_id) REFERENCES knowledge_entities(entity_id) ON DELETE CASCADE
);

-- ===================================================
-- DATA PROCESSING AGENT TABLES
-- ===================================================

CREATE TABLE data_pipelines (
    pipeline_id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(50) NOT NULL,
    configuration JSONB,
    schedule_expression VARCHAR(255),
    enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    version INT NOT NULL DEFAULT 1
);

CREATE TABLE data_sources (
    source_id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100) NOT NULL,
    connection_string VARCHAR(1000),
    configuration JSONB,
    schema_definition JSONB,
    enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE data_transformations (
    transformation_id VARCHAR(255) PRIMARY KEY,
    pipeline_id VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100) NOT NULL,
    configuration JSONB,
    order_index INT NOT NULL,
    enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (pipeline_id) REFERENCES data_pipelines(pipeline_id) ON DELETE CASCADE
);

CREATE TABLE processing_results (
    result_id VARCHAR(255) PRIMARY KEY,
    execution_id VARCHAR(255) NOT NULL,
    pipeline_id VARCHAR(255) NOT NULL,
    records_processed BIGINT,
    records_successful BIGINT,
    records_failed BIGINT,
    processing_time_ms BIGINT,
    quality_score DECIMAL(5,2),
    anomalies_detected INT,
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (execution_id) REFERENCES executions(execution_id) ON DELETE CASCADE,
    FOREIGN KEY (pipeline_id) REFERENCES data_pipelines(pipeline_id)
);

-- ===================================================
-- TASK ORCHESTRATOR AGENT TABLES
-- ===================================================

CREATE TABLE workflow_definitions (
    workflow_id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    version VARCHAR(50) NOT NULL,
    definition JSONB NOT NULL,
    enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE task_definitions (
    task_id VARCHAR(255) PRIMARY KEY,
    workflow_id VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100) NOT NULL,
    configuration JSONB,
    dependencies TEXT[],
    required_agents TEXT[],
    timeout_ms BIGINT,
    retry_count INT DEFAULT 0,
    order_index INT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (workflow_id) REFERENCES workflow_definitions(workflow_id) ON DELETE CASCADE
);

CREATE TABLE workflow_executions (
    execution_id VARCHAR(255) PRIMARY KEY,
    workflow_id VARCHAR(255) NOT NULL,
    status VARCHAR(50) NOT NULL,
    priority VARCHAR(20) NOT NULL,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    context JSONB,
    result JSONB,
    error_message TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (workflow_id) REFERENCES workflow_definitions(workflow_id)
);

CREATE TABLE task_executions (
    execution_id VARCHAR(255) PRIMARY KEY,
    workflow_execution_id VARCHAR(255) NOT NULL,
    task_id VARCHAR(255) NOT NULL,
    assigned_agent_id VARCHAR(255),
    status VARCHAR(50) NOT NULL,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    input_data JSONB,
    output_data JSONB,
    error_message TEXT,
    retry_count INT DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (workflow_execution_id) REFERENCES workflow_executions(execution_id) ON DELETE CASCADE,
    FOREIGN KEY (task_id) REFERENCES task_definitions(task_id),
    FOREIGN KEY (assigned_agent_id) REFERENCES agents(agent_id)
);

-- ===================================================
-- SECURITY MONITOR AGENT TABLES
-- ===================================================

CREATE TABLE security_events (
    event_id VARCHAR(255) PRIMARY KEY,
    event_type VARCHAR(100) NOT NULL,
    source_agent_id VARCHAR(255),
    severity VARCHAR(20) NOT NULL,
    status VARCHAR(50) NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    source_ip VARCHAR(45),
    source_location JSONB,
    description TEXT,
    threat_indicators TEXT[],
    correlation_id VARCHAR(255),
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (source_agent_id) REFERENCES agents(agent_id)
);

CREATE TABLE security_responses (
    response_id VARCHAR(255) PRIMARY KEY,
    event_id VARCHAR(255) NOT NULL,
    response_type VARCHAR(100) NOT NULL,
    status VARCHAR(50) NOT NULL,
    actions_taken TEXT[],
    automated BOOLEAN DEFAULT false,
    effectiveness_score DECIMAL(5,2),
    timestamp TIMESTAMP NOT NULL,
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES security_events(event_id) ON DELETE CASCADE
);

CREATE TABLE threat_analyses (
    analysis_id VARCHAR(255) PRIMARY KEY,
    event_id VARCHAR(255) NOT NULL,
    threat_type VARCHAR(100) NOT NULL,
    confidence_score DECIMAL(5,2) NOT NULL,
    risk_level VARCHAR(20) NOT NULL,
    indicators JSONB,
    recommendations TEXT[],
    analyzed_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES security_events(event_id) ON DELETE CASCADE
);

-- ===================================================
-- RESOURCE MANAGER AGENT TABLES
-- ===================================================

CREATE TABLE allocation_requests (
    request_id VARCHAR(255) PRIMARY KEY,
    requesting_agent_id VARCHAR(255) NOT NULL,
    resource_requirements JSONB NOT NULL,
    constraints JSONB,
    priority VARCHAR(20) NOT NULL,
    status VARCHAR(50) NOT NULL,
    requested_at TIMESTAMP NOT NULL,
    deadline TIMESTAMP,
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (requesting_agent_id) REFERENCES agents(agent_id)
);

CREATE TABLE allocation_results (
    result_id VARCHAR(255) PRIMARY KEY,
    request_id VARCHAR(255) NOT NULL,
    allocated_resources JSONB,
    cost_estimate JSONB,
    performance_prediction JSONB,
    carbon_footprint JSONB,
    optimization_applied BOOLEAN DEFAULT false,
    allocated_at TIMESTAMP NOT NULL,
    expires_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (request_id) REFERENCES allocation_requests(request_id) ON DELETE CASCADE
);

CREATE TABLE resource_health (
    health_id VARCHAR(255) PRIMARY KEY,
    resource_id VARCHAR(255) NOT NULL,
    resource_type VARCHAR(100) NOT NULL,
    health_score DECIMAL(5,2) NOT NULL,
    utilization_percent DECIMAL(5,2),
    performance_metrics JSONB,
    issues TEXT[],
    last_check TIMESTAMP NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- ===================================================
-- SUPREME INTELLIGENCE AGENT TABLES
-- ===================================================

CREATE TABLE strategic_plans (
    plan_id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    objectives JSONB NOT NULL,
    timeline JSONB,
    success_metrics JSONB,
    status VARCHAR(50) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    version INT NOT NULL DEFAULT 1
);

CREATE TABLE platform_consciousness (
    consciousness_id VARCHAR(255) PRIMARY KEY,
    timestamp TIMESTAMP NOT NULL,
    cognitive_load DECIMAL(5,2),
    awareness_level VARCHAR(50),
    decision_confidence DECIMAL(5,2),
    system_state JSONB,
    emergent_patterns JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE crisis_events (
    crisis_id VARCHAR(255) PRIMARY KEY,
    event_type VARCHAR(100) NOT NULL,
    severity VARCHAR(20) NOT NULL,
    scope VARCHAR(100) NOT NULL,
    affected_agents TEXT[],
    description TEXT,
    detected_at TIMESTAMP NOT NULL,
    resolved_at TIMESTAMP,
    impact_assessment JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE crisis_responses (
    response_id VARCHAR(255) PRIMARY KEY,
    crisis_id VARCHAR(255) NOT NULL,
    response_type VARCHAR(100) NOT NULL,
    actions JSONB NOT NULL,
    coordination_plan JSONB,
    effectiveness_score DECIMAL(5,2),
    executed_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (crisis_id) REFERENCES crisis_events(crisis_id) ON DELETE CASCADE
);

-- ===================================================
-- AGENT FACTORY AGENT TABLES
-- ===================================================

CREATE TABLE generation_requests (
    request_id VARCHAR(255) PRIMARY KEY,
    agent_specification JSONB NOT NULL,
    requirements JSONB,
    preferences JSONB,
    requested_by VARCHAR(255),
    status VARCHAR(50) NOT NULL,
    priority VARCHAR(20) NOT NULL,
    requested_at TIMESTAMP NOT NULL,
    deadline TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE generation_responses (
    response_id VARCHAR(255) PRIMARY KEY,
    request_id VARCHAR(255) NOT NULL,
    generated_code JSONB,
    test_suite JSONB,
    quality_report JSONB,
    deployment_config JSONB,
    ai_insights JSONB,
    generation_time_ms BIGINT,
    generated_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (request_id) REFERENCES generation_requests(request_id) ON DELETE CASCADE
);

CREATE TABLE agent_templates (
    template_id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100) NOT NULL,
    template_files JSONB NOT NULL,
    configuration JSONB,
    version VARCHAR(50) NOT NULL,
    enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- ===================================================
-- INDEXES FOR PERFORMANCE
-- ===================================================

-- Agent registry indexes
CREATE INDEX idx_agents_type ON agents(agent_type);
CREATE INDEX idx_agents_status ON agents(status);
CREATE INDEX idx_agents_heartbeat ON agents(last_heartbeat);

-- Events indexes
CREATE INDEX idx_events_type ON events(event_type);
CREATE INDEX idx_events_agent ON events(agent_id);
CREATE INDEX idx_events_timestamp ON events(timestamp);
CREATE INDEX idx_events_correlation ON events(correlation_id);
CREATE INDEX idx_events_severity ON events(severity);

-- Executions indexes
CREATE INDEX idx_executions_type ON executions(execution_type);
CREATE INDEX idx_executions_agent ON executions(agent_id);
CREATE INDEX idx_executions_parent ON executions(parent_execution_id);
CREATE INDEX idx_executions_status ON executions(status);
CREATE INDEX idx_executions_started ON executions(started_at);

-- Knowledge base indexes
CREATE INDEX idx_knowledge_items_type ON knowledge_items(content_type);
CREATE INDEX idx_knowledge_items_source ON knowledge_items(source_id);
CREATE INDEX idx_knowledge_items_tags ON knowledge_items USING GIN(tags);
CREATE INDEX idx_knowledge_items_content ON knowledge_items USING GIN(to_tsvector('english', content));

-- Data processing indexes
CREATE INDEX idx_data_pipelines_status ON data_pipelines(status);
CREATE INDEX idx_data_sources_type ON data_sources(type);
CREATE INDEX idx_processing_results_pipeline ON processing_results(pipeline_id);
CREATE INDEX idx_processing_results_execution ON processing_results(execution_id);

-- Task orchestrator indexes
CREATE INDEX idx_workflow_executions_workflow ON workflow_executions(workflow_id);
CREATE INDEX idx_workflow_executions_status ON workflow_executions(status);
CREATE INDEX idx_task_executions_workflow ON task_executions(workflow_execution_id);
CREATE INDEX idx_task_executions_task ON task_executions(task_id);
CREATE INDEX idx_task_executions_agent ON task_executions(assigned_agent_id);

-- Security indexes
CREATE INDEX idx_security_events_type ON security_events(event_type);
CREATE INDEX idx_security_events_severity ON security_events(severity);
CREATE INDEX idx_security_events_timestamp ON security_events(timestamp);
CREATE INDEX idx_security_events_correlation ON security_events(correlation_id);

-- Resource manager indexes
CREATE INDEX idx_allocation_requests_agent ON allocation_requests(requesting_agent_id);
CREATE INDEX idx_allocation_requests_status ON allocation_requests(status);
CREATE INDEX idx_allocation_requests_priority ON allocation_requests(priority);
CREATE INDEX idx_resource_health_type ON resource_health(resource_type);
CREATE INDEX idx_resource_health_resource ON resource_health(resource_id);

-- Supreme intelligence indexes
CREATE INDEX idx_strategic_plans_status ON strategic_plans(status);
CREATE INDEX idx_platform_consciousness_timestamp ON platform_consciousness(timestamp);
CREATE INDEX idx_crisis_events_severity ON crisis_events(severity);
CREATE INDEX idx_crisis_events_detected ON crisis_events(detected_at);

-- Agent factory indexes
CREATE INDEX idx_generation_requests_status ON generation_requests(status);
CREATE INDEX idx_generation_requests_priority ON generation_requests(priority);
CREATE INDEX idx_agent_templates_category ON agent_templates(category);
CREATE INDEX idx_agent_templates_enabled ON agent_templates(enabled);

-- ===================================================
-- SAMPLE DATA FOR TESTING
-- ===================================================

-- Insert sample agents
INSERT INTO agents (agent_id, agent_type, agent_name, status, health_score, capabilities) VALUES
('discovery-registry', 'DISCOVERY_REGISTRY', 'Discovery Registry Agent', 'ACTIVE', 95.5, ARRAY['service-discovery', 'health-monitoring']),
('security-monitor', 'SECURITY_MONITOR', 'Security Monitor Agent', 'ACTIVE', 98.2, ARRAY['threat-detection', 'behavioral-analysis']),
('resource-manager', 'RESOURCE_MANAGER', 'Resource Manager Agent', 'ACTIVE', 92.1, ARRAY['resource-allocation', 'cost-optimization']),
('data-processing', 'DATA_PROCESSING', 'Data Processing Agent', 'ACTIVE', 96.8, ARRAY['data-transformation', 'quality-analysis']),
('knowledge-base', 'KNOWLEDGE_BASE', 'Knowledge Base Agent', 'ACTIVE', 94.3, ARRAY['knowledge-extraction', 'semantic-search']),
('task-orchestrator', 'TASK_ORCHESTRATOR', 'Task Orchestrator Agent', 'ACTIVE', 97.5, ARRAY['workflow-orchestration', 'task-scheduling']),
('agent-factory', 'AGENT_FACTORY', 'Agent Factory Agent', 'ACTIVE', 93.7, ARRAY['agent-generation', 'code-generation']),
('supreme-intelligence', 'SUPREME_INTELLIGENCE', 'Supreme Intelligence Agent', 'ACTIVE', 99.1, ARRAY['strategic-planning', 'crisis-management']);