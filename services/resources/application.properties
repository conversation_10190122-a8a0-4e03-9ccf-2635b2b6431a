# ===================================================
# UNIFIED META-AGENT PLATFORM CONFIGURATION
# ===================================================

# Application Configuration
spring.application.name=meta-agent-platform
server.port=8080

# ===================================================
# DATABASE CONFIGURATION
# ===================================================

# PostgreSQL - Unified Database (Development Mode)
spring.datasource.url=*****************************************
spring.datasource.driverClassName=org.postgresql.Driver
spring.datasource.username=${DB_USERNAME:koneti}
spring.datasource.password=${DB_PASSWORD:}

# JPA Configuration
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=false

# Flyway Configuration (Disable for development)
spring.flyway.enabled=false

# Connection Pool Configuration
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000

# ===================================================
# REDIS CONFIGURATION
# ===================================================
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.timeout=2000ms
spring.redis.lettuce.pool.max-active=8
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.min-idle=0

# ===================================================
# MANAGEMENT & MONITORING
# ===================================================
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=always
management.metrics.export.prometheus.enabled=true
management.endpoints.web.base-path=/actuator

# ===================================================
# LOGGING CONFIGURATION
# ===================================================
logging.level.com.multiagent.platform=INFO
logging.level.org.springframework.web=INFO
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type=TRACE
logging.level.root=INFO

# ===================================================
# SECURITY MONITOR AGENT CONFIGURATION
# ===================================================
security.monitor.threat.detection.enabled=true
security.monitor.behavioral.analysis.enabled=true
security.monitor.incident.response.enabled=true
security.monitor.ai.enabled=true
security.monitor.ai.service.url=http://localhost:9082

# ===================================================
# RESOURCE MANAGER AGENT CONFIGURATION
# ===================================================
resource.manager.cloud.providers=aws,gcp,azure,kubernetes
resource.manager.auto.scaling.enabled=true
resource.manager.cost.optimization.enabled=true
resource.manager.ai.enabled=true
resource.manager.ai.service.url=http://localhost:9083

# Cloud Provider Configuration
cloud.aws.enabled=false
cloud.gcp.enabled=false
cloud.azure.enabled=false
cloud.kubernetes.enabled=true

# ===================================================
# DATA PROCESSING AGENT CONFIGURATION
# ===================================================
data.processing.stream.enabled=true
data.processing.quality.analysis.enabled=true
data.processing.anomaly.detection.enabled=true
data.processing.ai.enabled=true
data.processing.ai.service.url=http://localhost:9084

# Kafka Configuration
spring.kafka.bootstrap-servers=localhost:9092
spring.kafka.consumer.group-id=meta-agent-platform-group
spring.kafka.consumer.enable-auto-commit=false
spring.kafka.consumer.auto-offset-reset=earliest

# ===================================================
# KNOWLEDGE BASE AGENT CONFIGURATION
# ===================================================
knowledge.base.vector.enabled=true
knowledge.base.graph.enabled=true
knowledge.base.semantic.search.enabled=true
knowledge.base.ai.enabled=true
knowledge.base.ai.service.url=http://localhost:9085

# Vector Database Configuration
vector.db.enabled=true
vector.db.type=chroma
vector.db.url=http://localhost:8000

# ===================================================
# TASK ORCHESTRATOR AGENT CONFIGURATION
# ===================================================
task.orchestrator.workflow.enabled=true
task.orchestrator.parallel.execution.enabled=true
task.orchestrator.priority.scheduling.enabled=true
task.orchestrator.ai.enabled=true
task.orchestrator.ai.service.url=http://localhost:9086

# Workflow Engine Configuration
workflow.engine.enabled=true
workflow.parallel.max.threads=10
workflow.timeout.default=300000
management.observations.web.enabled=false
management.tracing.enabled=false

# ===================================================
# AGENT FACTORY AGENT CONFIGURATION
# ===================================================
agent.factory.code.generation.enabled=true
agent.factory.deployment.enabled=true
agent.factory.testing.enabled=true
agent.factory.ai.enabled=true
agent.factory.ai.service.url=http://localhost:9091

# Factory Configuration
factory.template.repository.enabled=true
factory.template.repository.path=/tmp/agent-templates
factory.build.timeout=600000
factory.test.timeout=300000

# ===================================================
# SUPREME INTELLIGENCE AGENT CONFIGURATION
# ===================================================
supreme.intelligence.strategic.planning.enabled=true
supreme.intelligence.crisis.management.enabled=true
supreme.intelligence.platform.optimization.enabled=true
supreme.intelligence.consciousness.enabled=true
supreme.intelligence.ai.enabled=true
supreme.intelligence.ai.service.url=http://localhost:9092

# Intelligence Configuration
intelligence.model.ensemble.enabled=true
intelligence.model.providers=openai,anthropic,google
intelligence.reasoning.depth=deep
intelligence.consciousness.monitoring.enabled=true

# ===================================================
# DISCOVERY REGISTRY AGENT CONFIGURATION (Python)
# ===================================================
discovery.registry.enabled=true
discovery.registry.python.service.url=http://localhost:8082
discovery.registry.health.check.interval=30000
discovery.registry.service.timeout=5000

# ===================================================
# AGENT-TO-AGENT COMMUNICATION CONFIGURATION
# ===================================================
a2a.messaging.enabled=true
a2a.messaging.timeout=10000
a2a.messaging.retry.max.attempts=3
a2a.messaging.retry.delay=1000

# ===================================================
# PLATFORM CONFIGURATION
# ===================================================
platform.name=Meta-Agent Platform
platform.version=1.0.0
platform.description=Unified Multi-Agent Platform with Service Facade Pattern
platform.health.check.interval=30000
platform.metrics.collection.interval=60000