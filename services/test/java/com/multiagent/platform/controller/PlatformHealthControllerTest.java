package com.multiagent.platform.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.test.context.TestPropertySource;
import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpStatus;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.datasource.driver-class-name=org.h2.Driver",
    "spring.jpa.hibernate.ddl-auto=create-drop",
    "spring.flyway.enabled=false"
})
class PlatformHealthControllerTest {
    
    @LocalServerPort
    private int port;
    
    private TestRestTemplate restTemplate;
    private ObjectMapper objectMapper;
    
    @BeforeEach
    void setUp() {
        restTemplate = new TestRestTemplate();
        objectMapper = new ObjectMapper();
    }
    
    @Test
    void testHealthEndpoint() throws Exception {
        String url = "http://localhost:" + port + "/health";
        ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        
        Map<String, Object> body = response.getBody();
        assertNotNull(body);
        assertEquals("UP", body.get("status"));
        assertEquals("Meta-Agent Platform", body.get("platform"));
        assertEquals("1.0.0", body.get("version"));
        assertNotNull(body.get("timestamp"));
        assertEquals("All agents unified", body.get("agents"));
        assertEquals(8080, body.get("port"));
    }
    
    @Test
    void testPlatformInfoEndpoint() throws Exception {
        String url = "http://localhost:" + port + "/platform/info";
        ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        
        Map<String, Object> body = response.getBody();
        assertNotNull(body);
        assertEquals("Meta-Agent Platform", body.get("platform"));
        assertEquals("1.0.0", body.get("version"));
        assertEquals("Service Facade Pattern", body.get("architecture"));
        
        assertTrue(body.get("agents") instanceof java.util.List);
        java.util.List<?> agents = (java.util.List<?>) body.get("agents");
        assertEquals(8, agents.size());
        
        assertTrue(body.get("features") instanceof java.util.List);
        java.util.List<?> features = (java.util.List<?>) body.get("features");
        assertEquals(5, features.size());
    }
    
    @Test
    void testHealthEndpointResponseStructure() throws Exception {
        String url = "http://localhost:" + port + "/health";
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        
        // Parse response and verify structure
        var jsonNode = objectMapper.readTree(response.getBody());
        
        // Verify all required fields are present
        assertNotNull(jsonNode.get("status"));
        assertNotNull(jsonNode.get("platform"));
        assertNotNull(jsonNode.get("version"));
        assertNotNull(jsonNode.get("timestamp"));
        assertNotNull(jsonNode.get("agents"));
        assertNotNull(jsonNode.get("port"));
        
        // Verify field types
        assertTrue(jsonNode.get("status").isTextual());
        assertTrue(jsonNode.get("platform").isTextual());
        assertTrue(jsonNode.get("version").isTextual());
        assertTrue(jsonNode.get("agents").isTextual());
        assertTrue(jsonNode.get("port").isNumber());
    }
    
    @Test
    void testPlatformInfoEndpointResponseStructure() throws Exception {
        String url = "http://localhost:" + port + "/platform/info";
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        
        // Parse response and verify structure
        var jsonNode = objectMapper.readTree(response.getBody());
        
        // Verify all required fields are present
        assertNotNull(jsonNode.get("platform"));
        assertNotNull(jsonNode.get("version"));
        assertNotNull(jsonNode.get("architecture"));
        assertNotNull(jsonNode.get("agents"));
        assertNotNull(jsonNode.get("features"));
        
        // Verify field types
        assertTrue(jsonNode.get("platform").isTextual());
        assertTrue(jsonNode.get("version").isTextual());
        assertTrue(jsonNode.get("architecture").isTextual());
        assertTrue(jsonNode.get("agents").isArray());
        assertTrue(jsonNode.get("features").isArray());
    }
    
    @Test
    void testHealthEndpointTimestamp() throws Exception {
        String url = "http://localhost:" + port + "/health";
        
        // Make two requests with a small delay
        ResponseEntity<String> response1 = restTemplate.getForEntity(url, String.class);
        Thread.sleep(10); // Small delay
        ResponseEntity<String> response2 = restTemplate.getForEntity(url, String.class);
        
        assertEquals(HttpStatus.OK, response1.getStatusCode());
        assertEquals(HttpStatus.OK, response2.getStatusCode());
        
        // Parse responses
        var json1 = objectMapper.readTree(response1.getBody());
        var json2 = objectMapper.readTree(response2.getBody());
        
        // Timestamps should be different (or at least not throw error)
        assertNotEquals(json1.get("timestamp"), json2.get("timestamp"));
    }
    
    @Test
    void testEndpointAccessibility() throws Exception {
        String healthUrl = "http://localhost:" + port + "/health";
        String infoUrl = "http://localhost:" + port + "/platform/info";
        
        // Test that both endpoints are accessible
        ResponseEntity<String> healthResponse = restTemplate.getForEntity(healthUrl, String.class);
        ResponseEntity<String> infoResponse = restTemplate.getForEntity(infoUrl, String.class);
        
        assertEquals(HttpStatus.OK, healthResponse.getStatusCode());
        assertEquals(HttpStatus.OK, infoResponse.getStatusCode());
    }
    
    @Test
    void testHealthEndpointConsistency() throws Exception {
        String url = "http://localhost:" + port + "/health";
        
        // Make multiple requests to ensure consistency
        for (int i = 0; i < 3; i++) {
            ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
            assertEquals(HttpStatus.OK, response.getStatusCode());
            
            Map<String, Object> body = response.getBody();
            assertNotNull(body);
            assertEquals("UP", body.get("status"));
            assertEquals("Meta-Agent Platform", body.get("platform"));
            assertEquals("1.0.0", body.get("version"));
        }
    }
}