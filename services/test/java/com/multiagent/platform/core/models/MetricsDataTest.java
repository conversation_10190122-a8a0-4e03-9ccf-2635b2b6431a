package com.multiagent.platform.core.models;

import ai.twodot.metaagent.platform.core.models.MetricsData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class MetricsDataTest {
    
    private MetricsData metricsData;
    
    @BeforeEach
    void setUp() {
        metricsData = new MetricsData();
    }
    
    @Test
    void testAgentIdSetterAndGetter() {
        String agentId = "test-agent-123";
        metricsData.setAgentId(agentId);
        assertEquals(agentId, metricsData.getAgentId());
    }
    
    @Test
    void testAgentTypeSetterAndGetter() {
        String agentType = "TestAgent";
        metricsData.setAgentType(agentType);
        assertEquals(agentType, metricsData.getAgentType());
    }
    
    @Test
    void testTimestampSetterAndGetter() {
        Instant timestamp = Instant.now();
        metricsData.setTimestamp(timestamp);
        assertEquals(timestamp, metricsData.getTimestamp());
    }
    
    @Test
    void testHealthySetterAndGetter() {
        metricsData.setHealthy(true);
        assertTrue(metricsData.isHealthy());
        
        metricsData.setHealthy(false);
        assertFalse(metricsData.isHealthy());
    }
    
    @Test
    void testMetricsSetterAndGetter() {
        Map<String, Object> metrics = Map.of(
            "cpu_usage", 75.5,
            "memory_usage", 60.2,
            "status", "running"
        );
        
        metricsData.setMetrics(metrics);
        assertEquals(metrics, metricsData.getMetrics());
    }
    
    @Test
    void testAddMetric() {
        metricsData.addMetric("response_time", 120.5);
        metricsData.addMetric("error_rate", 0.02);
        
        Map<String, Object> metrics = metricsData.getMetrics();
        assertEquals(120.5, metrics.get("response_time"));
        assertEquals(0.02, metrics.get("error_rate"));
        assertEquals(2, metrics.size());
    }
    
    @Test
    void testAddMetricOverwrite() {
        metricsData.addMetric("cpu_usage", 50.0);
        metricsData.addMetric("cpu_usage", 75.0);
        
        assertEquals(75.0, metricsData.getMetrics().get("cpu_usage"));
        assertEquals(1, metricsData.getMetrics().size());
    }
    
    @Test
    void testDefaultMetricsMapIsEmpty() {
        assertTrue(metricsData.getMetrics().isEmpty());
    }
    
    @Test
    void testMetricsMapIsNotNull() {
        assertNotNull(metricsData.getMetrics());
    }
}