package com.multiagent.platform.core.integration.a2a.models;

import ai.twodot.metaagent.platform.core.integration.a2a.models.A2AResponse;
import ai.twodot.metaagent.platform.core.models.enums.Status;
import org.junit.jupiter.api.Test;

import java.time.Instant;

import static org.junit.jupiter.api.Assertions.*;

class A2AResponseTest {
    
    @Test
    void testBuilderWithAllFields() {
        String responseId = "resp-123";
        String requestId = "req-456";
        String sourceAgentId = "agent-1";
        String targetAgentId = "agent-2";
        Status status = Status.COMPLETED;
        Object payload = "test payload";
        String errorMessage = "test error";
        Instant timestamp = Instant.now();
        
        A2AResponse response = A2AResponse.builder()
                .responseId(responseId)
                .requestId(requestId)
                .from(sourceAgentId)
                .to(targetAgentId)
                .status(status)
                .payload(payload)
                .error(errorMessage)
                .timestamp(timestamp)
                .build();
        
        assertEquals(responseId, response.getResponseId());
        assertEquals(requestId, response.getRequestId());
        assertEquals(sourceAgentId, response.getSourceAgentId());
        assertEquals(targetAgentId, response.getTargetAgentId());
        assertEquals(Status.FAILED, response.getStatus()); // Error method sets status to FAILED
        assertEquals(payload, response.getPayload());
        assertEquals(errorMessage, response.getErrorMessage());
        assertEquals(timestamp, response.getTimestamp());
    }
    
    @Test
    void testBuilderWithMinimalFields() {
        String responseId = "resp-123";
        Status status = Status.COMPLETED;
        
        A2AResponse response = A2AResponse.builder()
                .responseId(responseId)
                .status(status)
                .build();
        
        assertEquals(responseId, response.getResponseId());
        assertEquals(status, response.getStatus());
        assertNotNull(response.getTimestamp());
    }
    
    @Test
    void testIsSuccessWhenCompleted() {
        A2AResponse response = A2AResponse.builder()
                .responseId("resp-123")
                .status(Status.COMPLETED)
                .build();
        
        assertTrue(response.isSuccess());
    }
    
    @Test
    void testIsSuccessWhenFailed() {
        A2AResponse response = A2AResponse.builder()
                .responseId("resp-123")
                .status(Status.FAILED)
                .build();
        
        assertFalse(response.isSuccess());
    }
    
    @Test
    void testIsSuccessWhenPending() {
        A2AResponse response = A2AResponse.builder()
                .responseId("resp-123")
                .status(Status.PENDING)
                .build();
        
        assertFalse(response.isSuccess());
    }
    
    @Test
    void testErrorMethodSetsStatusToFailed() {
        A2AResponse response = A2AResponse.builder()
                .responseId("resp-123")
                .status(Status.COMPLETED)
                .error("Something went wrong")
                .build();
        
        assertEquals(Status.FAILED, response.getStatus());
        assertEquals("Something went wrong", response.getErrorMessage());
        assertFalse(response.isSuccess());
    }
    
    @Test
    void testDefaultTimestampIsSet() {
        A2AResponse response = A2AResponse.builder()
                .responseId("resp-123")
                .status(Status.COMPLETED)
                .build();
        
        assertNotNull(response.getTimestamp());
        assertTrue(response.getTimestamp().isBefore(Instant.now().plusSeconds(1)));
    }
    
    @Test
    void testCustomTimestampOverridesDefault() {
        Instant customTime = Instant.parse("2023-01-01T12:00:00Z");
        
        A2AResponse response = A2AResponse.builder()
                .responseId("resp-123")
                .status(Status.COMPLETED)
                .timestamp(customTime)
                .build();
        
        assertEquals(customTime, response.getTimestamp());
    }
    
    @Test
    void testBuilderChaining() {
        A2AResponse response = A2AResponse.builder()
                .responseId("resp-123")
                .requestId("req-456")
                .from("agent-1")
                .to("agent-2")
                .status(Status.COMPLETED)
                .payload("test")
                .build();
        
        assertEquals("resp-123", response.getResponseId());
        assertEquals("req-456", response.getRequestId());
        assertEquals("agent-1", response.getSourceAgentId());
        assertEquals("agent-2", response.getTargetAgentId());
        assertEquals(Status.COMPLETED, response.getStatus());
        assertEquals("test", response.getPayload());
    }
}