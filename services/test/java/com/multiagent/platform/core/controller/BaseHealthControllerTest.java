package com.multiagent.platform.core.controller;

import ai.twodot.metaagent.platform.core.controller.BaseHealthController;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.test.web.servlet.MockMvc;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(BaseHealthController.class)
class BaseHealthControllerTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Test
    void testCoreHealthEndpoint() throws Exception {
        mockMvc.perform(get("/api/core/health"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$.status").value("UP"))
                .andExpect(jsonPath("$.component").value("Core Framework"))
                .andExpect(jsonPath("$.timestamp").exists());
    }
    
    @Test
    void testCoreMetricsEndpoint() throws Exception {
        mockMvc.perform(get("/api/core/metrics"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$.component").value("Core Framework"))
                .andExpect(jsonPath("$.uptime").exists())
                .andExpect(jsonPath("$.status").value("operational"));
    }
    
    @Test
    void testCoreHealthResponseStructure() throws Exception {
        String response = mockMvc.perform(get("/api/core/health"))
                .andExpect(status().isOk())
                .andReturn()
                .getResponse()
                .getContentAsString();
        
        // Parse response and verify structure
        var jsonNode = objectMapper.readTree(response);
        
        // Verify all required fields are present
        assertNotNull(jsonNode.get("status"));
        assertNotNull(jsonNode.get("component"));
        assertNotNull(jsonNode.get("timestamp"));
        
        // Verify field types
        assertTrue(jsonNode.get("status").isTextual());
        assertTrue(jsonNode.get("component").isTextual());
        assertTrue(jsonNode.get("timestamp").isNumber());
    }
    
    @Test
    void testCoreMetricsResponseStructure() throws Exception {
        String response = mockMvc.perform(get("/api/core/metrics"))
                .andExpect(status().isOk())
                .andReturn()
                .getResponse()
                .getContentAsString();
        
        // Parse response and verify structure
        var jsonNode = objectMapper.readTree(response);
        
        // Verify all required fields are present
        assertNotNull(jsonNode.get("component"));
        assertNotNull(jsonNode.get("uptime"));
        assertNotNull(jsonNode.get("status"));
        
        // Verify field types
        assertTrue(jsonNode.get("component").isTextual());
        assertTrue(jsonNode.get("uptime").isNumber());
        assertTrue(jsonNode.get("status").isTextual());
    }
    
    @Test
    void testCoreHealthTimestamp() throws Exception {
        // Make two requests with a small delay
        String response1 = mockMvc.perform(get("/api/core/health"))
                .andExpect(status().isOk())
                .andReturn()
                .getResponse()
                .getContentAsString();
        
        Thread.sleep(10); // Small delay
        
        String response2 = mockMvc.perform(get("/api/core/health"))
                .andExpect(status().isOk())
                .andReturn()
                .getResponse()
                .getContentAsString();
        
        // Parse responses
        var json1 = objectMapper.readTree(response1);
        var json2 = objectMapper.readTree(response2);
        
        // Timestamps should be different (or at least not throw error)
        assertNotEquals(json1.get("timestamp"), json2.get("timestamp"));
    }
    
    @Test
    void testCoreMetricsUptime() throws Exception {
        // Make two requests with a small delay
        String response1 = mockMvc.perform(get("/api/core/metrics"))
                .andExpect(status().isOk())
                .andReturn()
                .getResponse()
                .getContentAsString();
        
        Thread.sleep(10); // Small delay
        
        String response2 = mockMvc.perform(get("/api/core/metrics"))
                .andExpect(status().isOk())
                .andReturn()
                .getResponse()
                .getContentAsString();
        
        // Parse responses
        var json1 = objectMapper.readTree(response1);
        var json2 = objectMapper.readTree(response2);
        
        // Uptime should be different (or at least not throw error)
        assertNotEquals(json1.get("uptime"), json2.get("uptime"));
    }
    
    @Test
    void testCoreEndpointAccessibility() throws Exception {
        // Test that both endpoints are accessible
        mockMvc.perform(get("/api/core/health"))
                .andExpect(status().isOk());
        
        mockMvc.perform(get("/api/core/metrics"))
                .andExpect(status().isOk());
    }
    
    @Test
    void testCoreHealthConsistency() throws Exception {
        // Make multiple requests to ensure consistency
        for (int i = 0; i < 3; i++) {
            mockMvc.perform(get("/api/core/health"))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.status").value("UP"))
                    .andExpect(jsonPath("$.component").value("Core Framework"));
        }
    }
    
    @Test
    void testCoreMetricsConsistency() throws Exception {
        // Make multiple requests to ensure consistency
        for (int i = 0; i < 3; i++) {
            mockMvc.perform(get("/api/core/metrics"))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.component").value("Core Framework"))
                    .andExpect(jsonPath("$.status").value("operational"));
        }
    }
}