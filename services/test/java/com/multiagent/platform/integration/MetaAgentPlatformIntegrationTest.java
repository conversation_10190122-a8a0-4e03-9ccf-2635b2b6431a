package com.multiagent.platform.integration;

import ai.twodot.metaagent.platform.core.models.enums.MessageType;
import ai.twodot.metaagent.platform.core.models.enums.Priority;
import ai.twodot.metaagent.platform.core.models.enums.Status;
import ai.twodot.metaagent.platform.MetaAgentPlatformApplication;
import ai.twodot.metaagent.platform.core.models.MetricsData;
import ai.twodot.metaagent.platform.facade.communication.CommunicationFacade;
import ai.twodot.metaagent.platform.facade.monitoring.MonitoringFacade;
import ai.twodot.metaagent.platform.facade.orchestration.OrchestrationFacade;
import ai.twodot.metaagent.platform.facade.registry.AgentRegistryFacade;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.TestPropertySource;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(
    classes = MetaAgentPlatformApplication.class,
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
)
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.datasource.driver-class-name=org.h2.Driver",
    "spring.jpa.hibernate.ddl-auto=create-drop",
    "spring.flyway.enabled=false"
})
class MetaAgentPlatformIntegrationTest {
    
    @LocalServerPort
    private int port;
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Autowired
    private AgentRegistryFacade agentRegistryFacade;
    
    @Autowired
    private CommunicationFacade communicationFacade;
    
    @Autowired
    private MonitoringFacade monitoringFacade;
    
    @Autowired
    private OrchestrationFacade orchestrationFacade;
    
    @Test
    void testApplicationContextLoads() {
        // Test that application context loads successfully
        assertNotNull(agentRegistryFacade);
        assertNotNull(communicationFacade);
        assertNotNull(monitoringFacade);
        assertNotNull(orchestrationFacade);
    }
    
    @Test
    void testHealthEndpoint() {
        String url = "http://localhost:" + port + "/health";
        ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        
        Map<String, Object> body = response.getBody();
        assertNotNull(body);
        assertEquals("UP", body.get("status"));
        assertEquals("Meta-Agent Platform", body.get("platform"));
        assertEquals("1.0.0", body.get("version"));
        assertNotNull(body.get("timestamp"));
    }
    
    @Test
    void testPlatformInfoEndpoint() {
        String url = "http://localhost:" + port + "/platform/info";
        ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        
        Map<String, Object> body = response.getBody();
        assertNotNull(body);
        assertEquals("Meta-Agent Platform", body.get("platform"));
        assertEquals("1.0.0", body.get("version"));
        assertEquals("Service Facade Pattern", body.get("architecture"));
        
        @SuppressWarnings("unchecked")
        List<String> agents = (List<String>) body.get("agents");
        assertNotNull(agents);
        assertEquals(8, agents.size());
    }
    
    @Test
    void testCoreHealthEndpoint() {
        String url = "http://localhost:" + port + "/api/core/health";
        ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        
        Map<String, Object> body = response.getBody();
        assertNotNull(body);
        assertEquals("UP", body.get("status"));
        assertEquals("Core Framework", body.get("component"));
        assertNotNull(body.get("timestamp"));
    }
    
    @Test
    void testCoreMetricsEndpoint() {
        String url = "http://localhost:" + port + "/api/core/metrics";
        ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        
        Map<String, Object> body = response.getBody();
        assertNotNull(body);
        assertEquals("Core Framework", body.get("component"));
        assertEquals("operational", body.get("status"));
        assertNotNull(body.get("uptime"));
    }
    
    @Test
    void testAgentRegistryFacadeIntegration() {
        // Test service registration
        AgentRegistryFacade.ServiceInfo service = new AgentRegistryFacade.ServiceInfo(
            "test-service",
            "TestService",
            "http://localhost:8080",
            List.of("test-capability"),
            true
        );
        
        assertTrue(agentRegistryFacade.registerService(service));
        
        // Test service discovery
        List<AgentRegistryFacade.ServiceInfo> services = agentRegistryFacade.discoverServices();
        assertEquals(1, services.size());
        assertEquals("test-service", services.get(0).getServiceId());
        
        // Test service health check
        assertTrue(agentRegistryFacade.isServiceHealthy("test-service"));
        
        // Test service metrics
        MetricsData metrics = agentRegistryFacade.getServiceMetrics("test-service");
        assertNotNull(metrics);
        assertEquals("test-service", metrics.getAgentId());
        
        // Test service unregistration
        assertTrue(agentRegistryFacade.unregisterService("test-service"));
        assertTrue(agentRegistryFacade.discoverServices().isEmpty());
    }
    
    @Test
    void testCommunicationFacadeIntegration() throws Exception {
        // Test sending a message
        var response = communicationFacade.sendMessage("target-agent", 
            MessageType.REQUEST,
            "test message").get();
        
        assertNotNull(response);
        assertEquals("target-agent", response.getTargetAgentId());
        assertTrue(response.isSuccess());
        
        // Test sending a request
        var requestResponse = communicationFacade.sendRequest("target-agent", "test request").get();
        
        assertNotNull(requestResponse);
        assertEquals("target-agent", requestResponse.getTargetAgentId());
        assertTrue(requestResponse.isSuccess());
        
        // Test query
        var queryResponse = communicationFacade.query("target-agent", "test query").get();
        
        assertNotNull(queryResponse);
        assertEquals("target-agent", queryResponse.getTargetAgentId());
        assertTrue(queryResponse.isSuccess());
    }
    
    @Test
    void testMonitoringFacadeIntegration() {
        // Test overall platform health
        MonitoringFacade.PlatformHealth platformHealth = monitoringFacade.getOverallHealth();
        assertNotNull(platformHealth);
        assertTrue(platformHealth.isHealthy());
        assertEquals(8, platformHealth.getTotalAgents());
        
        // Test agent health
        MonitoringFacade.AgentHealth agentHealth = monitoringFacade.getAgentHealth("test-agent");
        assertNotNull(agentHealth);
        assertEquals("test-agent", agentHealth.getAgentId());
        assertTrue(agentHealth.isHealthy());
        
        // Test all agent health
        List<MonitoringFacade.AgentHealth> allHealth = monitoringFacade.getAllAgentHealth();
        assertNotNull(allHealth);
        assertEquals(8, allHealth.size());
        
        // Test platform metrics
        MonitoringFacade.PlatformMetrics platformMetrics = monitoringFacade.getPlatformMetrics();
        assertNotNull(platformMetrics);
        assertNotNull(platformMetrics.getMetrics());
        assertTrue(platformMetrics.getMetrics().containsKey("uptime"));
        
        // Test performance report
        MonitoringFacade.PerformanceReport report = monitoringFacade.getPerformanceReport();
        assertNotNull(report);
        assertNotNull(report.getAvgResponseTimes());
        assertNotNull(report.getRequestCounts());
        assertNotNull(report.getErrorRates());
    }
    
    @Test
    void testOrchestrationFacadeIntegration() throws Exception {
        // Test task execution
        OrchestrationFacade.Task task = new OrchestrationFacade.Task(
            "test-task",
            "TestTask",
            "test input",
            java.util.Set.of("agent1", "agent2"),
            Priority.HIGH,
            java.time.Instant.now().plusSeconds(300)
        );
        
        var taskResult = orchestrationFacade.executeTask(task).get();
        
        assertNotNull(taskResult);
        assertEquals("test-task", taskResult.getTaskId());
        assertEquals(Status.COMPLETED, taskResult.getStatus());
        
        // Test task result retrieval
        OrchestrationFacade.TaskResult retrievedResult = orchestrationFacade.getTaskResult("test-task");
        assertNotNull(retrievedResult);
        assertEquals("test-task", retrievedResult.getTaskId());
        
        // Test getting tasks by status
        List<OrchestrationFacade.TaskResult> completedTasks = orchestrationFacade.getTasksByStatus(
            Status.COMPLETED
        );
        
        assertNotNull(completedTasks);
        assertTrue(completedTasks.size() > 0);
    }
    
    @Test
    void testEndToEndWorkflow() throws Exception {
        // Test a complete workflow from service registration to task execution
        
        // 1. Register a service
        AgentRegistryFacade.ServiceInfo service = new AgentRegistryFacade.ServiceInfo(
            "workflow-service",
            "WorkflowService",
            "http://localhost:8080",
            List.of("workflow-capability"),
            true
        );
        
        assertTrue(agentRegistryFacade.registerService(service));
        
        // 2. Check service health
        assertTrue(agentRegistryFacade.isServiceHealthy("workflow-service"));
        
        // 3. Send a message to the service
        var messageResponse = communicationFacade.sendMessage("workflow-service", 
            MessageType.REQUEST,
            "workflow request").get();
        
        assertTrue(messageResponse.isSuccess());
        
        // 4. Execute a task
        OrchestrationFacade.Task task = new OrchestrationFacade.Task(
            "workflow-task",
            "WorkflowTask",
            "workflow input",
            java.util.Set.of("workflow-service"),
            Priority.HIGH,
            java.time.Instant.now().plusSeconds(300)
        );
        
        var taskResult = orchestrationFacade.executeTask(task).get();
        assertEquals(Status.COMPLETED, taskResult.getStatus());
        
        // 5. Check monitoring
        MonitoringFacade.PlatformHealth health = monitoringFacade.getOverallHealth();
        assertTrue(health.isHealthy());
        
        // 6. Clean up
        assertTrue(agentRegistryFacade.unregisterService("workflow-service"));
    }
    
    @Test
    void testActuatorEndpoints() {
        // Test actuator health endpoint
        String healthUrl = "http://localhost:" + port + "/actuator/health";
        ResponseEntity<Map> healthResponse = restTemplate.getForEntity(healthUrl, Map.class);
        
        assertEquals(HttpStatus.OK, healthResponse.getStatusCode());
        
        Map<String, Object> healthBody = healthResponse.getBody();
        assertNotNull(healthBody);
        assertEquals("UP", healthBody.get("status"));
        
        // Test actuator info endpoint
        String infoUrl = "http://localhost:" + port + "/actuator/info";
        ResponseEntity<Map> infoResponse = restTemplate.getForEntity(infoUrl, Map.class);
        
        assertEquals(HttpStatus.OK, infoResponse.getStatusCode());
    }
    
    @Test
    void testErrorHandling() {
        // Test non-existent endpoint
        String url = "http://localhost:" + port + "/non-existent";
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
        
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
    }
}