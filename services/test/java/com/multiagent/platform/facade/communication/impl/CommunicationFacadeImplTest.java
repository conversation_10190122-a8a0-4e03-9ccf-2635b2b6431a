package com.multiagent.platform.facade.communication.impl;

import ai.twodot.metaagent.platform.core.integration.a2a.models.A2AResponse;
import ai.twodot.metaagent.platform.core.models.enums.MessageType;
import ai.twodot.metaagent.platform.core.models.enums.Status;
import ai.twodot.metaagent.platform.facade.communication.CommunicationFacade;
import ai.twodot.metaagent.platform.facade.communication.impl.CommunicationFacadeImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import static org.junit.jupiter.api.Assertions.*;

class CommunicationFacadeImplTest {
    
    private CommunicationFacadeImpl communicationFacade;
    
    @BeforeEach
    void setUp() {
        communicationFacade = new CommunicationFacadeImpl();
    }
    
    @Test
    void testSendMessage() throws ExecutionException, InterruptedException {
        String targetAgentId = "agent-2";
        MessageType type = MessageType.REQUEST;
        Object payload = "test message";
        
        CompletableFuture<A2AResponse> future = communicationFacade.sendMessage(targetAgentId, type, payload);
        A2AResponse response = future.get();
        
        assertNotNull(response);
        assertEquals(targetAgentId, response.getTargetAgentId());
        assertEquals(Status.COMPLETED, response.getStatus());
        assertEquals(payload, response.getPayload());
        assertTrue(response.isSuccess());
        assertNotNull(response.getResponseId());
        assertTrue(response.getResponseId().startsWith("msg-"));
    }
    
    @Test
    void testSendRequest() throws ExecutionException, InterruptedException {
        String targetAgentId = "agent-3";
        Object request = "test request";
        
        CompletableFuture<A2AResponse> future = communicationFacade.sendRequest(targetAgentId, request);
        A2AResponse response = future.get();
        
        assertNotNull(response);
        assertEquals(targetAgentId, response.getTargetAgentId());
        assertEquals(Status.COMPLETED, response.getStatus());
        assertEquals(request, response.getPayload());
        assertTrue(response.isSuccess());
        assertNotNull(response.getResponseId());
        assertTrue(response.getResponseId().startsWith("req-"));
    }
    
    @Test
    void testSendNotification() {
        String targetAgentId = "agent-4";
        Object notification = "test notification";
        
        // Should not throw exception
        assertDoesNotThrow(() -> {
            communicationFacade.sendNotification(targetAgentId, notification);
        });
    }
    
    @Test
    void testBroadcast() {
        MessageType type = MessageType.NOTIFICATION;
        Object payload = "broadcast message";
        
        // Should not throw exception
        assertDoesNotThrow(() -> {
            communicationFacade.broadcast(type, payload);
        });
    }
    
    @Test
    void testRegisterAndUnregisterMessageHandler() {
        MessageType type = MessageType.REQUEST;
        CommunicationFacade.MessageHandler handler = message -> 
            A2AResponse.builder()
                .responseId("test-response")
                .status(Status.COMPLETED)
                .build();
        
        // Should not throw exception
        assertDoesNotThrow(() -> {
            communicationFacade.registerMessageHandler(type, handler);
            communicationFacade.unregisterMessageHandler(type);
        });
    }
    
    @Test
    void testQuery() throws ExecutionException, InterruptedException {
        String targetAgentId = "agent-5";
        String query = "test query";
        
        CompletableFuture<A2AResponse> future = communicationFacade.query(targetAgentId, query);
        A2AResponse response = future.get();
        
        assertNotNull(response);
        assertEquals(targetAgentId, response.getTargetAgentId());
        assertEquals(Status.COMPLETED, response.getStatus());
        assertEquals(query, response.getPayload());
        assertTrue(response.isSuccess());
        assertNotNull(response.getResponseId());
        assertTrue(response.getResponseId().startsWith("query-"));
    }
    
    @Test
    void testPublishEventWithoutHandler() {
        String eventType = "test-event";
        Object eventData = "test data";
        
        // Should not throw exception when no handler is registered
        assertDoesNotThrow(() -> {
            communicationFacade.publishEvent(eventType, eventData);
        });
    }
    
    @Test
    void testPublishEventWithHandler() {
        String eventType = "test-event";
        Object eventData = "test data";
        
        // Register event handler
        CommunicationFacade.EventHandler handler = new CommunicationFacade.EventHandler() {
            private String receivedEventType;
            private Object receivedEventData;
            
            @Override
            public void handleEvent(String eventType, Object eventData) {
                this.receivedEventType = eventType;
                this.receivedEventData = eventData;
            }
            
            public String getReceivedEventType() {
                return receivedEventType;
            }
            
            public Object getReceivedEventData() {
                return receivedEventData;
            }
        };
        
        communicationFacade.subscribeToEvents(eventType, handler);
        
        // Should not throw exception
        assertDoesNotThrow(() -> {
            communicationFacade.publishEvent(eventType, eventData);
        });
    }
    
    @Test
    void testSubscribeAndUnsubscribeFromEvents() {
        String eventType = "test-event";
        CommunicationFacade.EventHandler handler = (type, data) -> {
            // Test handler
        };
        
        // Should not throw exception
        assertDoesNotThrow(() -> {
            communicationFacade.subscribeToEvents(eventType, handler);
            communicationFacade.unsubscribeFromEvents(eventType);
        });
    }
    
    @Test
    void testMultipleMessageHandlers() {
        CommunicationFacade.MessageHandler handler1 = message -> 
            A2AResponse.builder()
                .responseId("handler1-response")
                .status(Status.COMPLETED)
                .build();
                
        CommunicationFacade.MessageHandler handler2 = message -> 
            A2AResponse.builder()
                .responseId("handler2-response")
                .status(Status.COMPLETED)
                .build();
        
        // Should not throw exception
        assertDoesNotThrow(() -> {
            communicationFacade.registerMessageHandler(MessageType.REQUEST, handler1);
            communicationFacade.registerMessageHandler(MessageType.NOTIFICATION, handler2);
            communicationFacade.unregisterMessageHandler(MessageType.REQUEST);
            communicationFacade.unregisterMessageHandler(MessageType.NOTIFICATION);
        });
    }
    
    @Test
    void testResponseIdGeneration() throws ExecutionException, InterruptedException {
        CompletableFuture<A2AResponse> future1 = communicationFacade.sendMessage("agent-1", MessageType.REQUEST, "test1");
        Thread.sleep(10);
        CompletableFuture<A2AResponse> future2 = communicationFacade.sendMessage("agent-2", MessageType.REQUEST, "test2");
        
        A2AResponse response1 = future1.get();
        A2AResponse response2 = future2.get();
        
        assertNotEquals(response1.getResponseId(), response2.getResponseId());
    }
}