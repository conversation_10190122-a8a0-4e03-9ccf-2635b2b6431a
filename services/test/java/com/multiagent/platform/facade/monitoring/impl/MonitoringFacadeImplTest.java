package com.multiagent.platform.facade.monitoring.impl;

import ai.twodot.metaagent.platform.core.models.MetricsData;
import ai.twodot.metaagent.platform.facade.monitoring.MonitoringFacade;
import ai.twodot.metaagent.platform.facade.monitoring.impl.MonitoringFacadeImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class MonitoringFacadeImplTest {
    
    private MonitoringFacadeImpl monitoringFacade;
    
    @BeforeEach
    void setUp() {
        monitoringFacade = new MonitoringFacadeImpl();
    }
    
    @Test
    void testGetOverallHealth() {
        MonitoringFacade.PlatformHealth health = monitoringFacade.getOverallHealth();
        
        assertNotNull(health);
        assertTrue(health.isHealthy());
        assertEquals(8, health.getTotalAgents());
        assertEquals(8, health.getHealthyAgents());
        assertEquals(0, health.getUnhealthyAgents());
        assertNotNull(health.getTimestamp());
    }
    
    @Test
    void testGetAgentHealth() {
        String agentId = "test-agent";
        MonitoringFacade.AgentHealth health = monitoringFacade.getAgentHealth(agentId);
        
        assertNotNull(health);
        assertEquals(agentId, health.getAgentId());
        assertEquals("UnifiedAgent", health.getAgentType());
        assertTrue(health.isHealthy());
        assertEquals("RUNNING", health.getStatus());
        assertNotNull(health.getLastCheckTime());
        assertNull(health.getErrorMessage());
    }
    
    @Test
    void testGetAllAgentHealth() {
        List<MonitoringFacade.AgentHealth> healthList = monitoringFacade.getAllAgentHealth();
        
        assertNotNull(healthList);
        assertEquals(8, healthList.size());
        
        // Check specific agents
        MonitoringFacade.AgentHealth discoveryHealth = healthList.stream()
            .filter(h -> h.getAgentId().equals("discovery-registry"))
            .findFirst()
            .orElse(null);
        
        assertNotNull(discoveryHealth);
        assertEquals("DiscoveryRegistry", discoveryHealth.getAgentType());
        assertTrue(discoveryHealth.isHealthy());
        assertEquals("RUNNING", discoveryHealth.getStatus());
    }
    
    @Test
    void testGetUnhealthyAgents() {
        List<MonitoringFacade.AgentHealth> unhealthy = monitoringFacade.getUnhealthyAgents();
        
        assertNotNull(unhealthy);
        assertTrue(unhealthy.isEmpty());
    }
    
    @Test
    void testGetAgentMetrics() {
        String agentId = "test-agent";
        MetricsData metrics = monitoringFacade.getAgentMetrics(agentId);
        
        assertNotNull(metrics);
        assertEquals(agentId, metrics.getAgentId());
        assertEquals("agent", metrics.getAgentType());
        assertTrue(metrics.isHealthy());
        assertEquals("UP", metrics.getMetrics().get("status"));
        assertEquals(45.2, metrics.getMetrics().get("responseTime"));
    }
    
    @Test
    void testGetAllAgentMetrics() {
        List<MetricsData> metricsList = monitoringFacade.getAllAgentMetrics();
        
        assertNotNull(metricsList);
        assertEquals(2, metricsList.size());
        
        MetricsData platformMetrics = metricsList.stream()
            .filter(m -> m.getAgentId().equals("platform"))
            .findFirst()
            .orElse(null);
        
        assertNotNull(platformMetrics);
        assertEquals("platform", platformMetrics.getAgentType());
        assertTrue(platformMetrics.isHealthy());
        assertTrue(platformMetrics.getMetrics().containsKey("uptime"));
        
        MetricsData agentMetrics = metricsList.stream()
            .filter(m -> m.getAgentId().equals("agents"))
            .findFirst()
            .orElse(null);
        
        assertNotNull(agentMetrics);
        assertEquals("collection", agentMetrics.getAgentType());
        assertTrue(agentMetrics.isHealthy());
        assertEquals(8, agentMetrics.getMetrics().get("count"));
    }
    
    @Test
    void testGetPlatformMetrics() {
        MonitoringFacade.PlatformMetrics metrics = monitoringFacade.getPlatformMetrics();
        
        assertNotNull(metrics);
        assertNotNull(metrics.getMetrics());
        assertNotNull(metrics.getTimestamp());
        
        // Check specific metrics
        assertTrue(metrics.getMetrics().containsKey("uptime"));
        assertTrue(metrics.getMetrics().containsKey("memory_usage"));
        assertTrue(metrics.getMetrics().containsKey("cpu_usage"));
        assertTrue(metrics.getMetrics().containsKey("active_agents"));
        assertTrue(metrics.getMetrics().containsKey("requests_per_second"));
        
        assertEquals(0.65, metrics.getMetrics().get("memory_usage"));
        assertEquals(0.45, metrics.getMetrics().get("cpu_usage"));
        assertEquals(8, metrics.getMetrics().get("active_agents"));
        assertEquals(120.5, metrics.getMetrics().get("requests_per_second"));
    }
    
    @Test
    void testGetActiveAlerts() {
        List<MonitoringFacade.Alert> alerts = monitoringFacade.getActiveAlerts();
        
        assertNotNull(alerts);
        assertTrue(alerts.isEmpty());
    }
    
    @Test
    void testGetAlertsForAgent() {
        String agentId = "test-agent";
        List<MonitoringFacade.Alert> alerts = monitoringFacade.getAlertsForAgent(agentId);
        
        assertNotNull(alerts);
        assertTrue(alerts.isEmpty());
    }
    
    @Test
    void testAcknowledgeAlert() {
        String alertId = "test-alert";
        
        // Should not throw exception
        assertDoesNotThrow(() -> {
            monitoringFacade.acknowledgeAlert(alertId);
        });
    }
    
    @Test
    void testGetPerformanceReport() {
        MonitoringFacade.PerformanceReport report = monitoringFacade.getPerformanceReport();
        
        assertNotNull(report);
        assertNotNull(report.getReportTime());
        assertNotNull(report.getAvgResponseTimes());
        assertNotNull(report.getRequestCounts());
        assertNotNull(report.getErrorRates());
        
        // Check specific metrics
        assertEquals(45.2, report.getAvgResponseTimes().get("discovery-registry"));
        assertEquals(1250L, report.getRequestCounts().get("discovery-registry"));
        assertEquals(0.02, report.getErrorRates().get("discovery-registry"));
        
        assertEquals(23.1, report.getAvgResponseTimes().get("security-monitor"));
        assertEquals(890L, report.getRequestCounts().get("security-monitor"));
        assertEquals(0.01, report.getErrorRates().get("security-monitor"));
    }
    
    @Test
    void testPlatformHealthProperties() {
        MonitoringFacade.PlatformHealth health = monitoringFacade.getOverallHealth();
        
        // Test all properties are accessible
        assertNotNull(health.getTimestamp());
        assertTrue(health.getTotalAgents() > 0);
        assertTrue(health.getHealthyAgents() >= 0);
        assertTrue(health.getUnhealthyAgents() >= 0);
        assertEquals(health.getTotalAgents(), health.getHealthyAgents() + health.getUnhealthyAgents());
    }
    
    @Test
    void testAgentHealthProperties() {
        MonitoringFacade.AgentHealth health = monitoringFacade.getAgentHealth("test-agent");
        
        // Test all properties are accessible
        assertNotNull(health.getAgentId());
        assertNotNull(health.getAgentType());
        assertNotNull(health.getStatus());
        assertNotNull(health.getLastCheckTime());
        // Error message can be null for healthy agents
    }
}