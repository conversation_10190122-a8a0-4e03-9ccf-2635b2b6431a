package com.multiagent.platform.facade.registry.impl;

import ai.twodot.metaagent.platform.core.models.MetricsData;
import ai.twodot.metaagent.platform.facade.registry.AgentRegistryFacade;
import ai.twodot.metaagent.platform.facade.registry.impl.AgentRegistryFacadeImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

class AgentRegistryFacadeImplTest {
    
    private AgentRegistryFacadeImpl registryFacade;
    private AgentRegistryFacade.ServiceInfo testService;
    
    @BeforeEach
    void setUp() {
        registryFacade = new AgentRegistryFacadeImpl();
        testService = new AgentRegistryFacade.ServiceInfo(
            "service-1",
            "TestService",
            "http://localhost:8080",
            List.of("capability1", "capability2"),
            true
        );
    }
    
    @Test
    void testDiscoverServicesEmpty() {
        List<AgentRegistryFacade.ServiceInfo> services = registryFacade.discoverServices();
        assertTrue(services.isEmpty());
    }
    
    @Test
    void testRegisterAndDiscoverService() {
        boolean registered = registryFacade.registerService(testService);
        assertTrue(registered);
        
        List<AgentRegistryFacade.ServiceInfo> services = registryFacade.discoverServices();
        assertEquals(1, services.size());
        assertEquals(testService.getServiceId(), services.get(0).getServiceId());
    }
    
    @Test
    void testDiscoverServiceByType() {
        registryFacade.registerService(testService);
        
        Optional<AgentRegistryFacade.ServiceInfo> found = registryFacade.discoverService("TestService");
        assertTrue(found.isPresent());
        assertEquals(testService.getServiceId(), found.get().getServiceId());
    }
    
    @Test
    void testDiscoverServiceByTypeNotFound() {
        Optional<AgentRegistryFacade.ServiceInfo> found = registryFacade.discoverService("NonExistentService");
        assertFalse(found.isPresent());
    }
    
    @Test
    void testDiscoverServicesByCapability() {
        registryFacade.registerService(testService);
        
        List<AgentRegistryFacade.ServiceInfo> services = registryFacade.discoverServicesByCapability("capability1");
        assertEquals(1, services.size());
        assertEquals(testService.getServiceId(), services.get(0).getServiceId());
    }
    
    @Test
    void testDiscoverServicesByCapabilityNotFound() {
        registryFacade.registerService(testService);
        
        List<AgentRegistryFacade.ServiceInfo> services = registryFacade.discoverServicesByCapability("nonexistent");
        assertTrue(services.isEmpty());
    }
    
    @Test
    void testUnregisterService() {
        registryFacade.registerService(testService);
        boolean unregistered = registryFacade.unregisterService(testService.getServiceId());
        assertTrue(unregistered);
        
        List<AgentRegistryFacade.ServiceInfo> services = registryFacade.discoverServices();
        assertTrue(services.isEmpty());
    }
    
    @Test
    void testUnregisterNonExistentService() {
        boolean unregistered = registryFacade.unregisterService("non-existent");
        assertFalse(unregistered);
    }
    
    @Test
    void testGetServiceInfo() {
        registryFacade.registerService(testService);
        
        Optional<AgentRegistryFacade.ServiceInfo> info = registryFacade.getServiceInfo(testService.getServiceId());
        assertTrue(info.isPresent());
        assertEquals(testService.getServiceId(), info.get().getServiceId());
    }
    
    @Test
    void testGetServiceInfoNotFound() {
        Optional<AgentRegistryFacade.ServiceInfo> info = registryFacade.getServiceInfo("non-existent");
        assertFalse(info.isPresent());
    }
    
    @Test
    void testGetHealthyServices() {
        AgentRegistryFacade.ServiceInfo unhealthyService = new AgentRegistryFacade.ServiceInfo(
            "service-2",
            "UnhealthyService",
            "http://localhost:8081",
            List.of("capability3"),
            false
        );
        
        registryFacade.registerService(testService);
        registryFacade.registerService(unhealthyService);
        
        List<AgentRegistryFacade.ServiceInfo> healthyServices = registryFacade.getHealthyServices();
        assertEquals(1, healthyServices.size());
        assertEquals(testService.getServiceId(), healthyServices.get(0).getServiceId());
    }
    
    @Test
    void testGetServicesByType() {
        AgentRegistryFacade.ServiceInfo anotherService = new AgentRegistryFacade.ServiceInfo(
            "service-2",
            "TestService",
            "http://localhost:8081",
            List.of("capability3"),
            true
        );
        
        registryFacade.registerService(testService);
        registryFacade.registerService(anotherService);
        
        List<AgentRegistryFacade.ServiceInfo> services = registryFacade.getServicesByType("TestService");
        assertEquals(2, services.size());
    }
    
    @Test
    void testIsServiceHealthy() {
        registryFacade.registerService(testService);
        
        assertTrue(registryFacade.isServiceHealthy(testService.getServiceId()));
        assertFalse(registryFacade.isServiceHealthy("non-existent"));
    }
    
    @Test
    void testGetServiceMetrics() {
        registryFacade.registerService(testService);
        
        MetricsData metrics = registryFacade.getServiceMetrics(testService.getServiceId());
        assertNotNull(metrics);
        assertEquals(testService.getServiceId(), metrics.getAgentId());
        assertEquals("service", metrics.getAgentType());
        assertTrue(metrics.isHealthy());
        assertEquals("UP", metrics.getMetrics().get("status"));
        assertEquals(45.2, metrics.getMetrics().get("responseTime"));
    }
    
    @Test
    void testServiceInfoProperties() {
        assertEquals("service-1", testService.getServiceId());
        assertEquals("TestService", testService.getServiceType());
        assertEquals("http://localhost:8080", testService.getEndpoint());
        assertEquals(List.of("capability1", "capability2"), testService.getCapabilities());
        assertTrue(testService.isHealthy());
    }
}