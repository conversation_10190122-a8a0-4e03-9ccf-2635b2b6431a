package ai.twodot.metaagent.platform.agents.resourcemanager.ai;

import ai.twodot.metaagent.platform.agents.resourcemanager.agent.ResourceManagerAgent;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Service
public class CapacityPredictionServiceImpl implements CapacityPredictionService {

    @Override
    public CompletableFuture<ResourceManagerAgent.CapacityPrediction> predictCapacity(String serviceId, int durationHours) {
        return CompletableFuture.completedFuture(new ResourceManagerAgent.CapacityPrediction(serviceId, Map.of()));
    }
}
