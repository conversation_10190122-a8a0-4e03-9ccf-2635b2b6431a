package ai.twodot.metaagent.platform.agents.resourcemanager.cloud;

import ai.twodot.metaagent.platform.agents.resourcemanager.resource.ResourceAllocation.AllocationRequest;
import ai.twodot.metaagent.platform.agents.resourcemanager.agent.ResourceManagerAgent.MultiCloudDeploymentPlan;
import ai.twodot.metaagent.platform.agents.resourcemanager.cloud.MultiCloudManager.*;

import java.util.Map;

/**
 * Cloud Provider Interface
 * 
 * Common interface for all cloud providers to enable unified resource management
 * across AWS, GCP, Azure, and Kubernetes environments.
 */
public interface CloudProviderInterface {

    /**
     * Test connection to the cloud provider
     */
    boolean testConnection();

    /**
     * Estimate deployment cost and performance for a resource allocation request
     */
    CloudDeploymentOption estimateDeployment(AllocationRequest request);

    /**
     * Allocate resources on the cloud provider
     */
    CloudAllocationResult allocateResources(AllocationRequest request, MultiCloudDeploymentPlan deploymentPlan);

    /**
     * Deallocate resources from the cloud provider
     */
    boolean deallocateResources(String allocationId);

    /**
     * Scale resources up or down
     */
    CloudAllocationResult scaleResources(String allocationId, int targetInstanceCount);

    /**
     * Get current resource utilization and metrics
     */
    Map<String, Object> getResourceMetrics(String allocationId);

    /**
     * Get provider-specific metrics and status
     */
    Map<String, Object> getProviderMetrics();

    /**
     * Get available instance types and their specifications
     */
    Map<String, InstanceTypeInfo> getAvailableInstanceTypes();

    /**
     * Get available regions and their characteristics
     */
    Map<String, RegionInfo> getAvailableRegions();

    /**
     * Validate resource allocation request for this provider
     */
    ValidationResult validateAllocationRequest(AllocationRequest request);

    /**
     * Get cost estimate for specific instance configuration
     */
    CostEstimate estimateCost(String instanceType, int instanceCount, String region);

    /**
     * Instance type information
     */
    record InstanceTypeInfo(
        String instanceType,
        double cpuCores,
        double memoryGb,
        double storageGb,
        double networkGbps,
        int gpuCount,
        String gpuType,
        double hourlyCost,
        boolean spotAvailable,
        Map<String, Object> specifications
    ) {}

    /**
     * Region information
     */
    record RegionInfo(
        String regionCode,
        String regionName,
        String location,
        boolean availabilityZoneSupport,
        int availabilityZoneCount,
        double carbonIntensity,
        double renewableEnergyPercentage,
        Map<String, Object> characteristics
    ) {}

    /**
     * Validation result
     */
    record ValidationResult(
        boolean valid,
        String errorMessage,
        Map<String, String> warnings
    ) {
        public static ValidationResult success() {
            return new ValidationResult(true, null, Map.of());
        }
        
        public static ValidationResult failure(String errorMessage) {
            return new ValidationResult(false, errorMessage, Map.of());
        }
        
        public static ValidationResult withWarnings(Map<String, String> warnings) {
            return new ValidationResult(true, null, warnings);
        }
    }

    /**
     * Cost estimate
     */
    record CostEstimate(
        double hourlyCost,
        double dailyCost,
        double monthlyCost,
        String currency,
        Map<String, Double> costBreakdown,
        double spotInstanceSavings,
        double reservedInstanceSavings
    ) {}
}