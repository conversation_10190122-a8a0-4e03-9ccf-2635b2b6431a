package ai.twodot.metaagent.platform.agents.resourcemanager.a2a;

import ai.twodot.metaagent.platform.agents.resourcemanager.a2a.A2AProtocol.*;
import ai.twodot.metaagent.platform.agents.resourcemanager.resource.ResourceAllocation.*;
import ai.twodot.metaagent.platform.agents.resourcemanager.agent.ResourceManagerAgent.*;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * A2A Workflow Coordinator
 * 
 * Orchestrates complex multi-agent workflows for resource allocation with comprehensive analysis.
 */
@Service
public class A2AWorkflowCoordinator {

    private static final Logger logger = LoggerFactory.getLogger(A2AWorkflowCoordinator.class);

    @Autowired
    private A2ACommunicationService communicationService;
    
    @Autowired
    private CostBenefitAnalyzerClient cbaClient;
    
    @Autowired
    private DeploymentRiskAssessorClient draClient;
    
    @Autowired
    private SecurityMonitoringAgentClient smaClient;

    private final AtomicLong workflowsExecuted = new AtomicLong(0);
    private final AtomicLong successfulWorkflows = new AtomicLong(0);
    private final AtomicLong failedWorkflows = new AtomicLong(0);
    private final Map<String, WorkflowExecution> activeWorkflows = new ConcurrentHashMap<>();

    /**
     * Execute comprehensive allocation workflow with all agents
     */
    public CompletableFuture<ComprehensiveAllocationResult> executeComprehensiveAllocation(
            AllocationRequest request, MultiCloudDeploymentPlan deploymentPlan) {
        
        String workflowId = UUID.randomUUID().toString();
        logger.info("Starting comprehensive allocation workflow {} for allocation: {}", 
            workflowId, request.allocationId());
        
        workflowsExecuted.incrementAndGet();
        
        WorkflowExecution execution = new WorkflowExecution(
            workflowId, request.allocationId(), WorkflowType.COMPREHENSIVE_ALLOCATION, 
            Instant.now(), WorkflowStatus.IN_PROGRESS
        );
        activeWorkflows.put(workflowId, execution);
        
        return executeWorkflowSteps(workflowId, request, deploymentPlan)
            .thenApply(result -> {
                successfulWorkflows.incrementAndGet();
                activeWorkflows.remove(workflowId);
                
                // Broadcast workflow completion event
                broadcastWorkflowEvent(workflowId, "WORKFLOW_COMPLETED", Map.of(
                    "allocation_id", request.allocationId(),
                    "workflow_type", "COMPREHENSIVE_ALLOCATION",
                    "success", true
                ));
                
                logger.info("Comprehensive allocation workflow {} completed successfully", workflowId);
                return result;
            })
            .exceptionally(throwable -> {
                failedWorkflows.incrementAndGet();
                activeWorkflows.remove(workflowId);
                
                // Broadcast workflow failure event
                broadcastWorkflowEvent(workflowId, "WORKFLOW_FAILED", Map.of(
                    "allocation_id", request.allocationId(),
                    "workflow_type", "COMPREHENSIVE_ALLOCATION",
                    "error", throwable.getMessage()
                ));
                
                logger.error("Comprehensive allocation workflow {} failed: {}", workflowId, throwable.getMessage());
                return createFallbackAllocationResult(request, deploymentPlan, throwable);
            });
    }

    /**
     * Execute risk assessment workflow
     */
    public CompletableFuture<RiskAssessmentWorkflowResult> executeRiskAssessmentWorkflow(
            AllocationRequest request, MultiCloudDeploymentPlan deploymentPlan) {
        
        String workflowId = UUID.randomUUID().toString();
        logger.info("Starting risk assessment workflow {} for allocation: {}", 
            workflowId, request.allocationId());
        
        WorkflowExecution execution = new WorkflowExecution(
            workflowId, request.allocationId(), WorkflowType.RISK_ASSESSMENT, 
            Instant.now(), WorkflowStatus.IN_PROGRESS
        );
        activeWorkflows.put(workflowId, execution);
        
        // Execute risk assessment with both DRA and SMA
        CompletableFuture<DeploymentRiskAssessorClient.DeploymentRiskAssessmentResult> draFuture = 
            draClient.assessDeploymentRisk(request, deploymentPlan);
        
        CompletableFuture<SecurityMonitoringAgentClient.SecurityComplianceResult> smaFuture = 
            smaClient.validateSecurityCompliance(request);
        
        return CompletableFuture.allOf(draFuture, smaFuture)
            .thenApply(v -> {
                DeploymentRiskAssessorClient.DeploymentRiskAssessmentResult draResult = draFuture.join();
                SecurityMonitoringAgentClient.SecurityComplianceResult smaResult = smaFuture.join();
                
                activeWorkflows.remove(workflowId);
                
                RiskAssessmentWorkflowResult result = new RiskAssessmentWorkflowResult(
                    workflowId,
                    request.allocationId(),
                    draResult,
                    smaResult,
                    calculateOverallRiskScore(draResult, smaResult),
                    generateConsolidatedRecommendations(draResult, smaResult),
                    Instant.now()
                );
                
                logger.info("Risk assessment workflow {} completed", workflowId);
                return result;
            })
            .exceptionally(throwable -> {
                activeWorkflows.remove(workflowId);
                logger.error("Risk assessment workflow {} failed: {}", workflowId, throwable.getMessage());
                
                return new RiskAssessmentWorkflowResult(
                    workflowId, request.allocationId(), null, null, 75.0, 
                    List.of("Risk assessment partially failed - proceed with caution"), Instant.now()
                );
            });
    }

    /**
     * Execute cost optimization workflow
     */
    public CompletableFuture<CostOptimizationWorkflowResult> executeCostOptimizationWorkflow(
            AllocationRequest request) {
        
        String workflowId = UUID.randomUUID().toString();
        logger.info("Starting cost optimization workflow {} for allocation: {}", 
            workflowId, request.allocationId());
        
        WorkflowExecution execution = new WorkflowExecution(
            workflowId, request.allocationId(), WorkflowType.COST_OPTIMIZATION, 
            Instant.now(), WorkflowStatus.IN_PROGRESS
        );
        activeWorkflows.put(workflowId, execution);
        
        // Execute cost-benefit analysis and budget validation
        CompletableFuture<CostBenefitAnalyzerClient.CostBenefitAnalysisResult> cbaFuture = 
            cbaClient.analyzeCostBenefit(request);
        
        CompletableFuture<CostBenefitAnalyzerClient.BudgetValidationResult> budgetFuture = 
            cbaClient.validateBudgetConstraints(request);
        
        CompletableFuture<CostBenefitAnalyzerClient.BudgetRecommendationResult> recommendationsFuture = 
            cbaClient.getBudgetRecommendations(request.serviceId(), Map.of(
                "priority", request.priority().name(),
                "environment", request.constraints().budgetLimit() != null ? 
                    request.constraints().budgetLimit().environment() : "development"
            ));
        
        return CompletableFuture.allOf(cbaFuture, budgetFuture, recommendationsFuture)
            .thenApply(v -> {
                CostBenefitAnalyzerClient.CostBenefitAnalysisResult cbaResult = cbaFuture.join();
                CostBenefitAnalyzerClient.BudgetValidationResult budgetResult = budgetFuture.join();
                CostBenefitAnalyzerClient.BudgetRecommendationResult recommendationsResult = recommendationsFuture.join();
                
                activeWorkflows.remove(workflowId);
                
                CostOptimizationWorkflowResult result = new CostOptimizationWorkflowResult(
                    workflowId,
                    request.allocationId(),
                    cbaResult,
                    budgetResult,
                    recommendationsResult,
                    calculateCostOptimizationScore(cbaResult, budgetResult, recommendationsResult),
                    Instant.now()
                );
                
                logger.info("Cost optimization workflow {} completed", workflowId);
                return result;
            })
            .exceptionally(throwable -> {
                activeWorkflows.remove(workflowId);
                logger.error("Cost optimization workflow {} failed: {}", workflowId, throwable.getMessage());
                
                return createFallbackCostOptimizationResult(workflowId, request);
            });
    }

    /**
     * Get workflow coordinator metrics
     */
    public Map<String, Object> getMetrics() {
        return Map.of(
            "workflows_executed", workflowsExecuted.get(),
            "successful_workflows", successfulWorkflows.get(),
            "failed_workflows", failedWorkflows.get(),
            "active_workflows", activeWorkflows.size(),
            "success_rate", calculateSuccessRate(),
            "workflow_types", getWorkflowTypeDistribution(),
            "average_workflow_duration", calculateAverageWorkflowDuration(),
            "last_updated", Instant.now()
        );
    }

    /**
     * Get active workflow status
     */
    public Map<String, WorkflowExecution> getActiveWorkflows() {
        return new HashMap<>(activeWorkflows);
    }

    // Private methods

    private CompletableFuture<ComprehensiveAllocationResult> executeWorkflowSteps(
            String workflowId, AllocationRequest request, MultiCloudDeploymentPlan deploymentPlan) {
        
        logger.info("Executing workflow steps for workflow: {}", workflowId);
        
        // Step 1: Cost-Benefit Analysis
        CompletableFuture<CostBenefitAnalyzerClient.CostBenefitAnalysisResult> cbaFuture = 
            cbaClient.analyzeCostBenefit(request)
                .whenComplete((result, ex) -> {
                    if (ex != null) {
                        logger.warn("CBA step failed in workflow {}: {}", workflowId, ex.getMessage());
                    } else {
                        logger.debug("CBA step completed in workflow {}", workflowId);
                    }
                });
        
        // Step 2: Risk Assessment
        CompletableFuture<DeploymentRiskAssessorClient.DeploymentRiskAssessmentResult> draFuture = 
            draClient.assessDeploymentRisk(request, deploymentPlan)
                .whenComplete((result, ex) -> {
                    if (ex != null) {
                        logger.warn("DRA step failed in workflow {}: {}", workflowId, ex.getMessage());
                    } else {
                        logger.debug("DRA step completed in workflow {}", workflowId);
                    }
                });
        
        // Step 3: Security Compliance
        CompletableFuture<SecurityMonitoringAgentClient.SecurityComplianceResult> smaFuture = 
            smaClient.validateSecurityCompliance(request)
                .whenComplete((result, ex) -> {
                    if (ex != null) {
                        logger.warn("SMA step failed in workflow {}: {}", workflowId, ex.getMessage());
                    } else {
                        logger.debug("SMA step completed in workflow {}", workflowId);
                    }
                });
        
        // Step 4: Budget Validation
        CompletableFuture<CostBenefitAnalyzerClient.BudgetValidationResult> budgetFuture = 
            cbaClient.validateBudgetConstraints(request)
                .whenComplete((result, ex) -> {
                    if (ex != null) {
                        logger.warn("Budget validation step failed in workflow {}: {}", workflowId, ex.getMessage());
                    } else {
                        logger.debug("Budget validation step completed in workflow {}", workflowId);
                    }
                });
        
        // Wait for all steps to complete
        return CompletableFuture.allOf(cbaFuture, draFuture, smaFuture, budgetFuture)
            .thenApply(v -> {
                logger.info("All workflow steps completed for workflow: {}", workflowId);
                
                ComprehensiveAllocationResult result = new ComprehensiveAllocationResult(
                    workflowId,
                    request.allocationId(),
                    request.serviceId(),
                    cbaFuture.join(),
                    draFuture.join(),
                    smaFuture.join(),
                    budgetFuture.join(),
                    deploymentPlan,
                    calculateOverallRecommendation(cbaFuture.join(), draFuture.join(), 
                        smaFuture.join(), budgetFuture.join()),
                    determineApprovalStatus(cbaFuture.join(), draFuture.join(), 
                        smaFuture.join(), budgetFuture.join()),
                    Instant.now()
                );
                
                return result;
            });
    }

    private void broadcastWorkflowEvent(String workflowId, String eventType, Map<String, Object> eventData) {
        try {
            Map<String, Object> payload = new HashMap<>(eventData);
            payload.put("workflow_id", workflowId);
            payload.put("source_agent", "RMA-004");
            payload.put("timestamp", Instant.now());
            
            communicationService.sendEvent(OperationType.valueOf(eventType), payload);
            
        } catch (Exception e) {
            logger.warn("Failed to broadcast workflow event {}: {}", eventType, e.getMessage());
        }
    }

    private double calculateOverallRiskScore(
            DeploymentRiskAssessorClient.DeploymentRiskAssessmentResult draResult,
            SecurityMonitoringAgentClient.SecurityComplianceResult smaResult) {
        
        if (draResult == null && smaResult == null) {
            return 75.0; // Default moderate risk
        }
        
        double draScore = draResult != null ? draResult.overallRiskScore() : 75.0;
        double smaScore = smaResult != null ? (100.0 - smaResult.overallComplianceScore()) : 25.0;
        
        // Weighted average: DRA 60%, SMA 40%
        return (draScore * 0.6) + (smaScore * 0.4);
    }

    private List<String> generateConsolidatedRecommendations(
            DeploymentRiskAssessorClient.DeploymentRiskAssessmentResult draResult,
            SecurityMonitoringAgentClient.SecurityComplianceResult smaResult) {
        
        List<String> recommendations = new ArrayList<>();
        
        if (draResult != null) {
            recommendations.addAll(draResult.recommendations());
        }
        
        if (smaResult != null) {
            recommendations.addAll(smaResult.securityRecommendations().stream()
                .map(rec -> rec.title() + ": " + rec.description())
                .toList());
        }
        
        // Add workflow-specific recommendations
        recommendations.add("Monitor resource utilization post-deployment");
        recommendations.add("Conduct periodic security and risk assessments");
        
        return recommendations;
    }

    private double calculateCostOptimizationScore(
            CostBenefitAnalyzerClient.CostBenefitAnalysisResult cbaResult,
            CostBenefitAnalyzerClient.BudgetValidationResult budgetResult,
            CostBenefitAnalyzerClient.BudgetRecommendationResult recommendationsResult) {
        
        double score = 70.0; // Base score
        
        if (cbaResult != null) {
            score += Math.min(30.0, cbaResult.roiPercentage() / 2); // Up to 30 points for ROI
        }
        
        if (budgetResult != null && budgetResult.withinBudget()) {
            score += 10.0; // Bonus for being within budget
        }
        
        if (recommendationsResult != null) {
            score += Math.min(10.0, recommendationsResult.potentialSavings() / 200); // Up to 10 points for savings
        }
        
        return Math.min(100.0, score);
    }

    private String calculateOverallRecommendation(
            CostBenefitAnalyzerClient.CostBenefitAnalysisResult cbaResult,
            DeploymentRiskAssessorClient.DeploymentRiskAssessmentResult draResult,
            SecurityMonitoringAgentClient.SecurityComplianceResult smaResult,
            CostBenefitAnalyzerClient.BudgetValidationResult budgetResult) {
        
        List<String> factors = new ArrayList<>();
        
        if (cbaResult != null && cbaResult.roiPercentage() > 20) {
            factors.add("positive ROI");
        }
        
        if (draResult != null && "LOW".equals(draResult.riskLevel())) {
            factors.add("low deployment risk");
        }
        
        if (smaResult != null && "COMPLIANT".equals(smaResult.complianceStatus())) {
            factors.add("security compliant");
        }
        
        if (budgetResult != null && budgetResult.withinBudget()) {
            factors.add("within budget");
        }
        
        if (factors.size() >= 3) {
            return "RECOMMENDED: " + String.join(", ", factors);
        } else if (factors.size() >= 2) {
            return "CONDITIONAL: Review concerns and proceed with monitoring";
        } else {
            return "NOT RECOMMENDED: Significant concerns identified";
        }
    }

    private String determineApprovalStatus(
            CostBenefitAnalyzerClient.CostBenefitAnalysisResult cbaResult,
            DeploymentRiskAssessorClient.DeploymentRiskAssessmentResult draResult,
            SecurityMonitoringAgentClient.SecurityComplianceResult smaResult,
            CostBenefitAnalyzerClient.BudgetValidationResult budgetResult) {
        
        // Auto-approval criteria
        boolean goodROI = cbaResult != null && cbaResult.roiPercentage() > 15;
        boolean lowRisk = draResult != null && ("LOW".equals(draResult.riskLevel()) || "MEDIUM".equals(draResult.riskLevel()));
        boolean secureCompliant = smaResult != null && smaResult.overallComplianceScore() > 80;
        boolean withinBudget = budgetResult != null && budgetResult.withinBudget();
        
        if (goodROI && lowRisk && secureCompliant && withinBudget) {
            return "AUTO_APPROVED";
        } else if ((goodROI || lowRisk) && secureCompliant && withinBudget) {
            return "APPROVED_WITH_CONDITIONS";
        } else {
            return "REQUIRES_MANUAL_REVIEW";
        }
    }

    private ComprehensiveAllocationResult createFallbackAllocationResult(
            AllocationRequest request, MultiCloudDeploymentPlan deploymentPlan, Throwable error) {
        
        return new ComprehensiveAllocationResult(
            UUID.randomUUID().toString(),
            request.allocationId(),
            request.serviceId(),
            null, null, null, null,
            deploymentPlan,
            "FALLBACK: Partial analysis completed - " + error.getMessage(),
            "REQUIRES_MANUAL_REVIEW",
            Instant.now()
        );
    }

    private CostOptimizationWorkflowResult createFallbackCostOptimizationResult(String workflowId, AllocationRequest request) {
        return new CostOptimizationWorkflowResult(
            workflowId,
            request.allocationId(),
            null, null, null,
            60.0, // Conservative score
            Instant.now()
        );
    }

    private double calculateSuccessRate() {
        long total = workflowsExecuted.get();
        if (total == 0) {
            return 1.0;
        }
        return (double) successfulWorkflows.get() / total;
    }

    private Map<String, Integer> getWorkflowTypeDistribution() {
        Map<String, Integer> distribution = new HashMap<>();
        
        for (WorkflowExecution execution : activeWorkflows.values()) {
            String type = execution.workflowType().name();
            distribution.put(type, distribution.getOrDefault(type, 0) + 1);
        }
        
        return distribution;
    }

    private double calculateAverageWorkflowDuration() {
        // Simplified calculation - in real implementation, track actual durations
        return 45.0; // 45 seconds average
    }

    // Supporting classes

    public enum WorkflowType {
        COMPREHENSIVE_ALLOCATION,
        RISK_ASSESSMENT,
        COST_OPTIMIZATION,
        SECURITY_VALIDATION
    }

    public enum WorkflowStatus {
        IN_PROGRESS,
        COMPLETED,
        FAILED,
        CANCELLED
    }

    public record WorkflowExecution(
        String workflowId,
        String allocationId,
        WorkflowType workflowType,
        Instant startedAt,
        WorkflowStatus status
    ) {}

    public record ComprehensiveAllocationResult(
        String workflowId,
        String allocationId,
        String serviceId,
        CostBenefitAnalyzerClient.CostBenefitAnalysisResult costBenefitAnalysis,
        DeploymentRiskAssessorClient.DeploymentRiskAssessmentResult riskAssessment,
        SecurityMonitoringAgentClient.SecurityComplianceResult securityCompliance,
        CostBenefitAnalyzerClient.BudgetValidationResult budgetValidation,
        MultiCloudDeploymentPlan deploymentPlan,
        String overallRecommendation,
        String approvalStatus,
        Instant completedAt
    ) {}

    public record RiskAssessmentWorkflowResult(
        String workflowId,
        String allocationId,
        DeploymentRiskAssessorClient.DeploymentRiskAssessmentResult deploymentRisk,
        SecurityMonitoringAgentClient.SecurityComplianceResult securityCompliance,
        double overallRiskScore,
        List<String> consolidatedRecommendations,
        Instant completedAt
    ) {}

    public record CostOptimizationWorkflowResult(
        String workflowId,
        String allocationId,
        CostBenefitAnalyzerClient.CostBenefitAnalysisResult costBenefitAnalysis,
        CostBenefitAnalyzerClient.BudgetValidationResult budgetValidation,
        CostBenefitAnalyzerClient.BudgetRecommendationResult budgetRecommendations,
        double optimizationScore,
        Instant completedAt
    ) {}
}