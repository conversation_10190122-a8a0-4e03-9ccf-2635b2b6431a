package ai.twodot.metaagent.platform.agents.resourcemanager.a2a;

import ai.twodot.metaagent.platform.agents.resourcemanager.a2a.A2AProtocol.*;
import ai.twodot.metaagent.platform.agents.resourcemanager.resource.ResourceAllocation.*;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Cost-Benefit Analyzer Client (CBA-001)
 * 
 * Integrates with the Cost-Benefit Analyzer agent to perform financial analysis and optimization.
 */
@Service
public class CostBenefitAnalyzerClient {

    private static final Logger logger = LoggerFactory.getLogger(CostBenefitAnalyzerClient.class);

    @Autowired
    private A2ACommunicationService communicationService;

    private final AtomicLong analysisRequests = new AtomicLong(0);
    private final AtomicLong successfulAnalyses = new AtomicLong(0);
    private final AtomicLong failedAnalyses = new AtomicLong(0);
    private final Map<String, CachedAnalysis> analysisCache = new HashMap<>();

    /**
     * Perform cost-benefit analysis for a resource allocation request
     */
    public CompletableFuture<CostBenefitAnalysisResult> analyzeCostBenefit(AllocationRequest request) {
        logger.info("Requesting cost-benefit analysis for allocation: {}", request.allocationId());
        
        analysisRequests.incrementAndGet();
        
        // Check cache first
        String cacheKey = generateCacheKey(request);
        CachedAnalysis cached = analysisCache.get(cacheKey);
        if (cached != null && isValidCache(cached)) {
            logger.debug("Using cached cost-benefit analysis for allocation: {}", request.allocationId());
            return CompletableFuture.completedFuture(cached.result());
        }
        
        // Prepare request payload
        Map<String, Object> payload = createCostBenefitPayload(request);
        
        return communicationService
            .sendRequest(OperationType.ANALYZE_COST_BENEFIT, AgentType.COST_BENEFIT_ANALYZER, 
                        payload, CostBenefitAnalysis.Response.class)
            .thenApply(response -> {
                if (response.success()) {
                    successfulAnalyses.incrementAndGet();
                    CostBenefitAnalysisResult result = convertToCostBenefitResult(response.data());
                    
                    // Cache the result
                    analysisCache.put(cacheKey, new CachedAnalysis(result, Instant.now()));
                    
                    logger.info("Cost-benefit analysis completed for allocation {}: ROI={}%", 
                        request.allocationId(), result.roiPercentage());
                    
                    return result;
                } else {
                    failedAnalyses.incrementAndGet();
                    logger.error("Cost-benefit analysis failed for allocation {}: {} - {}", 
                        request.allocationId(), response.errorCode(), response.errorMessage());
                    
                    return createFallbackAnalysis(request);
                }
            })
            .exceptionally(throwable -> {
                failedAnalyses.incrementAndGet();
                logger.error("Error during cost-benefit analysis for allocation {}: {}", 
                    request.allocationId(), throwable.getMessage(), throwable);
                
                return createFallbackAnalysis(request);
            });
    }

    /**
     * Get budget recommendations for a service
     */
    public CompletableFuture<BudgetRecommendationResult> getBudgetRecommendations(String serviceId, 
                                                                                 Map<String, Object> serviceContext) {
        logger.info("Requesting budget recommendations for service: {}", serviceId);
        
        Map<String, Object> payload = Map.of(
            "service_id", serviceId,
            "service_context", serviceContext,
            "time_horizon", "12m",
            "optimization_target", "cost_efficiency"
        );
        
        return communicationService
            .sendRequest(OperationType.GET_BUDGET_RECOMMENDATIONS, AgentType.COST_BENEFIT_ANALYZER, 
                        payload, Map.class)
            .thenApply(response -> {
                if (response.success()) {
                    return convertToBudgetRecommendations(response.data());
                } else {
                    logger.error("Budget recommendations failed for service {}: {} - {}", 
                        serviceId, response.errorCode(), response.errorMessage());
                    return createFallbackBudgetRecommendations(serviceId);
                }
            })
            .exceptionally(throwable -> {
                logger.error("Error getting budget recommendations for service {}: {}", 
                    serviceId, throwable.getMessage(), throwable);
                return createFallbackBudgetRecommendations(serviceId);
            });
    }

    /**
     * Validate budget constraints for a resource allocation
     */
    public CompletableFuture<BudgetValidationResult> validateBudgetConstraints(AllocationRequest request) {
        logger.info("Validating budget constraints for allocation: {}", request.allocationId());
        
        Map<String, Object> payload = createBudgetValidationPayload(request);
        
        return communicationService
            .sendRequest(OperationType.VALIDATE_BUDGET_CONSTRAINTS, AgentType.COST_BENEFIT_ANALYZER, 
                        payload, Map.class)
            .thenApply(response -> {
                if (response.success()) {
                    return convertToBudgetValidation(response.data());
                } else {
                    logger.error("Budget validation failed for allocation {}: {} - {}", 
                        request.allocationId(), response.errorCode(), response.errorMessage());
                    return createFallbackBudgetValidation(request);
                }
            })
            .exceptionally(throwable -> {
                logger.error("Error validating budget constraints for allocation {}: {}", 
                    request.allocationId(), throwable.getMessage(), throwable);
                return createFallbackBudgetValidation(request);
            });
    }

    /**
     * Get CBA client metrics
     */
    public Map<String, Object> getMetrics() {
        return Map.of(
            "analysis_requests", analysisRequests.get(),
            "successful_analyses", successfulAnalyses.get(),
            "failed_analyses", failedAnalyses.get(),
            "success_rate", calculateSuccessRate(),
            "cache_size", analysisCache.size(),
            "cba_agent_available", communicationService.isAgentAvailable(AgentType.COST_BENEFIT_ANALYZER),
            "last_updated", Instant.now()
        );
    }

    // Private methods

    private Map<String, Object> createCostBenefitPayload(AllocationRequest request) {
        Map<String, Object> resourceRequirements = Map.of(
            "cpu_cores", request.requirements().cpu().maxCores(),
            "memory_gb", request.requirements().memory().maxGb(),
            "storage_gb", request.requirements().storage().maxGb(),
            "network_bandwidth", request.requirements().network().maxBandwidthMbps(),
            "scaling_requirements", Map.of(
                "min_instances", request.requirements().scaling().minInstances(),
                "max_instances", request.requirements().scaling().maxInstances()
            )
        );

        Map<String, Object> budgetConstraints = new HashMap<>();
        if (request.constraints().budgetLimit() != null) {
            BudgetConstraint budget = request.constraints().budgetLimit();
            budgetConstraints.put("monthly_limit", budget.monthlyLimit());
            budgetConstraints.put("currency", budget.currency());
            budgetConstraints.put("environment", budget.environment());
        }

        Map<String, Object> businessContext = Map.of(
            "service_criticality", determineCriticality(request.priority()),
            "expected_load_pattern", "variable",
            "business_impact", "medium",
            "compliance_requirements", request.constraints().complianceRequirements()
        );

        return Map.of(
            "allocation_id", request.allocationId(),
            "service_id", request.serviceId(),
            "resource_requirements", resourceRequirements,
            "budget_constraints", budgetConstraints,
            "time_horizon", "12m",
            "business_context", businessContext
        );
    }

    private Map<String, Object> createBudgetValidationPayload(AllocationRequest request) {
        Map<String, Object> currentAllocation = Map.of(
            "allocation_id", request.allocationId(),
            "service_id", request.serviceId(),
            "estimated_monthly_cost", estimateCurrentCost(request),
            "resource_utilization", Map.of(
                "cpu", 70.0,
                "memory", 75.0,
                "storage", 60.0
            )
        );

        Map<String, Object> budgetLimits = new HashMap<>();
        if (request.constraints().budgetLimit() != null) {
            BudgetConstraint budget = request.constraints().budgetLimit();
            budgetLimits.put("monthly_limit", budget.monthlyLimit());
            budgetLimits.put("hard_limit", budget.monthlyLimit() * 1.1); // 10% buffer
            budgetLimits.put("currency", budget.currency());
        }

        return Map.of(
            "current_allocation", currentAllocation,
            "budget_limits", budgetLimits,
            "validation_criteria", Map.of(
                "check_monthly_limits", true,
                "check_utilization_efficiency", true,
                "check_cost_trends", true
            )
        );
    }

    private CostBenefitAnalysisResult convertToCostBenefitResult(CostBenefitAnalysis.Response response) {
        return new CostBenefitAnalysisResult(
            response.analysisId(),
            response.costBenefitRatio(),
            response.totalCost(),
            response.expectedBenefits(),
            response.roiPercentage(),
            response.paybackPeriodMonths(),
            response.riskAdjustedNpv(),
            response.costBreakdown(),
            response.benefitBreakdown(),
            response.recommendations(),
            response.confidenceScore(),
            Instant.now()
        );
    }

    private BudgetRecommendationResult convertToBudgetRecommendations(Map<String, Object> data) {
        return new BudgetRecommendationResult(
            (String) data.get("recommendation_id"),
            (Double) data.getOrDefault("recommended_monthly_budget", 0.0),
            (Double) data.getOrDefault("current_monthly_cost", 0.0),
            (Double) data.getOrDefault("potential_savings", 0.0),
            (List<String>) data.getOrDefault("optimization_opportunities", List.of()),
            (Map<String, Double>) data.getOrDefault("cost_breakdown", Map.of()),
            (Double) data.getOrDefault("confidence_score", 0.8),
            Instant.now()
        );
    }

    private BudgetValidationResult convertToBudgetValidation(Map<String, Object> data) {
        return new BudgetValidationResult(
            (String) data.get("validation_id"),
            (Boolean) data.getOrDefault("within_budget", true),
            (Double) data.getOrDefault("current_utilization_percentage", 0.0),
            (Double) data.getOrDefault("projected_monthly_cost", 0.0),
            (Double) data.getOrDefault("budget_variance", 0.0),
            (List<String>) data.getOrDefault("warnings", List.of()),
            (List<String>) data.getOrDefault("recommendations", List.of()),
            Instant.now()
        );
    }

    private CostBenefitAnalysisResult createFallbackAnalysis(AllocationRequest request) {
        logger.info("Creating fallback cost-benefit analysis for allocation: {}", request.allocationId());
        
        double estimatedCost = estimateCurrentCost(request);
        double estimatedBenefits = estimatedCost * 1.3; // 30% benefit assumption
        
        return new CostBenefitAnalysisResult(
            UUID.randomUUID().toString(),
            1.3, // Conservative cost-benefit ratio
            estimatedCost,
            estimatedBenefits,
            30.0, // 30% ROI
            8, // 8 months payback
            estimatedBenefits * 0.8, // Risk-adjusted NPV
            Map.of("compute", estimatedCost * 0.7, "storage", estimatedCost * 0.2, "network", estimatedCost * 0.1),
            Map.of("productivity", estimatedBenefits * 0.6, "efficiency", estimatedBenefits * 0.4),
            List.of("Consider reserved instances for cost savings", "Monitor utilization for optimization opportunities"),
            0.6, // Lower confidence for fallback
            Instant.now()
        );
    }

    private BudgetRecommendationResult createFallbackBudgetRecommendations(String serviceId) {
        double estimatedCost = 2000.0; // Default estimate
        
        return new BudgetRecommendationResult(
            UUID.randomUUID().toString(),
            estimatedCost * 1.2, // 20% buffer
            estimatedCost,
            estimatedCost * 0.15, // 15% potential savings
            List.of("Implement auto-scaling", "Consider spot instances", "Optimize storage usage"),
            Map.of("compute", estimatedCost * 0.7, "storage", estimatedCost * 0.2, "network", estimatedCost * 0.1),
            0.6,
            Instant.now()
        );
    }

    private BudgetValidationResult createFallbackBudgetValidation(AllocationRequest request) {
        double estimatedCost = estimateCurrentCost(request);
        boolean withinBudget = true;
        
        if (request.constraints().budgetLimit() != null) {
            withinBudget = estimatedCost <= request.constraints().budgetLimit().monthlyLimit();
        }
        
        return new BudgetValidationResult(
            UUID.randomUUID().toString(),
            withinBudget,
            estimatedCost / (request.constraints().budgetLimit() != null ? 
                request.constraints().budgetLimit().monthlyLimit() : estimatedCost * 1.2) * 100,
            estimatedCost,
            withinBudget ? 0.0 : estimatedCost - request.constraints().budgetLimit().monthlyLimit(),
            withinBudget ? List.of() : List.of("Estimated cost exceeds budget limit"),
            List.of("Monitor resource utilization", "Consider cost optimization strategies"),
            Instant.now()
        );
    }

    private double estimateCurrentCost(AllocationRequest request) {
        // Simple cost estimation based on resource requirements
        ResourceRequirements req = request.requirements();
        
        double cpuCost = req.cpu().maxCores() * 50; // $50 per core per month
        double memoryCost = req.memory().maxGb() * 10; // $10 per GB per month
        double storageCost = req.storage().maxGb() * 1; // $1 per GB per month
        double networkCost = req.network().maxBandwidthMbps() * 5; // $5 per Mbps per month
        
        return cpuCost + memoryCost + storageCost + networkCost;
    }

    private String determineCriticality(AllocationPriority priority) {
        return switch (priority) {
            case HIGH, CRITICAL, EMERGENCY -> "high";
            case NORMAL -> "medium";
            case LOW -> "low";
        };
    }

    private String generateCacheKey(AllocationRequest request) {
        return String.format("cba_%s_%d", request.serviceId(), 
            Objects.hash(request.requirements(), request.constraints()));
    }

    private boolean isValidCache(CachedAnalysis cached) {
        return cached.timestamp().isAfter(Instant.now().minusSeconds(3600)); // 1 hour TTL
    }

    private double calculateSuccessRate() {
        long total = analysisRequests.get();
        if (total == 0) {
            return 1.0;
        }
        return (double) successfulAnalyses.get() / total;
    }

    // Result classes

    public record CostBenefitAnalysisResult(
        String analysisId,
        double costBenefitRatio,
        double totalCost,
        double expectedBenefits,
        double roiPercentage,
        int paybackPeriodMonths,
        double riskAdjustedNpv,
        Map<String, Double> costBreakdown,
        Map<String, Double> benefitBreakdown,
        List<String> recommendations,
        double confidenceScore,
        Instant analyzedAt
    ) {}

    public record BudgetRecommendationResult(
        String recommendationId,
        double recommendedMonthlyBudget,
        double currentMonthlyCost,
        double potentialSavings,
        List<String> optimizationOpportunities,
        Map<String, Double> costBreakdown,
        double confidenceScore,
        Instant generatedAt
    ) {}

    public record BudgetValidationResult(
        String validationId,
        boolean withinBudget,
        double currentUtilizationPercentage,
        double projectedMonthlyCost,
        double budgetVariance,
        List<String> warnings,
        List<String> recommendations,
        Instant validatedAt
    ) {}

    private record CachedAnalysis(
        CostBenefitAnalysisResult result,
        Instant timestamp
    ) {}
}