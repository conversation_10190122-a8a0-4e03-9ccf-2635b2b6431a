package ai.twodot.metaagent.platform.agents.resourcemanager.a2a;

import ai.twodot.metaagent.platform.agents.resourcemanager.a2a.A2AProtocol.*;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicLong;

/**
 * A2A Communication Service
 * 
 * Handles agent-to-agent communication with message routing, retries, and circuit breaking.
 */
@Service
public class A2ACommunicationService {

    private static final Logger logger = LoggerFactory.getLogger(A2ACommunicationService.class);
    
    private final WebClient webClient;
    private final AtomicLong messagesSent = new AtomicLong(0);
    private final AtomicLong messagesReceived = new AtomicLong(0);
    private final AtomicLong messagesFailed = new AtomicLong(0);
    private final Map<String, PendingRequest> pendingRequests = new ConcurrentHashMap<>();
    private final Map<AgentType, AgentEndpoint> agentEndpoints = new ConcurrentHashMap<>();
    private final Map<AgentType, CircuitBreaker> circuitBreakers = new ConcurrentHashMap<>();

    public A2ACommunicationService() {
        this.webClient = WebClient.builder()
            .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024)) // 10MB
            .build();
        
        initializeAgentEndpoints();
        initializeCircuitBreakers();
    }

    /**
     * Send a request to another agent and wait for response
     */
    public <T> CompletableFuture<A2AResponse<T>> sendRequest(OperationType operation, 
                                                           AgentType targetAgent,
                                                           Map<String, Object> payload,
                                                           Class<T> responseType) {
        String correlationId = UUID.randomUUID().toString();
        
        A2AMessage request = A2AMessage.createRequest(
            operation, AgentType.RESOURCE_MANAGER, targetAgent, payload, correlationId
        );
        
        return sendRequest(request, responseType);
    }

    /**
     * Send a request message to another agent
     */
    public <T> CompletableFuture<A2AResponse<T>> sendRequest(A2AMessage request, Class<T> responseType) {
        logger.info("Sending A2A request {} to agent {}", request.operationType(), request.targetAgent());
        
        messagesSent.incrementAndGet();
        
        // Check circuit breaker
        CircuitBreaker circuitBreaker = circuitBreakers.get(request.targetAgent());
        if (circuitBreaker != null && !circuitBreaker.allowRequest()) {
            CompletableFuture<A2AResponse<T>> future = new CompletableFuture<>();
            future.completeExceptionally(new RuntimeException("Circuit breaker is OPEN for agent: " + request.targetAgent()));
            return future;
        }
        
        // Store pending request
        PendingRequest pendingRequest = new PendingRequest(request, responseType, Instant.now());
        pendingRequests.put(request.correlationId(), pendingRequest);
        
        // Send request
        CompletableFuture<A2AResponse<T>> future = new CompletableFuture<>();
        
        AgentEndpoint endpoint = agentEndpoints.get(request.targetAgent());
        if (endpoint == null) {
            future.completeExceptionally(new RuntimeException("No endpoint configured for agent: " + request.targetAgent()));
            return future;
        }
        
        long startTime = System.currentTimeMillis();
        
        webClient
            .post()
            .uri(endpoint.baseUrl() + "/a2a/message")
            .bodyValue(request)
            .retrieve()
            .bodyToMono(Map.class)
            .timeout(Duration.ofSeconds(30))
            .subscribe(
                response -> {
                    long processingTime = System.currentTimeMillis() - startTime;
                    handleResponse(request, response, responseType, future, processingTime);
                    if (circuitBreaker != null) {
                        circuitBreaker.recordSuccess();
                    }
                },
                error -> {
                    messagesFailed.incrementAndGet();
                    pendingRequests.remove(request.correlationId());
                    
                    if (circuitBreaker != null) {
                        circuitBreaker.recordFailure();
                    }
                    
                    logger.error("A2A request failed for {}: {}", request.targetAgent(), error.getMessage());
                    
                    if (error instanceof TimeoutException) {
                        future.completeExceptionally(new RuntimeException("Request timeout for agent: " + request.targetAgent()));
                    } else if (error instanceof WebClientResponseException) {
                        WebClientResponseException webError = (WebClientResponseException) error;
                        future.completeExceptionally(new RuntimeException("HTTP " + webError.getStatusCode() + ": " + webError.getResponseBodyAsString()));
                    } else {
                        future.completeExceptionally(error);
                    }
                }
            );
        
        return future;
    }

    /**
     * Send an event message (fire-and-forget)
     */
    public void sendEvent(OperationType operation, Map<String, Object> payload) {
        A2AMessage event = A2AMessage.createEvent(operation, AgentType.RESOURCE_MANAGER, payload);
        
        logger.info("Broadcasting A2A event: {}", operation);
        
        // Send to all connected agents
        agentEndpoints.values().forEach(endpoint -> {
            webClient
                .post()
                .uri(endpoint.baseUrl() + "/a2a/event")
                .bodyValue(event)
                .retrieve()
                .bodyToMono(Void.class)
                .subscribe(
                    result -> logger.debug("Event sent successfully to {}", endpoint.agentType()),
                    error -> logger.warn("Failed to send event to {}: {}", endpoint.agentType(), error.getMessage())
                );
        });
    }

    /**
     * Handle incoming A2A message
     */
    public A2AResponse<Map<String, Object>> handleIncomingMessage(A2AMessage message) {
        logger.info("Received A2A message {} from agent {}", message.operationType(), message.sourceAgent());
        
        messagesReceived.incrementAndGet();
        long startTime = System.currentTimeMillis();
        
        try {
            Map<String, Object> responseData = processMessage(message);
            long processingTime = System.currentTimeMillis() - startTime;
            
            return A2AResponse.success(responseData, processingTime, AgentInfo.forResourceManager());
            
        } catch (Exception e) {
            logger.error("Error processing A2A message: {}", e.getMessage(), e);
            return A2AResponse.error("PROCESSING_ERROR", e.getMessage(), AgentInfo.forResourceManager());
        }
    }

    /**
     * Get communication metrics
     */
    public Map<String, Object> getMetrics() {
        return Map.of(
            "messages_sent", messagesSent.get(),
            "messages_received", messagesReceived.get(),
            "messages_failed", messagesFailed.get(),
            "pending_requests", pendingRequests.size(),
            "circuit_breaker_status", getCircuitBreakerStatus(),
            "agent_endpoints", agentEndpoints.size(),
            "success_rate", calculateSuccessRate(),
            "last_updated", Instant.now()
        );
    }

    /**
     * Check if an agent is available
     */
    public boolean isAgentAvailable(AgentType agentType) {
        AgentEndpoint endpoint = agentEndpoints.get(agentType);
        if (endpoint == null) {
            return false;
        }
        
        CircuitBreaker circuitBreaker = circuitBreakers.get(agentType);
        return circuitBreaker == null || circuitBreaker.allowRequest();
    }

    /**
     * Test connection to all agents
     */
    public Map<AgentType, Boolean> testConnections() {
        Map<AgentType, Boolean> results = new HashMap<>();
        
        for (Map.Entry<AgentType, AgentEndpoint> entry : agentEndpoints.entrySet()) {
            AgentType agentType = entry.getKey();
            AgentEndpoint endpoint = entry.getValue();
            
            try {
                webClient
                    .get()
                    .uri(endpoint.baseUrl() + "/health")
                    .retrieve()
                    .bodyToMono(Map.class)
                    .timeout(Duration.ofSeconds(5))
                    .block();
                
                results.put(agentType, true);
                
            } catch (Exception e) {
                logger.warn("Health check failed for agent {}: {}", agentType, e.getMessage());
                results.put(agentType, false);
            }
        }
        
        return results;
    }

    // Private methods

    private void initializeAgentEndpoints() {
        // In a real implementation, these would come from configuration
        agentEndpoints.put(AgentType.COST_BENEFIT_ANALYZER, 
            new AgentEndpoint(AgentType.COST_BENEFIT_ANALYZER, "http://localhost:8081", true));
        agentEndpoints.put(AgentType.DEPLOYMENT_RISK_ASSESSOR, 
            new AgentEndpoint(AgentType.DEPLOYMENT_RISK_ASSESSOR, "http://localhost:8082", true));
        agentEndpoints.put(AgentType.SECURITY_MONITORING_AGENT, 
            new AgentEndpoint(AgentType.SECURITY_MONITORING_AGENT, "http://localhost:8083", true));
    }

    private void initializeCircuitBreakers() {
        for (AgentType agentType : AgentType.values()) {
            if (agentType != AgentType.RESOURCE_MANAGER) {
                circuitBreakers.put(agentType, new CircuitBreaker(agentType.name()));
            }
        }
    }

    @SuppressWarnings("unchecked")
    private <T> void handleResponse(A2AMessage request, Map<String, Object> response, 
                                   Class<T> responseType, CompletableFuture<A2AResponse<T>> future, 
                                   long processingTime) {
        try {
            pendingRequests.remove(request.correlationId());
            
            boolean success = (Boolean) response.getOrDefault("success", false);
            
            if (success) {
                Object data = response.get("data");
                T typedData = responseType.cast(data);
                Map<String, Object> agentInfoMap = (Map<String, Object>) response.get("agent_info");
                AgentInfo agentInfo = parseAgentInfo(agentInfoMap);
                
                A2AResponse<T> a2aResponse = A2AResponse.success(typedData, processingTime, agentInfo);
                future.complete(a2aResponse);
                
            } else {
                String errorCode = (String) response.get("error_code");
                String errorMessage = (String) response.get("error_message");
                Map<String, Object> agentInfoMap = (Map<String, Object>) response.get("agent_info");
                AgentInfo agentInfo = parseAgentInfo(agentInfoMap);
                
                A2AResponse<T> a2aResponse = A2AResponse.error(errorCode, errorMessage, agentInfo);
                future.complete(a2aResponse);
            }
            
        } catch (Exception e) {
            logger.error("Error handling A2A response: {}", e.getMessage(), e);
            future.completeExceptionally(e);
        }
    }

    private Map<String, Object> processMessage(A2AMessage message) {
        // This would contain the actual business logic for handling different message types
        // For now, return a simple acknowledgment
        
        return Map.of(
            "message_id", message.messageId(),
            "operation", message.operationType().name(),
            "processed_at", Instant.now(),
            "status", "acknowledged"
        );
    }

    private AgentInfo parseAgentInfo(Map<String, Object> agentInfoMap) {
        if (agentInfoMap == null) {
            return AgentInfo.forResourceManager();
        }
        
        return new AgentInfo(
            (String) agentInfoMap.get("agent_id"),
            AgentType.valueOf((String) agentInfoMap.get("agent_type")),
            (String) agentInfoMap.get("version"),
            (String) agentInfoMap.get("status"),
            (Map<String, Object>) agentInfoMap.get("capabilities")
        );
    }

    private Map<String, Object> getCircuitBreakerStatus() {
        Map<String, Object> status = new HashMap<>();
        
        for (Map.Entry<AgentType, CircuitBreaker> entry : circuitBreakers.entrySet()) {
            CircuitBreaker cb = entry.getValue();
            status.put(entry.getKey().name(), Map.of(
                "state", cb.getState(),
                "failure_count", cb.getFailureCount(),
                "last_failure_time", cb.getLastFailureTime()
            ));
        }
        
        return status;
    }

    private double calculateSuccessRate() {
        long total = messagesSent.get();
        if (total == 0) {
            return 1.0;
        }
        return (double) (total - messagesFailed.get()) / total;
    }

    // Helper classes

    private record AgentEndpoint(
        AgentType agentType,
        String baseUrl,
        boolean enabled
    ) {}

    private record PendingRequest(
        A2AMessage request,
        Class<?> responseType,
        Instant sentAt
    ) {}

    /**
     * Simple Circuit Breaker implementation
     */
    private static class CircuitBreaker {
        private enum State { CLOSED, OPEN, HALF_OPEN }
        
        private final String name;
        private State state = State.CLOSED;
        private int failureCount = 0;
        private Instant lastFailureTime;
        private final int failureThreshold = 5;
        private final Duration timeout = Duration.ofMinutes(1);

        public CircuitBreaker(String name) {
            this.name = name;
        }

        public boolean allowRequest() {
            if (state == State.CLOSED) {
                return true;
            } else if (state == State.OPEN) {
                if (Instant.now().isAfter(lastFailureTime.plus(timeout))) {
                    state = State.HALF_OPEN;
                    return true;
                }
                return false;
            } else { // HALF_OPEN
                return true;
            }
        }

        public void recordSuccess() {
            failureCount = 0;
            state = State.CLOSED;
        }

        public void recordFailure() {
            failureCount++;
            lastFailureTime = Instant.now();
            
            if (failureCount >= failureThreshold) {
                state = State.OPEN;
            }
        }

        public State getState() { return state; }
        public int getFailureCount() { return failureCount; }
        public Instant getLastFailureTime() { return lastFailureTime; }
    }
}