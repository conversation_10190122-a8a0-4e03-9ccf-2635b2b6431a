package ai.twodot.metaagent.platform.agents.securitymonitor.integration;

import ai.twodot.metaagent.platform.agents.securitymonitor.security.SecurityEvent;
import ai.twodot.metaagent.platform.agents.securitymonitor.security.SecurityEventType;
import ai.twodot.metaagent.platform.agents.securitymonitor.security.SecuritySeverity;
import ai.twodot.metaagent.platform.agents.securitymonitor.security.SecurityEventSource;
import ai.twodot.metaagent.platform.agents.securitymonitor.security.SecurityEntityType;
import ai.twodot.metaagent.platform.agents.securitymonitor.security.ThreatAnalysisResult;
import ai.twodot.metaagent.platform.core.communication.AgentCommunicationService;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Agent Integration Service
 * 
 * Coordinates all cross-agent communication for the Security Monitor Agent
 * through standardized A2A protocols via the Communication Broker Agent.
 */
@Service
public class SecurityAgentIntegrationService {

    private static final Logger logger = LoggerFactory.getLogger(SecurityAgentIntegrationService.class);

    @Autowired
    private AgentCommunicationService communicationService;

    @Autowired
    private SecurityEventStreamer eventStreamer;

    @Value("${sma.agent.id:SMA-003}")
    private String agentId;

    @Value("${sma.integration.auto-register:true}")
    private boolean autoRegister;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final AtomicBoolean initialized = new AtomicBoolean(false);
    private final AtomicLong messagesProcessed = new AtomicLong(0);
    private final AtomicLong integrationErrors = new AtomicLong(0);

    @PostConstruct
    public void initialize() {
        logger.info("Initializing Agent Integration Service...");
        
        if (autoRegister) {
            // Register services via facade
            communicationService.registerService(
                "security-monitor-main", 
                "SecurityMonitor", 
                "http://localhost:8080/security", 
                Map.of("version", "1.0.0", "capabilities", List.of("threat-detection", "incident-response"))
            ).thenAccept(status -> {
                if ("REGISTERED".equals(status)) {
                    logger.info("Successfully registered services with platform");
                    initialized.set(true);
                } else {
                    logger.error("Failed to register services with platform: {}", status);
                }
                });
        }
        
        logger.info("Agent Integration Service initialized");
    }

    /**
     * Process incoming security threat from external agent
     */
    public CompletableFuture<A2AMessageResponse> processIncomingThreatAlert(A2AMessage message) {
        logger.info("Processing incoming threat alert from agent: {}", message.fromAgentId());
        messagesProcessed.incrementAndGet();
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                ThreatAlertMessage threatAlert = objectMapper.convertValue(message.payload(), ThreatAlertMessage.class);
                
                // Create security event from incoming threat
                SecurityEvent incomingEvent = SecurityEvent.createDetailed(
                    SecurityEventType.ACTIVE_ATTACK,
                    SecuritySeverity.valueOf(threatAlert.severity().toUpperCase()),
                    SecurityEventSource.THREAT_INTELLIGENCE,
                    threatAlert.eventId(),
                    SecurityEntityType.UNKNOWN,
                    threatAlert.description(),
                    null, null, null, null, null,
                    Map.of(
                        "source_agent", message.fromAgentId(),
                        "original_event_id", threatAlert.eventId(),
                        "threat_indicators", threatAlert.indicators(),
                        "confidence", threatAlert.confidence()
                    ),
                    null
                );

                // Stream the external threat event
                eventStreamer.streamSecurityEvent(incomingEvent, null);

                logger.info("Processed incoming threat alert: {} from {}", threatAlert.threatType(), message.fromAgentId());
                
                return A2AMessageResponse.success(
                    "Threat alert processed successfully",
                    Map.of(
                        "processed_event_id", incomingEvent.eventId(),
                        "status", "acknowledged",
                        "action_taken", "event_created_and_streamed"
                    )
                );

            } catch (Exception e) {
                integrationErrors.incrementAndGet();
                logger.error("Error processing incoming threat alert: {}", e.getMessage(), e);
                return A2AMessageResponse.error("Failed to process threat alert: " + e.getMessage());
            }
        });
    }

    /**
     * Process service discovery request from other agents
     */
    public CompletableFuture<A2AMessageResponse> processServiceDiscoveryRequest(A2AMessage message) {
        logger.debug("Processing service discovery request from agent: {}", message.fromAgentId());
        messagesProcessed.incrementAndGet();
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                ServiceDiscoveryRequest request = objectMapper.convertValue(message.payload(), ServiceDiscoveryRequest.class);
                
                // Check if we can provide the requested capabilities
                List<String> ourCapabilities = List.of(
                    "threat_detection", "behavioral_analysis", "incident_response", 
                    "security_monitoring", "policy_enforcement", "anomaly_detection"
                );
                
                List<String> matchingCapabilities = request.capabilities().stream()
                    .filter(ourCapabilities::contains)
                    .toList();

                if (!matchingCapabilities.isEmpty()) {
                    // We can provide some of the requested capabilities
                    ServiceDiscoveryResponse response = new ServiceDiscoveryResponse(
                        agentId,
                        "Security Monitor Agent",
                        matchingCapabilities,
                        List.of(
                            new ServiceEndpoint("http", "localhost", 8083, "/api/v1/security"),
                            new ServiceEndpoint("a2a", "via-cba", 0, "/a2a/security")
                        ),
                        Map.of(
                            "match_score", (double) matchingCapabilities.size() / request.capabilities().size(),
                            "availability", "high",
                            "response_time", "< 100ms",
                            "intelligence_level", "HIGH"
                        )
                    );

                    logger.debug("Responding to service discovery with {} matching capabilities", matchingCapabilities.size());
                    return A2AMessageResponse.success("Service capabilities provided", response);
                } else {
                    logger.debug("No matching capabilities for service discovery request");
                    return A2AMessageResponse.success("No matching capabilities", Map.of("match_score", 0.0));
                }

            } catch (Exception e) {
                integrationErrors.incrementAndGet();
                logger.error("Error processing service discovery request: {}", e.getMessage(), e);
                return A2AMessageResponse.error("Failed to process service discovery: " + e.getMessage());
            }
        });
    }

    /**
     * Process intelligence sharing request from other agents
     */
    public CompletableFuture<A2AMessageResponse> processIntelligenceShare(A2AMessage message) {
        logger.info("Processing intelligence sharing from agent: {}", message.fromAgentId());
        messagesProcessed.incrementAndGet();
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                IntelligenceShareMessage intel = objectMapper.convertValue(message.payload(), IntelligenceShareMessage.class);
                
                // Store and process the shared intelligence
                Map<String, Object> processedIntel = Map.of(
                    "source_agent", message.fromAgentId(),
                    "intelligence_type", intel.intelligenceType(),
                    "data", intel.data(),
                    "confidence", intel.confidence(),
                    "processed_at", Instant.now().toString()
                );

                // Stream the intelligence update
                eventStreamer.streamThreatIntelligence(processedIntel);

                logger.info("Processed intelligence sharing: {} from {}", intel.intelligenceType(), message.fromAgentId());
                
                return A2AMessageResponse.success(
                    "Intelligence shared successfully",
                    Map.of(
                        "intelligence_id", intel.intelligenceId(),
                        "status", "processed",
                        "action_taken", "intelligence_integrated_and_distributed"
                    )
                );

            } catch (Exception e) {
                integrationErrors.incrementAndGet();
                logger.error("Error processing intelligence share: {}", e.getMessage(), e);
                return A2AMessageResponse.error("Failed to process intelligence share: " + e.getMessage());
            }
        });
    }

    /**
     * Request security assistance from other agents
     */
    public CompletableFuture<A2AMessageResponse> requestSecurityAssistance(SecurityEvent event, String assistanceType) {
        logger.info("Requesting security assistance for event: {}", event.eventId());
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                SecurityAssistanceRequest request = new SecurityAssistanceRequest(
                    UUID.randomUUID().toString(),
                    assistanceType,
                    event,
                    Map.of(
                        "urgency", event.severity().name(),
                        "requester", agentId,
                        "assistance_context", "threat_analysis"
                    ),
                    Instant.now()
                );

                // Send assistance request via facade
                CompletableFuture<Boolean> response = 
                    communicationService.sendMessage("platform-coordinator", "security_assistance_request", 
                        Map.of("request", request));

                return response.thenApply(result -> {
                    if (result) {
                        logger.info("Security assistance request sent successfully");
                        return A2AMessageResponse.success("Assistance request sent", Map.of());
                    } else {
                        logger.error("Failed to send assistance request");
                        return A2AMessageResponse.error("Failed to send assistance request");
                    }
                }).join();

            } catch (Exception e) {
                integrationErrors.incrementAndGet();
                logger.error("Error requesting security assistance: {}", e.getMessage(), e);
                return A2AMessageResponse.error("Failed to request assistance: " + e.getMessage());
            }
        });
    }

    /**
     * Coordinate security incident response with other agents
     */
    public CompletableFuture<A2AMessageResponse> coordinateIncidentResponse(SecurityEvent event, ThreatAnalysisResult analysis) {
        logger.info("Coordinating incident response for event: {}", event.eventId());
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                IncidentCoordinationMessage incident = new IncidentCoordinationMessage(
                    UUID.randomUUID().toString(),
                    event.eventId(),
                    analysis.threatType(),
                    analysis.riskLevel(),
                    List.of("isolate_entity", "analyze_logs", "update_policies"),
                    Map.of(
                        "incident_commander", agentId,
                        "severity", event.severity().name(),
                        "confidence", analysis.confidenceScore(),
                        "estimated_impact", determineImpactLevel(event, analysis)
                    ),
                    Instant.now()
                );

                // Send incident coordination message via facade
                CompletableFuture<Boolean> response = 
                    communicationService.sendMessage("platform-coordinator", "incident_coordination", 
                        Map.of("incident", incident));

                return response.thenApply(result -> {
                    if (result) {
                        logger.info("Incident coordination message sent successfully");
                        return A2AMessageResponse.success("Incident coordination initiated", Map.of());
                    } else {
                        logger.error("Failed to send incident coordination");
                        return A2AMessageResponse.error("Failed to coordinate incident");
                    }
                }).join();

            } catch (Exception e) {
                integrationErrors.incrementAndGet();
                logger.error("Error coordinating incident response: {}", e.getMessage(), e);
                return A2AMessageResponse.error("Failed to coordinate incident: " + e.getMessage());
            }
        });
    }

    /**
     * Send heartbeat and status to other agents
     */
    public CompletableFuture<A2AMessageResponse> sendAgentHeartbeat() {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // Send heartbeat via facade
                CompletableFuture<Boolean> response = communicationService.checkAgentHealth("platform-coordinator");
                
                return response.thenApply(result -> {
                    if (result) {
                        logger.debug("Agent heartbeat sent successfully");
                        return A2AMessageResponse.success("Heartbeat sent", null);
                    } else {
                        logger.warn("Failed to send heartbeat");
                        return A2AMessageResponse.error("Failed to send heartbeat");
                    }
                }).join();

            } catch (Exception e) {
                integrationErrors.incrementAndGet();
                logger.error("Error sending agent heartbeat: {}", e.getMessage(), e);
                return A2AMessageResponse.error("Failed to send heartbeat: " + e.getMessage());
            }
        });
    }

    /**
     * Determine impact level based on event and analysis
     */
    private String determineImpactLevel(SecurityEvent event, ThreatAnalysisResult analysis) {
        // Logic to determine impact level
        if (event.severity() == SecuritySeverity.CRITICAL || analysis.confidenceScore() > 0.9) {
            return "HIGH";
        } else if (event.severity() == SecuritySeverity.HIGH || analysis.confidenceScore() > 0.7) {
            return "MEDIUM";
        } else {
            return "LOW";
        }
    }

    /**
     * Get integration metrics
     */
    public IntegrationMetrics getMetrics() {
        return new IntegrationMetrics(
            messagesProcessed.get(),
            integrationErrors.get(),
            initialized.get(),
            true, // Platform connected via facade
            initialized.get(), // Services registered via facade
            eventStreamer.getMetrics().isRunning()
        );
    }

    // Message Protocol Models

    /**
     * A2A message structure
     */
    public record A2AMessage(
        @JsonProperty("id") String id,
        @JsonProperty("from_agent_id") String fromAgentId,
        @JsonProperty("to_agent_id") String toAgentId,
        @JsonProperty("subject") String subject,
        @JsonProperty("type") String type,
        @JsonProperty("priority") String priority,
        @JsonProperty("payload") Object payload,
        @JsonProperty("timestamp") Instant timestamp,
        @JsonProperty("metadata") Map<String, Object> metadata
    ) {}

    /**
     * A2A message response
     */
    public record A2AMessageResponse(
        @JsonProperty("success") boolean success,
        @JsonProperty("message") String message,
        @JsonProperty("data") Object data,
        @JsonProperty("error") String error,
        @JsonProperty("timestamp") Instant timestamp
    ) {
        public static A2AMessageResponse success(String message, Object data) {
            return new A2AMessageResponse(true, message, data, null, Instant.now());
        }

        public static A2AMessageResponse error(String error) {
            return new A2AMessageResponse(false, null, null, error, Instant.now());
        }
    }

    /**
     * Threat alert message protocol
     */
    public record ThreatAlertMessage(
        @JsonProperty("event_id") String eventId,
        @JsonProperty("threat_type") String threatType,
        @JsonProperty("severity") String severity,
        @JsonProperty("confidence") double confidence,
        @JsonProperty("description") String description,
        @JsonProperty("indicators") List<String> indicators,
        @JsonProperty("timestamp") Instant timestamp
    ) {}

    /**
     * Service discovery request protocol
     */
    public record ServiceDiscoveryRequest(
        @JsonProperty("capabilities") List<String> capabilities,
        @JsonProperty("requirements") Map<String, Object> requirements,
        @JsonProperty("context") String context
    ) {}

    /**
     * Service discovery response protocol
     */
    public record ServiceDiscoveryResponse(
        @JsonProperty("agent_id") String agentId,
        @JsonProperty("agent_name") String agentName,
        @JsonProperty("capabilities") List<String> capabilities,
        @JsonProperty("endpoints") List<ServiceEndpoint> endpoints,
        @JsonProperty("metadata") Map<String, Object> metadata
    ) {}

    /**
     * Service endpoint definition
     */
    public record ServiceEndpoint(
        @JsonProperty("protocol") String protocol,
        @JsonProperty("host") String host,
        @JsonProperty("port") int port,
        @JsonProperty("path") String path
    ) {}

    /**
     * Intelligence sharing message protocol
     */
    public record IntelligenceShareMessage(
        @JsonProperty("intelligence_id") String intelligenceId,
        @JsonProperty("intelligence_type") String intelligenceType,
        @JsonProperty("data") Map<String, Object> data,
        @JsonProperty("confidence") double confidence,
        @JsonProperty("source") String source,
        @JsonProperty("timestamp") Instant timestamp
    ) {}

    /**
     * Security assistance request protocol
     */
    public record SecurityAssistanceRequest(
        @JsonProperty("request_id") String requestId,
        @JsonProperty("assistance_type") String assistanceType,
        @JsonProperty("security_event") SecurityEvent securityEvent,
        @JsonProperty("context") Map<String, Object> context,
        @JsonProperty("timestamp") Instant timestamp
    ) {}

    /**
     * Incident coordination message protocol
     */
    public record IncidentCoordinationMessage(
        @JsonProperty("incident_id") String incidentId,
        @JsonProperty("event_id") String eventId,
        @JsonProperty("threat_type") String threatType,
        @JsonProperty("risk_level") String riskLevel,
        @JsonProperty("recommended_actions") List<String> recommendedActions,
        @JsonProperty("coordination_data") Map<String, Object> coordinationData,
        @JsonProperty("timestamp") Instant timestamp
    ) {}

    /**
     * Integration metrics
     */
    public record IntegrationMetrics(
        long messagesProcessed,
        long integrationErrors,
        boolean initialized,
        boolean cbaConnected,
        boolean draRegistered,
        boolean streamingActive
    ) {}
}