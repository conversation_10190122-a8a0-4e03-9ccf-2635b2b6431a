package ai.twodot.metaagent.platform.agents.securitymonitor.intelligence;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class ThreatIntelligenceServiceImpl implements ThreatIntelligenceService {
    private static final Logger logger = LoggerFactory.getLogger(ThreatIntelligenceServiceImpl.class);

    @Override
    public String getStatus() { return "HEALTHY"; }

    @Override
    public void initializeThreatFeeds() {
        logger.info("Initializing threat intelligence feeds (stub).");
    }
}