package ai.twodot.metaagent.platform.agents.securitymonitor.security;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.DecimalMax;

import java.time.Instant;
import java.util.Map;

/**
 * Threat Analysis Result - Result of AI-powered threat detection analysis
 * 
 * Contains comprehensive information about the threat analysis performed
 * on a security event, including detection results, confidence scores,
 * and recommended actions.
 */
public record ThreatAnalysisResult(
    
    @JsonProperty("eventId")
    @NotBlank
    String eventId,
    
    @JsonProperty("threatDetected")
    @NotNull
    Boolean threatDetected,
    
    @JsonProperty("threatType")
    @NotBlank
    String threatType,
    
    @JsonProperty("confidenceScore")
    @DecimalMin("0.0")
    @DecimalMax("1.0")
    Double confidenceScore,
    
    @JsonProperty("riskLevel")
    @NotBlank
    String riskLevel,
    
    @JsonProperty("analysisMethod")
    @NotBlank
    String analysisMethod,
    
    @JsonProperty("details")
    @NotNull
    Map<String, Object> details,
    
    @JsonProperty("requiresBehavioralAnalysis")
    @NotNull
    Boolean requiresBehavioralAnalysis,
    
    @JsonProperty("timestamp")
    @NotNull
    Instant timestamp,
    
    @JsonProperty("processingTimeMs")
    @DecimalMin("0.0")
    Long processingTimeMs
    
) {
    
    /**
     * Check if the threat is high priority (requires immediate attention)
     */
    public boolean isHighPriorityThreat() {
        return threatDetected && 
               (confidenceScore > 0.8 || 
                "CRITICAL".equals(riskLevel) || 
                "HIGH".equals(riskLevel));
    }
    
    /**
     * Check if the threat requires immediate automated response
     */
    public boolean requiresImmediateResponse() {
        return threatDetected && 
               confidenceScore > 0.9 && 
               "CRITICAL".equals(riskLevel);
    }
    
    /**
     * Check if this is a confirmed threat (high confidence)
     */
    public boolean isConfirmedThreat() {
        return threatDetected && confidenceScore > 0.8;
    }
    
    /**
     * Check if this threat should trigger policy updates
     */
    public boolean shouldTriggerPolicyUpdate() {
        return threatDetected && 
               confidenceScore > 0.7 && 
               ("CRITICAL".equals(riskLevel) || "HIGH".equals(riskLevel));
    }
    
    /**
     * Get risk score as integer (0-100)
     */
    public int getRiskScore() {
        return (int) Math.round(confidenceScore * 100);
    }
    
    /**
     * Get detailed analysis summary
     */
    public String getAnalysisSummary() {
        return String.format(
            "Threat Analysis [Event: %s, Detected: %s, Type: %s, Confidence: %.2f, Risk: %s, Method: %s]",
            eventId, threatDetected, threatType, confidenceScore, riskLevel, analysisMethod
        );
    }
    
    /**
     * Get analysis detail by key
     */
    @SuppressWarnings("unchecked")
    public <T> T getDetail(String key, Class<T> type) {
        Object value = details.get(key);
        if (value != null && type.isInstance(value)) {
            return (T) value;
        }
        return null;
    }
    
    /**
     * Get analysis detail with default value
     */
    public <T> T getDetail(String key, Class<T> type, T defaultValue) {
        T value = getDetail(key, type);
        return value != null ? value : defaultValue;
    }
    
    /**
     * Check if analysis has specific detail
     */
    public boolean hasDetail(String key) {
        return details.containsKey(key);
    }
    
    /**
     * Builder pattern for creating ThreatAnalysisResult
     */
    public static Builder builder() {
        return new Builder();
    }
    
    public static class Builder {
        private String eventId;
        private Boolean threatDetected;
        private String threatType;
        private Double confidenceScore;
        private String riskLevel;
        private String analysisMethod;
        private Map<String, Object> details;
        private Boolean requiresBehavioralAnalysis;
        private Instant timestamp;
        private Long processingTimeMs;
        
        public Builder eventId(String eventId) {
            this.eventId = eventId;
            return this;
        }
        
        public Builder threatDetected(Boolean threatDetected) {
            this.threatDetected = threatDetected;
            return this;
        }
        
        public Builder threatType(String threatType) {
            this.threatType = threatType;
            return this;
        }
        
        public Builder confidenceScore(Double confidenceScore) {
            this.confidenceScore = confidenceScore;
            return this;
        }
        
        public Builder riskLevel(String riskLevel) {
            this.riskLevel = riskLevel;
            return this;
        }
        
        public Builder analysisMethod(String analysisMethod) {
            this.analysisMethod = analysisMethod;
            return this;
        }
        
        public Builder details(Map<String, Object> details) {
            this.details = details;
            return this;
        }
        
        public Builder requiresBehavioralAnalysis(Boolean requiresBehavioralAnalysis) {
            this.requiresBehavioralAnalysis = requiresBehavioralAnalysis;
            return this;
        }
        
        public Builder timestamp(Instant timestamp) {
            this.timestamp = timestamp;
            return this;
        }
        
        public Builder processingTimeMs(Long processingTimeMs) {
            this.processingTimeMs = processingTimeMs;
            return this;
        }
        
        public ThreatAnalysisResult build() {
            return new ThreatAnalysisResult(
                eventId,
                threatDetected,
                threatType,
                confidenceScore,
                riskLevel,
                analysisMethod,
                details != null ? details : Map.of(),
                requiresBehavioralAnalysis,
                timestamp != null ? timestamp : Instant.now(),
                processingTimeMs
            );
        }
    }
    
    // Backward compatibility methods
    public boolean isThreatDetected() { return threatDetected; }
    public int riskScore() { return getRiskScore(); }
}
