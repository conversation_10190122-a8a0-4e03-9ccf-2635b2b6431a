package ai.twodot.metaagent.platform.agents.securitymonitor.security;

import java.time.Instant;
import java.util.Map;

/**
 * Security Metrics - Metrics for security monitoring
 */
public record SecurityMetrics(
    long totalEvents,
    long threatsDetected,
    long falsePositives,
    double detectionRate,
    double falsePositiveRate,
    long responseTime,
    Map<SecurityEventType, Long> eventsByType,
    Map<ThreatType, Long> threatsByType,
    Map<SecuritySeverity, Long> eventsBySeverity,
    Instant generatedAt
) {
    public static SecurityMetrics empty() {
        return new SecurityMetrics(
            0, 0, 0, 0.0, 0.0, 0,
            Map.of(), Map.of(), Map.of(),
            Instant.now()
        );
    }
}