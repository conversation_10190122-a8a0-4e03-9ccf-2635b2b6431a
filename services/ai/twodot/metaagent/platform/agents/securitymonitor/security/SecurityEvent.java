package ai.twodot.metaagent.platform.agents.securitymonitor.security;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;

import java.time.Instant;
import java.util.Map;
import java.util.UUID;

/**
 * Security Event Record - Core security event data structure
 * 
 * Represents a security-related event in the system that needs to be
 * analyzed for potential threats and security implications.
 */
public record SecurityEvent(
    
    @JsonProperty("eventId")
    @NotNull
    String eventId,
    
    @JsonProperty("eventType")
    @NotNull
    SecurityEventType eventType,
    
    @JsonProperty("severity")
    @NotNull
    SecuritySeverity severity,
    
    @JsonProperty("source")
    @NotNull
    SecurityEventSource source,
    
    @JsonProperty("entityId")
    @NotBlank
    String entityId,
    
    @JsonProperty("entityType")
    @NotNull
    SecurityEntityType entityType,
    
    @JsonProperty("timestamp")
    @NotNull
    Instant timestamp,
    
    @JsonProperty("description")
    @NotBlank
    String description,
    
    @JsonProperty("sourceIP")
    String sourceIP,
    
    @JsonProperty("targetIP")
    String targetIP,
    
    @JsonProperty("userAgent")
    String userAgent,
    
    @JsonProperty("sessionId")
    String sessionId,
    
    @JsonProperty("location")
    SecurityLocation location,
    
    @JsonProperty("metadata")
    Map<String, Object> metadata,
    
    @JsonProperty("rawData")
    String rawData,
    
    @JsonProperty("correlationId")
    String correlationId
    
) {
    
    /**
     * Create a new SecurityEvent with generated ID
     */
    public static SecurityEvent create(
            SecurityEventType eventType,
            SecuritySeverity severity,
            SecurityEventSource source,
            String entityId,
            SecurityEntityType entityType,
            String description) {
        
        return new SecurityEvent(
            UUID.randomUUID().toString(),
            eventType,
            severity,
            source,
            entityId,
            entityType,
            Instant.now(),
            description,
            null, null, null, null, null,
            Map.of(),
            null,
            null
        );
    }
    
    /**
     * Create a SecurityEvent with full details
     */
    public static SecurityEvent createDetailed(
            SecurityEventType eventType,
            SecuritySeverity severity,
            SecurityEventSource source,
            String entityId,
            SecurityEntityType entityType,
            String description,
            String sourceIP,
            String targetIP,
            String userAgent,
            String sessionId,
            SecurityLocation location,
            Map<String, Object> metadata,
            String rawData) {
        
        return new SecurityEvent(
            UUID.randomUUID().toString(),
            eventType,
            severity,
            source,
            entityId,
            entityType,
            Instant.now(),
            description,
            sourceIP,
            targetIP,
            userAgent,
            sessionId,
            location,
            metadata != null ? metadata : Map.of(),
            rawData,
            UUID.randomUUID().toString()
        );
    }
    
    /**
     * Create a copy with updated correlation ID
     */
    public SecurityEvent withCorrelationId(String correlationId) {
        return new SecurityEvent(
            eventId, eventType, severity, source, entityId, entityType,
            timestamp, description, sourceIP, targetIP, userAgent,
            sessionId, location, metadata, rawData, correlationId
        );
    }
    
    /**
     * Check if this event is a high-priority security event
     */
    public boolean isHighPriority() {
        return severity == SecuritySeverity.CRITICAL || 
               severity == SecuritySeverity.HIGH ||
               eventType.isHighRisk();
    }
    
    /**
     * Check if this event requires immediate response
     */
    public boolean requiresImmediateResponse() {
        return severity == SecuritySeverity.CRITICAL ||
               eventType == SecurityEventType.ACTIVE_ATTACK ||
               eventType == SecurityEventType.DATA_BREACH ||
               eventType == SecurityEventType.PRIVILEGE_ESCALATION;
    }
    
    /**
     * Get event age in milliseconds
     */
    public long getAgeMillis() {
        return Instant.now().toEpochMilli() - timestamp.toEpochMilli();
    }
    
    /**
     * Check if event is stale (older than threshold)
     */
    public boolean isStale(long thresholdMillis) {
        return getAgeMillis() > thresholdMillis;
    }
    
    /**
     * Get metadata value with type safety
     */
    @SuppressWarnings("unchecked")
    public <T> T getMetadata(String key, Class<T> type) {
        Object value = metadata.get(key);
        if (value != null && type.isInstance(value)) {
            return (T) value;
        }
        return null;
    }
    
    /**
     * Get metadata value with default
     */
    @SuppressWarnings("unchecked")
    public <T> T getMetadata(String key, Class<T> type, T defaultValue) {
        T value = getMetadata(key, type);
        return value != null ? value : defaultValue;
    }
    
    /**
     * Check if event has specific metadata key
     */
    public boolean hasMetadata(String key) {
        return metadata.containsKey(key);
    }
    
    /**
     * Create event summary for logging
     */
    public String toSummary() {
        return String.format(
            "SecurityEvent[id=%s, type=%s, severity=%s, entity=%s, source=%s, time=%s]",
            eventId, eventType, severity, entityId, source, timestamp
        );
    }
    
    /**
     * Validate event data integrity
     */
    public boolean isValid() {
        return eventId != null && !eventId.isBlank() &&
               eventType != null &&
               severity != null &&
               source != null &&
               entityId != null && !entityId.isBlank() &&
               entityType != null &&
               timestamp != null &&
               description != null && !description.isBlank();
    }
    
    // Backward compatibility methods for getter-style access
    public String getEventId() { return eventId; }
    public String getEntityId() { return entityId; }
    public String getSummary() { return toSummary(); }
    public SecurityEventType getEventType() { return eventType; }
    public SecuritySeverity getSeverity() { return severity; }
    public SecurityEventSource getSource() { return source; }
}

