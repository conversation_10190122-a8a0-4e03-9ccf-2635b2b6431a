package ai.twodot.metaagent.platform.agents.securitymonitor.security;

import java.time.Instant;
import java.util.List;
import java.util.Map;

/**
 * Security Response - Response to a security event
 */
public record SecurityResponse(
    String responseId,
    String eventId,
    SecurityResponseType responseType,
    String action,
    String description,
    boolean automated,
    SecurityResponseStatus status,
    List<String> affectedSystems,
    Map<String, Object> parameters,
    Instant initiatedAt,
    Instant completedAt,
    String initiatedBy,
    double effectivenessScore
) {
    public static SecurityResponse automated(String eventId, String action, String description) {
        return new SecurityResponse(
            java.util.UUID.randomUUID().toString(),
            eventId,
            SecurityResponseType.AUTOMATED,
            action,
            description,
            true,
            SecurityResponseStatus.INITIATED,
            List.of(),
            Map.of(),
            Instant.now(),
            null,
            "system",
            0.0
        );
    }
    
    public boolean requiresAutomatedResponse() {
        return automated && (status == SecurityResponseStatus.INITIATED || status == SecurityResponseStatus.IN_PROGRESS);
    }
    
    public boolean requiresPolicyUpdate() {
        return responseType == SecurityResponseType.AUTOMATED && 
               action.contains("policy") && 
               effectivenessScore > 0.8;
    }
}