package ai.twodot.metaagent.platform.agents.securitymonitor.security;

/**
 * Risk Levels - Standardized risk level enumeration
 */
public enum RiskLevel {
    CRITICAL(4, "Critical - Immediate action required"),
    HIGH(3, "High - Urgent attention needed"),
    MEDIUM(2, "Medium - Should be addressed soon"),
    LOW(1, "Low - Monitor and review"),
    INFO(0, "Informational - No action required"),
    UNKNOWN(-1, "Unknown - Further analysis needed");
    
    private final int severity;
    private final String description;
    
    RiskLevel(int severity, String description) {
        this.severity = severity;
        this.description = description;
    }
    
    public int getSeverity() {
        return severity;
    }
    
    public String getDescription() {
        return description;
    }
    
    public boolean isHigherThan(RiskLevel other) {
        return this.severity > other.severity;
    }
    
    public static RiskLevel fromScore(double score) {
        if (score >= 0.9) return CRITICAL;
        if (score >= 0.7) return HIGH;
        if (score >= 0.5) return MEDIUM;
        if (score >= 0.3) return LOW;
        if (score >= 0.0) return INFO;
        return UNKNOWN;
    }
}