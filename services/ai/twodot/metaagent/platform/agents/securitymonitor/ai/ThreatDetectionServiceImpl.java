package ai.twodot.metaagent.platform.agents.securitymonitor.ai;

import ai.twodot.metaagent.platform.agents.securitymonitor.security.SecurityEvent;
import ai.twodot.metaagent.platform.agents.securitymonitor.security.SecuritySeverity;
import ai.twodot.metaagent.platform.agents.securitymonitor.security.ThreatAnalysisResult;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.time.Instant;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Service
public class ThreatDetectionServiceImpl implements ThreatDetectionService {

  private static final Logger logger = LoggerFactory.getLogger(
    ThreatDetectionServiceImpl.class
  );
  private final ObjectMapper objectMapper = new ObjectMapper();

  private final AtomicLong totalRequests = new AtomicLong(0);
  private final AtomicLong successfulRequests = new AtomicLong(0);
  private final AtomicBoolean modelsInitialized = new AtomicBoolean(false);

  private WebClient webClient; // Test will inject a mock via reflection

  public record MLThreatDetectionResponse(
    String eventId,
    boolean threatDetected,
    String threatType,
    double confidenceScore,
    String riskLevel,
    String analysisMethod,
    Map<String, Object> details,
    Instant timestamp,
    double processingTimeMs
  ) {}

  public record ThreatDetectionMetrics(
    long totalRequests,
    long successfulRequests,
    boolean modelsInitialized
  ) {}

  public ThreatDetectionServiceImpl() {
    this.webClient =
      WebClient.builder().baseUrl("http://ml-service:8080").build();
  }

  @Override
  public void initializeModels() {
    try {
      String response = webClient
        .get()
        .uri("/health")
        .retrieve()
        .bodyToMono(String.class)
        .block(); // Block for initialization
      JsonNode root = objectMapper.readTree(response);
      if ("healthy".equals(root.path("status").asText())) {
        modelsInitialized.set(true);
        logger.info("Threat detection models initialized successfully.");
      } else {
        modelsInitialized.set(false);
        logger.warn(
          "Threat detection models initialization failed: {}",
          response
        );
      }
    } catch (Exception e) {
      modelsInitialized.set(false);
      logger.error("Error initializing threat detection models", e);
    }
  }

  @Override
  public CompletableFuture<ThreatAnalysisResult> analyzeThreat(
    SecurityEvent event
  ) {
    totalRequests.incrementAndGet();
    if (!modelsInitialized.get()) {
      logger.warn(
        "AI models not initialized. Using fallback rules for event {}.",
        event.eventId()
      );
      return CompletableFuture.completedFuture(
        applyFallbackRules(event, "AI models not initialized")
      );
    }

    return webClient
      .post()
      .uri("/detect-threat")
      .bodyValue(event)
      .retrieve()
      .bodyToMono(MLThreatDetectionResponse.class)
      .map(mlResponse -> {
        successfulRequests.incrementAndGet();
        return new ThreatAnalysisResult(
          event.eventId(),
          mlResponse.threatDetected(),
          mlResponse.threatType(),
          mlResponse.confidenceScore(),
          mlResponse.riskLevel(),
          mlResponse.analysisMethod(),
          mlResponse.details(),
          mlResponse.threatDetected() && mlResponse.confidenceScore() > 0.7,
          Instant.now(),
          (long) mlResponse.processingTimeMs()
        );
      })
      .onErrorResume(e -> {
        logger.error(
          "ML service call failed for event {}. Applying fallback rules.",
          event.eventId(),
          e
        );
        return Mono.just(
          applyFallbackRules(event, "ML service call failed: " + e.getMessage())
        );
      })
      .toFuture();
  }

  private ThreatAnalysisResult applyFallbackRules(
    SecurityEvent event,
    String reason
  ) {
    boolean detected =
      event.severity() == SecuritySeverity.HIGH ||
      event.severity() == SecuritySeverity.CRITICAL;
    return new ThreatAnalysisResult(
      event.eventId(),
      detected,
      detected ? "SUSPICIOUS_ACTIVITY" : "NORMAL",
      detected ? 0.5 : 0.1,
      detected ? "MEDIUM" : "LOW",
      "FALLBACK_RULES",
      Map.of("reason", reason),
      false,
      Instant.now(),
      10L
    );
  }

  public CompletableFuture<ThreatAnalysisResult> analyzeThreatAsync(
    SecurityEvent event
  ) {
    return analyzeThreat(event);
  }

  @Override
  public String getStatus() {
    if (modelsInitialized.get()) {
      return "HEALTHY";
    } else {
      return "DEGRADED";
    }
  }

  public ThreatDetectionMetrics getMetrics() {
    return new ThreatDetectionMetrics(
      totalRequests.get(),
      successfulRequests.get(),
      modelsInitialized.get()
    );
  }

  public void startThreatHunting() {
    logger.info("Threat hunting started (placeholder).");
  }

  public void stopThreatHunting() {
    logger.info("Threat hunting stopped (placeholder).");
  }
}