package ai.twodot.metaagent.platform.agents.securitymonitor.agent;

import ai.twodot.metaagent.platform.agents.securitymonitor.ai.ThreatDetectionService;
import ai.twodot.metaagent.platform.agents.securitymonitor.intelligence.ThreatIntelligenceService;
import ai.twodot.metaagent.platform.agents.securitymonitor.monitoring.SecurityMetricsCollector;
import ai.twodot.metaagent.platform.agents.securitymonitor.security.BehavioralAnalysisResult;
import ai.twodot.metaagent.platform.agents.securitymonitor.security.BehavioralAnalysisService;
import ai.twodot.metaagent.platform.agents.securitymonitor.security.IncidentResponseService;
import ai.twodot.metaagent.platform.agents.securitymonitor.security.SecurityEvent;
import ai.twodot.metaagent.platform.agents.securitymonitor.security.SecurityEventProcessor;
import ai.twodot.metaagent.platform.agents.securitymonitor.security.SecurityPolicyService;

import java.time.Duration;
import java.time.Instant;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class SecurityMonitorAgent {

    private static final Logger logger = LoggerFactory.getLogger(SecurityMonitorAgent.class);

    private final ThreatDetectionService threatDetectionService;
    private final BehavioralAnalysisService behavioralAnalysisService;
    private final IncidentResponseService incidentResponseService;
    private final SecurityPolicyService securityPolicyService;
    private final SecurityEventProcessor securityEventProcessor;
    private final ThreatIntelligenceService threatIntelligenceService;
    private final SecurityMetricsCollector metricsCollector;

    private final AtomicBoolean running = new AtomicBoolean(false);
    private final Instant startTime = Instant.now();
    private final AtomicLong eventsProcessed = new AtomicLong(0);
    private final AtomicLong threatsDetected = new AtomicLong(0);
    private final AtomicLong incidentsHandled = new AtomicLong(0);

    public SecurityMonitorAgent(
            ThreatDetectionService threatDetectionService,
            BehavioralAnalysisService behavioralAnalysisService,
            IncidentResponseService incidentResponseService,
            SecurityPolicyService securityPolicyService,
            SecurityEventProcessor securityEventProcessor,
            ThreatIntelligenceService threatIntelligenceService,
            SecurityMetricsCollector metricsCollector) {
        this.threatDetectionService = threatDetectionService;
        this.behavioralAnalysisService = behavioralAnalysisService;
        this.incidentResponseService = incidentResponseService;
        this.securityPolicyService = securityPolicyService;
        this.securityEventProcessor = securityEventProcessor;
        this.threatIntelligenceService = threatIntelligenceService;
        this.metricsCollector = metricsCollector;
    }

    public void initialize() {
        threatDetectionService.initializeModels();
        behavioralAnalysisService.initializeProfiles();
        incidentResponseService.loadResponsePlaybooks();
        securityPolicyService.loadSecurityPolicies();
        securityEventProcessor.startEventStreaming();
        threatIntelligenceService.initializeThreatFeeds();
        metricsCollector.startCollection();
        running.set(true);
    }

    public CompletableFuture<SecurityEventProcessor.SecurityEventResult> processSecurityEvent(SecurityEvent event) {
        eventsProcessed.incrementAndGet();
        metricsCollector.recordEvent(event);

        return threatDetectionService.analyzeThreat(event)
                .thenCompose(threatAnalysis -> {
                    metricsCollector.recordThreatAnalysis(threatAnalysis);
                    if (threatAnalysis.isThreatDetected()) {
                        threatsDetected.incrementAndGet();
                    }

                    CompletableFuture<BehavioralAnalysisResult> behavioralFuture = threatAnalysis.requiresBehavioralAnalysis()
                            ? behavioralAnalysisService.analyzeEntity(event.entityId(), event)
                            : CompletableFuture.completedFuture(null);

                    return behavioralFuture.thenCompose(behavioralResult ->
                            securityPolicyService.determineResponse(event, threatAnalysis, behavioralResult)
                                    .thenCompose(securityResponse -> {
                                        metricsCollector.recordSecurityResponse(securityResponse);
                                        if (securityResponse.requiresAutomatedResponse()) {
                                            return incidentResponseService.respondToThreat(event, threatAnalysis, securityResponse)
                                                    .thenApply(incidentResult -> {
                                                        if (incidentResult.success()) {
                                                            incidentsHandled.incrementAndGet();
                                                        }
                                                        return SecurityEventProcessor.SecurityEventResult.success(event.eventId(), "Threat handled");
                                                    });
                                        }
                                        return CompletableFuture.completedFuture(
                                                SecurityEventProcessor.SecurityEventResult.success(event.eventId(), "Threat monitored"));
                                    })
                    );
                })
                .exceptionally(ex -> {
                    logger.error("Failed to process security event {}: {}", event.eventId(), ex.getMessage(), ex);
                    return new SecurityEventProcessor.SecurityEventResult(false, event.eventId(), "Processing failed: " + ex.getMessage());
                });
    }

    // getters, shutdown, and health status methods remain here...
    public boolean isRunning() { return running.get(); }
    public void shutdown() {
        securityEventProcessor.stopEventStreaming();
        metricsCollector.stopCollection();
        running.set(false);
    }
    public long getEventsProcessed() { return eventsProcessed.get(); }
    public long getThreatsDetected() { return threatsDetected.get(); }
    public long getIncidentsHandled() { return incidentsHandled.get(); }
    public AgentHealthStatus getHealthStatus() {
        Map<String, String> componentStatus = Stream.of(
            Map.entry("ThreatDetectionService", threatDetectionService.getStatus()),
            Map.entry("BehavioralAnalysisService", behavioralAnalysisService.getStatus()),
            Map.entry("IncidentResponseService", incidentResponseService.getStatus()),
            Map.entry("SecurityPolicyService", securityPolicyService.getStatus()),
            Map.entry("SecurityEventProcessor", securityEventProcessor.getStatus()),
            Map.entry("ThreatIntelligenceService", threatIntelligenceService.getStatus())
        ).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        boolean isHealthy = componentStatus.values().stream().allMatch(s -> "HEALTHY".equals(s) || "STREAMING".equals(s));
        return new AgentHealthStatus("SMA-003", "Security Monitor Agent", "1.0.0", isHealthy ? "HEALTHY" : "DEGRADED", Duration.between(startTime, Instant.now()).toString(), componentStatus);
    }
    public SecurityMetricsCollector.SecurityMetrics getSecurityMetrics() { return metricsCollector.getCurrentMetrics(); }
    public record AgentHealthStatus(String agentId, String agentName, String version, String status, String uptime, Map<String, String> componentStatus) {}
}