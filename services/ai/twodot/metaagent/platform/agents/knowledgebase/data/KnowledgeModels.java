package ai.twodot.metaagent.platform.agents.knowledgebase.data;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.Instant;
import java.util.List;
import java.util.Map;

/**
 * Knowledge Base Agent Data Models
 * 
 * Comprehensive data structures for knowledge management, extraction, storage, and retrieval.
 */
public class KnowledgeModels {

    // === Core Knowledge Types ===

    public record KnowledgeItem(
        @JsonProperty("knowledge_id") String knowledgeId,
        @JsonProperty("title") String title,
        @JsonProperty("content") String content,
        @JsonProperty("content_type") KnowledgeContentType contentType,
        @JsonProperty("source") KnowledgeSource source,
        @JsonProperty("metadata") KnowledgeMetadata metadata,
        @JsonProperty("classification") KnowledgeClassification classification,
        @JsonProperty("vector_embedding") List<Double> vectorEmbedding,
        @JsonProperty("entities") List<KnowledgeEntity> entities,
        @JsonProperty("relationships") List<KnowledgeRelationship> relationships,
        @JsonProperty("quality_score") double qualityScore,
        @JsonProperty("confidence_score") double confidenceScore,
        @JsonProperty("created_at") Instant createdAt,
        @JsonProperty("updated_at") Instant updatedAt
    ) {}

    public record KnowledgeMetadata(
        @JsonProperty("tags") List<String> tags,
        @JsonProperty("categories") List<String> categories,
        @JsonProperty("language") String language,
        @JsonProperty("domain") String domain,
        @JsonProperty("complexity_level") String complexityLevel,
        @JsonProperty("access_level") String accessLevel,
        @JsonProperty("author") String author,
        @JsonProperty("version") String version,
        @JsonProperty("last_verified") Instant lastVerified,
        @JsonProperty("custom_attributes") Map<String, Object> customAttributes
    ) {}

    public record KnowledgeSource(
        @JsonProperty("source_id") String sourceId,
        @JsonProperty("source_type") KnowledgeSourceType sourceType,
        @JsonProperty("source_location") String sourceLocation,
        @JsonProperty("source_name") String sourceName,
        @JsonProperty("extraction_method") String extractionMethod,
        @JsonProperty("reliability_score") double reliabilityScore,
        @JsonProperty("last_accessed") Instant lastAccessed
    ) {}

    public record KnowledgeEntity(
        @JsonProperty("entity_id") String entityId,
        @JsonProperty("entity_type") String entityType,
        @JsonProperty("entity_value") String entityValue,
        @JsonProperty("entity_context") String entityContext,
        @JsonProperty("confidence") double confidence,
        @JsonProperty("start_position") int startPosition,
        @JsonProperty("end_position") int endPosition
    ) {}

    public record KnowledgeRelationship(
        @JsonProperty("relationship_id") String relationshipId,
        @JsonProperty("source_entity") String sourceEntity,
        @JsonProperty("target_entity") String targetEntity,
        @JsonProperty("relationship_type") String relationshipType,
        @JsonProperty("relationship_strength") double relationshipStrength,
        @JsonProperty("context") String context,
        @JsonProperty("created_at") Instant createdAt
    ) {}

    public record KnowledgeClassification(
        @JsonProperty("primary_category") String primaryCategory,
        @JsonProperty("secondary_categories") List<String> secondaryCategories,
        @JsonProperty("knowledge_type") KnowledgeType knowledgeType,
        @JsonProperty("complexity_score") double complexityScore,
        @JsonProperty("relevance_score") double relevanceScore,
        @JsonProperty("importance_level") ImportanceLevel importanceLevel
    ) {}

    // === Knowledge Operations ===

    public record KnowledgeExtractionRequest(
        @JsonProperty("extraction_id") String extractionId,
        @JsonProperty("source") KnowledgeSource source,
        @JsonProperty("content") String content,
        @JsonProperty("extraction_config") ExtractionConfiguration extractionConfig,
        @JsonProperty("target_categories") List<String> targetCategories,
        @JsonProperty("required_entities") List<String> requiredEntities,
        @JsonProperty("agent_context") String agentContext,
        @JsonProperty("requested_by") String requestedBy
    ) {}

    public record ExtractionConfiguration(
        @JsonProperty("enable_entity_extraction") boolean enableEntityExtraction,
        @JsonProperty("enable_relationship_mapping") boolean enableRelationshipMapping,
        @JsonProperty("enable_classification") boolean enableClassification,
        @JsonProperty("enable_vector_embedding") boolean enableVectorEmbedding,
        @JsonProperty("quality_threshold") double qualityThreshold,
        @JsonProperty("confidence_threshold") double confidenceThreshold,
        @JsonProperty("max_entities") int maxEntities,
        @JsonProperty("extraction_timeout_ms") long extractionTimeoutMs
    ) {}

    public record KnowledgeExtractionResult(
        @JsonProperty("extraction_id") String extractionId,
        @JsonProperty("status") ExtractionStatus status,
        @JsonProperty("extracted_knowledge") List<KnowledgeItem> extractedKnowledge,
        @JsonProperty("extraction_metrics") ExtractionMetrics extractionMetrics,
        @JsonProperty("quality_assessment") QualityAssessment qualityAssessment,
        @JsonProperty("processing_time_ms") long processingTimeMs,
        @JsonProperty("error_messages") List<String> errorMessages,
        @JsonProperty("completed_at") Instant completedAt
    ) {}

    public record ExtractionMetrics(
        @JsonProperty("total_content_processed") long totalContentProcessed,
        @JsonProperty("knowledge_items_extracted") int knowledgeItemsExtracted,
        @JsonProperty("entities_identified") int entitiesIdentified,
        @JsonProperty("relationships_mapped") int relationshipsMapped,
        @JsonProperty("average_quality_score") double averageQualityScore,
        @JsonProperty("average_confidence_score") double averageConfidenceScore
    ) {}

    public record QualityAssessment(
        @JsonProperty("overall_quality_score") double overallQualityScore,
        @JsonProperty("completeness_score") double completenessScore,
        @JsonProperty("accuracy_score") double accuracyScore,
        @JsonProperty("relevance_score") double relevanceScore,
        @JsonProperty("consistency_score") double consistencyScore,
        @JsonProperty("quality_issues") List<String> qualityIssues,
        @JsonProperty("improvement_recommendations") List<String> improvementRecommendations
    ) {}

    // === Semantic Search ===

    public record SemanticSearchRequest(
        @JsonProperty("search_id") String searchId,
        @JsonProperty("query") String query,
        @JsonProperty("search_type") SemanticSearchType searchType,
        @JsonProperty("context") String context,
        @JsonProperty("filters") SearchFilters filters,
        @JsonProperty("result_limit") int resultLimit,
        @JsonProperty("similarity_threshold") double similarityThreshold,
        @JsonProperty("requested_by") String requestedBy
    ) {}

    public record SearchFilters(
        @JsonProperty("categories") List<String> categories,
        @JsonProperty("knowledge_types") List<KnowledgeType> knowledgeTypes,
        @JsonProperty("source_types") List<KnowledgeSourceType> sourceTypes,
        @JsonProperty("date_range") DateRange dateRange,
        @JsonProperty("quality_threshold") double qualityThreshold,
        @JsonProperty("access_levels") List<String> accessLevels,
        @JsonProperty("tags") List<String> tags
    ) {}

    public record DateRange(
        @JsonProperty("start_date") Instant startDate,
        @JsonProperty("end_date") Instant endDate
    ) {}

    public record SemanticSearchResult(
        @JsonProperty("search_id") String searchId,
        @JsonProperty("query") String query,
        @JsonProperty("results") List<KnowledgeSearchMatch> results,
        @JsonProperty("total_results") int totalResults,
        @JsonProperty("search_time_ms") long searchTimeMs,
        @JsonProperty("result_quality_score") double resultQualityScore,
        @JsonProperty("search_suggestions") List<String> searchSuggestions,
        @JsonProperty("completed_at") Instant completedAt
    ) {}

    public record KnowledgeSearchMatch(
        @JsonProperty("knowledge_item") KnowledgeItem knowledgeItem,
        @JsonProperty("similarity_score") double similarityScore,
        @JsonProperty("relevance_score") double relevanceScore,
        @JsonProperty("matching_context") String matchingContext,
        @JsonProperty("highlighted_content") String highlightedContent,
        @JsonProperty("explanation") String explanation
    ) {}

    // === Pattern Recognition ===

    public record PatternRecognitionRequest(
        @JsonProperty("pattern_request_id") String patternRequestId,
        @JsonProperty("knowledge_items") List<KnowledgeItem> knowledgeItems,
        @JsonProperty("pattern_types") List<PatternType> patternTypes,
        @JsonProperty("analysis_config") PatternAnalysisConfig analysisConfig,
        @JsonProperty("time_range") DateRange timeRange,
        @JsonProperty("requested_by") String requestedBy
    ) {}

    public record PatternAnalysisConfig(
        @JsonProperty("enable_temporal_analysis") boolean enableTemporalAnalysis,
        @JsonProperty("enable_relationship_analysis") boolean enableRelationshipAnalysis,
        @JsonProperty("enable_trend_detection") boolean enableTrendDetection,
        @JsonProperty("enable_anomaly_detection") boolean enableAnomalyDetection,
        @JsonProperty("pattern_confidence_threshold") double patternConfidenceThreshold,
        @JsonProperty("min_pattern_support") int minPatternSupport
    ) {}

    public record PatternRecognitionResult(
        @JsonProperty("pattern_request_id") String patternRequestId,
        @JsonProperty("detected_patterns") List<KnowledgePattern> detectedPatterns,
        @JsonProperty("relationship_insights") List<RelationshipInsight> relationshipInsights,
        @JsonProperty("trends") List<KnowledgeTrend> trends,
        @JsonProperty("anomalies") List<KnowledgeAnomaly> anomalies,
        @JsonProperty("pattern_metrics") PatternMetrics patternMetrics,
        @JsonProperty("analysis_time_ms") long analysisTimeMs,
        @JsonProperty("completed_at") Instant completedAt
    ) {}

    public record KnowledgePattern(
        @JsonProperty("pattern_id") String patternId,
        @JsonProperty("pattern_type") PatternType patternType,
        @JsonProperty("pattern_description") String patternDescription,
        @JsonProperty("support_count") int supportCount,
        @JsonProperty("confidence_score") double confidenceScore,
        @JsonProperty("related_knowledge_items") List<String> relatedKnowledgeItems,
        @JsonProperty("pattern_context") String patternContext,
        @JsonProperty("actionable_insights") List<String> actionableInsights
    ) {}

    public record RelationshipInsight(
        @JsonProperty("insight_id") String insightId,
        @JsonProperty("related_entities") List<String> relatedEntities,
        @JsonProperty("relationship_strength") double relationshipStrength,
        @JsonProperty("relationship_context") String relationshipContext,
        @JsonProperty("insight_description") String insightDescription,
        @JsonProperty("confidence_score") double confidenceScore
    ) {}

    public record KnowledgeTrend(
        @JsonProperty("trend_id") String trendId,
        @JsonProperty("trend_type") String trendType,
        @JsonProperty("trend_direction") String trendDirection,
        @JsonProperty("trend_strength") double trendStrength,
        @JsonProperty("time_series_data") List<TrendDataPoint> timeSeriesData,
        @JsonProperty("trend_description") String trendDescription,
        @JsonProperty("prediction") TrendPrediction prediction
    ) {}

    public record TrendDataPoint(
        @JsonProperty("timestamp") Instant timestamp,
        @JsonProperty("value") double value,
        @JsonProperty("context") String context
    ) {}

    public record TrendPrediction(
        @JsonProperty("predicted_direction") String predictedDirection,
        @JsonProperty("confidence") double confidence,
        @JsonProperty("time_horizon") String timeHorizon,
        @JsonProperty("predicted_values") List<TrendDataPoint> predictedValues
    ) {}

    public record KnowledgeAnomaly(
        @JsonProperty("anomaly_id") String anomalyId,
        @JsonProperty("anomaly_type") String anomalyType,
        @JsonProperty("severity") AnomalySeverity severity,
        @JsonProperty("affected_knowledge_items") List<String> affectedKnowledgeItems,
        @JsonProperty("anomaly_score") double anomalyScore,
        @JsonProperty("description") String description,
        @JsonProperty("detected_at") Instant detectedAt
    ) {}

    public record PatternMetrics(
        @JsonProperty("total_patterns_detected") int totalPatternsDetected,
        @JsonProperty("high_confidence_patterns") int highConfidencePatterns,
        @JsonProperty("relationship_insights_generated") int relationshipInsightsGenerated,
        @JsonProperty("trends_identified") int trendsIdentified,
        @JsonProperty("anomalies_detected") int anomaliesDetected,
        @JsonProperty("average_pattern_confidence") double averagePatternConfidence
    ) {}

    // === Recommendations ===

    public record RecommendationRequest(
        @JsonProperty("recommendation_id") String recommendationId,
        @JsonProperty("agent_id") String agentId,
        @JsonProperty("context") String context,
        @JsonProperty("current_knowledge") List<String> currentKnowledge,
        @JsonProperty("user_preferences") Map<String, Object> userPreferences,
        @JsonProperty("recommendation_type") RecommendationType recommendationType,
        @JsonProperty("max_recommendations") int maxRecommendations
    ) {}

    public record RecommendationResult(
        @JsonProperty("recommendation_id") String recommendationId,
        @JsonProperty("recommendations") List<KnowledgeRecommendation> recommendations,
        @JsonProperty("learning_opportunities") List<LearningOpportunity> learningOpportunities,
        @JsonProperty("collaboration_suggestions") List<CollaborationSuggestion> collaborationSuggestions,
        @JsonProperty("recommendation_quality_score") double recommendationQualityScore,
        @JsonProperty("generated_at") Instant generatedAt
    ) {}

    public record KnowledgeRecommendation(
        @JsonProperty("recommendation_id") String recommendationId,
        @JsonProperty("knowledge_item") KnowledgeItem knowledgeItem,
        @JsonProperty("relevance_score") double relevanceScore,
        @JsonProperty("recommendation_reason") String recommendationReason,
        @JsonProperty("expected_benefit") String expectedBenefit,
        @JsonProperty("priority") RecommendationPriority priority
    ) {}

    public record LearningOpportunity(
        @JsonProperty("opportunity_id") String opportunityId,
        @JsonProperty("opportunity_type") String opportunityType,
        @JsonProperty("knowledge_gap") String knowledgeGap,
        @JsonProperty("suggested_learning_path") List<String> suggestedLearningPath,
        @JsonProperty("estimated_effort") String estimatedEffort,
        @JsonProperty("expected_outcome") String expectedOutcome
    ) {}

    public record CollaborationSuggestion(
        @JsonProperty("suggestion_id") String suggestionId,
        @JsonProperty("collaboration_type") String collaborationType,
        @JsonProperty("suggested_agents") List<String> suggestedAgents,
        @JsonProperty("collaboration_topic") String collaborationTopic,
        @JsonProperty("expected_synergy") String expectedSynergy,
        @JsonProperty("collaboration_priority") RecommendationPriority collaborationPriority
    ) {}

    // === A2A Integration ===

    public record KnowledgeShareRequest(
        @JsonProperty("share_id") String shareId,
        @JsonProperty("source_agent_id") String sourceAgentId,
        @JsonProperty("target_agent_id") String targetAgentId,
        @JsonProperty("knowledge_items") List<String> knowledgeItems,
        @JsonProperty("share_purpose") String sharePurpose,
        @JsonProperty("access_level") String accessLevel,
        @JsonProperty("expiry_time") Instant expiryTime
    ) {}

    public record KnowledgeShareResult(
        @JsonProperty("share_id") String shareId,
        @JsonProperty("status") ShareStatus status,
        @JsonProperty("shared_knowledge_count") int sharedKnowledgeCount,
        @JsonProperty("delivery_confirmation") boolean deliveryConfirmation,
        @JsonProperty("access_granted") boolean accessGranted,
        @JsonProperty("share_metadata") Map<String, Object> shareMetadata,
        @JsonProperty("completed_at") Instant completedAt
    ) {}

    public record KnowledgeFeedback(
        @JsonProperty("feedback_id") String feedbackId,
        @JsonProperty("agent_id") String agentId,
        @JsonProperty("knowledge_item_id") String knowledgeItemId,
        @JsonProperty("feedback_type") FeedbackType feedbackType,
        @JsonProperty("usefulness_rating") int usefulnessRating,
        @JsonProperty("accuracy_rating") int accuracyRating,
        @JsonProperty("relevance_rating") int relevanceRating,
        @JsonProperty("feedback_text") String feedbackText,
        @JsonProperty("improvement_suggestions") List<String> improvementSuggestions,
        @JsonProperty("submitted_at") Instant submittedAt
    ) {}

    // === Enums ===

    public enum KnowledgeContentType {
        TEXT, DOCUMENT, CODE, API_SPEC, LOG_ENTRY, METADATA, 
        STRUCTURED_DATA, CONVERSATION, EMAIL, PRESENTATION, 
        WIKI_PAGE, MANUAL, REPORT, RESEARCH_PAPER, OTHER
    }

    public enum KnowledgeSourceType {
        DOCUMENT_FILE, DATABASE, API_ENDPOINT, WEB_PAGE, 
        LOG_FILE, CODE_REPOSITORY, EMAIL_SYSTEM, 
        CONVERSATION_TRANSCRIPT, MANUAL_INPUT, 
        AGENT_GENERATED, REAL_TIME_STREAM, OTHER
    }

    public enum KnowledgeType {
        FACTUAL, PROCEDURAL, CONCEPTUAL, METACOGNITIVE, 
        EXPERIENTIAL, TECHNICAL, BUSINESS, PERSONAL, OTHER
    }

    public enum ImportanceLevel {
        CRITICAL, HIGH, MEDIUM, LOW, INFORMATIONAL
    }

    public enum ExtractionStatus {
        PENDING, IN_PROGRESS, COMPLETED, FAILED, CANCELLED
    }

    public enum SemanticSearchType {
        SIMILARITY, CONTEXTUAL, HYBRID, KEYWORD, SEMANTIC_ONLY
    }

    public enum PatternType {
        USAGE_PATTERN, TEMPORAL_PATTERN, RELATIONSHIP_PATTERN, 
        CONTENT_PATTERN, BEHAVIORAL_PATTERN, KNOWLEDGE_FLOW_PATTERN
    }

    public enum AnomalySeverity {
        LOW, MEDIUM, HIGH, CRITICAL
    }

    public enum RecommendationType {
        KNOWLEDGE_DISCOVERY, LEARNING_PATH, COLLABORATION, 
        KNOWLEDGE_GAP, TRENDING_TOPICS, PERSONALIZED
    }

    public enum RecommendationPriority {
        URGENT, HIGH, MEDIUM, LOW, OPTIONAL
    }

    public enum ShareStatus {
        PENDING, IN_PROGRESS, COMPLETED, FAILED, REJECTED
    }

    public enum FeedbackType {
        POSITIVE, NEGATIVE, NEUTRAL, IMPROVEMENT_REQUEST, BUG_REPORT
    }

    // === Agent Status and Health ===

    public record AgentHealthStatus(
        @JsonProperty("agent_id") String agentId,
        @JsonProperty("status") AgentStatus status,
        @JsonProperty("health_score") double healthScore,
        @JsonProperty("active_extractions") int activeExtractions,
        @JsonProperty("active_searches") int activeSearches,
        @JsonProperty("knowledge_item_count") long knowledgeItemCount,
        @JsonProperty("total_storage_mb") long totalStorageMb,
        @JsonProperty("cpu_usage_percent") double cpuUsagePercent,
        @JsonProperty("memory_usage_percent") double memoryUsagePercent,
        @JsonProperty("last_health_check") Instant lastHealthCheck,
        @JsonProperty("uptime_hours") long uptimeHours
    ) {}

    public enum AgentStatus {
        HEALTHY, DEGRADED, UNHEALTHY, OFFLINE, MAINTENANCE
    }

    // === Processing Context ===

    public record ProcessingContext(
        @JsonProperty("context_id") String contextId,
        @JsonProperty("agent_id") String agentId,
        @JsonProperty("session_id") String sessionId,
        @JsonProperty("operation_type") String operationType,
        @JsonProperty("context_data") Map<String, Object> contextData,
        @JsonProperty("created_at") Instant createdAt
    ) {}
}