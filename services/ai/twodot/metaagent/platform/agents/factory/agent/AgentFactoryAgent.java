package ai.twodot.metaagent.platform.agents.factory.agent;

import ai.twodot.metaagent.platform.agents.factory.data.GenerationModels.*;
import ai.twodot.metaagent.platform.agents.factory.generation.AgentGenerationService;
import ai.twodot.metaagent.platform.agents.factory.orchestration.GenerationOrchestrationService;
import ai.twodot.metaagent.platform.agents.factory.template.TemplateManagementService;
import ai.twodot.metaagent.platform.agents.factory.quality.QualityValidationService;
import ai.twodot.metaagent.platform.agents.factory.deployment.DeploymentAutomationService;
import ai.twodot.metaagent.platform.agents.factory.ai.AIIntegrationService;
import ai.twodot.metaagent.platform.agents.factory.integration.FactoryAgentIntegrationService;
import ai.twodot.metaagent.platform.agents.factory.monitoring.GenerationMonitoringService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Agent Factory Agent (AFA-008)
 * 
 * Core agent responsible for AI-powered agent generation factory that creates complete,
 * production-ready intelligent agents from natural language requirements.
 * 
 * Key Capabilities:
 * - AI-powered requirement analysis and architecture design
 * - Multi-language code generation (Python, Java, TypeScript, Go)
 * - Intelligent template selection and customization
 * - Automated testing and quality assurance
 * - CI/CD pipeline generation and deployment automation
 * - Real-time code optimization and security validation
 * - Self-improving generation through machine learning
 * 
 * Integration Features:
 * - A2A communication with all platform agents
 * - Multi-AI model integration (GPT-4, Claude, Codex, Copilot)
 * - Enterprise-grade security and compliance
 * - Comprehensive monitoring and observability
 * 
 * <AUTHOR> Platform Team
 * @version 1.0.0
 * @since 2025-01-14
 */
@Component
public class AgentFactoryAgent {
    
    private static final Logger logger = LoggerFactory.getLogger(AgentFactoryAgent.class);
    
    private final AtomicBoolean isInitialized = new AtomicBoolean(false);
    private final AtomicBoolean isHealthy = new AtomicBoolean(false);
    private final AtomicReference<AgentStatus> agentStatus = new AtomicReference<>(AgentStatus.INITIALIZING);
    
    // Core Services
    private final AgentGenerationService agentGenerationService;
    private final GenerationOrchestrationService generationOrchestrationService;
    private final TemplateManagementService templateManagementService;
    private final QualityValidationService qualityValidationService;
    private final DeploymentAutomationService deploymentAutomationService;
    private final AIIntegrationService aiIntegrationService;
    private final FactoryAgentIntegrationService agentIntegrationService;
    private final GenerationMonitoringService monitoringService;
    
    // HTTP Client for external communications
    private final WebClient webClient;
    
    // Agent Statistics
    private long totalAgentsGenerated = 0;
    private long successfulGenerations = 0;
    private long totalCodeLinesGenerated = 0;
    private double averageQualityScore = 0.0;
    private double averageGenerationTimeMs = 0.0;
    
    /**
     * Constructor with dependency injection
     */
    @Autowired
    public AgentFactoryAgent(
            AgentGenerationService agentGenerationService,
            GenerationOrchestrationService generationOrchestrationService,
            TemplateManagementService templateManagementService,
            QualityValidationService qualityValidationService,
            DeploymentAutomationService deploymentAutomationService,
            AIIntegrationService aiIntegrationService,
            FactoryAgentIntegrationService agentIntegrationService,
            GenerationMonitoringService monitoringService,
            WebClient.Builder webClientBuilder) {
        
        this.agentGenerationService = agentGenerationService;
        this.generationOrchestrationService = generationOrchestrationService;
        this.templateManagementService = templateManagementService;
        this.qualityValidationService = qualityValidationService;
        this.deploymentAutomationService = deploymentAutomationService;
        this.aiIntegrationService = aiIntegrationService;
        this.agentIntegrationService = agentIntegrationService;
        this.monitoringService = monitoringService;
        this.webClient = webClientBuilder.build();
        
        logger.info("Agent Factory Agent (AFA-008) initialized with core services");
    }
    
    /**
     * Initialize the Agent Factory Agent
     */
    public CompletableFuture<Void> initialize() {
        return CompletableFuture.runAsync(() -> {
            try {
                logger.info("Initializing Agent Factory Agent...");
                agentStatus.set(AgentStatus.INITIALIZING);
                
                // Initialize core services
                agentGenerationService.initialize();
                generationOrchestrationService.initialize();
                templateManagementService.initialize();
                qualityValidationService.initialize();
                deploymentAutomationService.initialize();
                aiIntegrationService.initialize();
                agentIntegrationService.initialize();
                monitoringService.initialize();
                
                // Load and validate templates
                loadAndValidateTemplates();
                
                // Initialize AI models
                initializeAIModels();
                
                // Start health monitoring
                startHealthMonitoring();
                
                // Mark as initialized and healthy
                isInitialized.set(true);
                isHealthy.set(true);
                agentStatus.set(AgentStatus.ACTIVE);
                
                logger.info("Agent Factory Agent initialization completed successfully");
                
            } catch (Exception e) {
                logger.error("Failed to initialize Agent Factory Agent", e);
                agentStatus.set(AgentStatus.ERROR);
                throw new RuntimeException("Agent Factory Agent initialization failed", e);
            }
        });
    }
    
    /**
     * Generate a complete agent from natural language requirements
     */
    public CompletableFuture<AgentGenerationResponse> generateAgent(AgentGenerationRequest request) {
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            
            try {
                logger.info("Starting agent generation: {} with request ID: {}", 
                           request.agentName(), request.requestId());
                
                // Validate request
                validateGenerationRequest(request);
                
                // Phase 1: AI-powered requirement analysis
                logger.info("Phase 1: Analyzing requirements with AI");
                AgentRequirements analyzedRequirements = aiIntegrationService.analyzeRequirements(
                    request.description(), request.requirements(), request.context()
                );
                
                // Phase 2: Architecture design with AI
                logger.info("Phase 2: Designing architecture with AI");
                AgentSpecification agentSpec = aiIntegrationService.designArchitecture(
                    analyzedRequirements, request.preferences()
                );
                
                // Phase 3: Template selection and customization
                logger.info("Phase 3: Selecting and customizing templates");
                List<AgentTemplate> selectedTemplates = templateManagementService.selectOptimalTemplates(
                    agentSpec, request.preferences()
                );
                
                // Phase 4: Multi-language code generation
                logger.info("Phase 4: Generating code with AI");
                GeneratedCodeResult generatedCode = agentGenerationService.generateCode(
                    agentSpec, selectedTemplates, request.preferences()
                );
                
                // Phase 5: Test generation
                logger.info("Phase 5: Generating comprehensive test suite");
                TestSuiteResult testSuite = agentGenerationService.generateTestSuite(
                    agentSpec, generatedCode, request.qualityRequirements()
                );
                
                // Phase 6: Documentation generation
                logger.info("Phase 6: Generating documentation");
                DocumentationResult documentation = agentGenerationService.generateDocumentation(
                    agentSpec, generatedCode, request.preferences().documentationLevel()
                );
                
                // Phase 7: Quality validation and optimization
                logger.info("Phase 7: Validating quality and optimizing");
                QualityReport qualityReport = qualityValidationService.validateGeneration(
                    generatedCode, testSuite, documentation, request.qualityRequirements()
                );
                
                // Apply optimizations if needed
                if (qualityReport.improvementSuggestions().size() > 0) {
                    logger.info("Applying quality improvements");
                    generatedCode = applyQualityImprovements(generatedCode, qualityReport);
                    
                    // Re-validate after improvements
                    qualityReport = qualityValidationService.validateGeneration(
                        generatedCode, testSuite, documentation, request.qualityRequirements()
                    );
                }
                
                // Phase 8: Deployment configuration generation
                logger.info("Phase 8: Generating deployment configuration");
                DeploymentConfiguration deploymentConfig = deploymentAutomationService.generateDeploymentConfiguration(
                    agentSpec, generatedCode, request.deploymentRequirements()
                );
                
                // Phase 9: Integration with platform ecosystem
                logger.info("Phase 9: Preparing platform integration");
                prepareAgentIntegration(agentSpec, generatedCode, deploymentConfig);
                
                // Calculate metrics
                long totalDuration = System.currentTimeMillis() - startTime;
                updateGenerationStatistics(totalDuration, qualityReport.overallQualityScore(), generatedCode);
                
                // Collect AI insights
                List<AIInsight> aiInsights = aiIntegrationService.getGenerationInsights(request.requestId());
                
                // Create generation metadata
                GenerationMetadata metadata = createGenerationMetadata(
                    request.requestId(), selectedTemplates, aiInsights, totalDuration
                );
                
                // Create response
                AgentGenerationResponse response = new AgentGenerationResponse(
                    request.requestId(),
                    UUID.randomUUID().toString(),
                    GenerationStatus.COMPLETED,
                    agentSpec,
                    generatedCode,
                    testSuite,
                    documentation,
                    deploymentConfig,
                    qualityReport,
                    metadata,
                    aiInsights,
                    Instant.now(),
                    totalDuration
                );
                
                // Store generation result
                storeGenerationResult(response);
                
                logger.info("Agent generation completed successfully in {}ms for: {}", 
                           totalDuration, request.agentName());
                
                return response;
                
            } catch (Exception e) {
                long totalDuration = System.currentTimeMillis() - startTime;
                logger.error("Agent generation failed for: {} after {}ms", request.agentName(), totalDuration, e);
                
                return new AgentGenerationResponse(
                    request.requestId(),
                    UUID.randomUUID().toString(),
                    GenerationStatus.FAILED,
                    null, null, null, null, null,
                    createErrorQualityReport(e.getMessage()),
                    createErrorMetadata(request.requestId(), totalDuration),
                    List.of(),
                    Instant.now(),
                    totalDuration
                );
            }
        });
    }
    
    /**
     * Deploy a generated agent to the target environment
     */
    public CompletableFuture<DeploymentResult> deployGeneratedAgent(
            String generationId, 
            DeploymentConfiguration deploymentConfig
    ) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                logger.info("Deploying generated agent: {}", generationId);
                
                // Deploy using automation service
                DeploymentResult result = deploymentAutomationService.deployAgent(
                    generationId, deploymentConfig
                );
                
                // Register with platform if deployment successful
                if (result.status() == DeploymentStatus.COMPLETED) {
                    agentIntegrationService.registerAgentWithPlatform(result);
                }
                
                logger.info("Agent deployment completed with status: {}", result.status());
                return result;
                
            } catch (Exception e) {
                logger.error("Agent deployment failed for generation: {}", generationId, e);
                throw new RuntimeException("Agent deployment failed", e);
            }
        });
    }
    
    /**
     * Get generation status and progress
     */
    public CompletableFuture<GenerationProgress> getGenerationProgress(String requestId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return generationOrchestrationService.getGenerationProgress(requestId);
            } catch (Exception e) {
                logger.error("Failed to get generation progress for: {}", requestId, e);
                throw new RuntimeException("Failed to get generation progress", e);
            }
        });
    }
    
    /**
     * List available templates with filtering
     */
    public CompletableFuture<List<AgentTemplate>> getAvailableTemplates(
            TemplateType templateType,
            List<ProgrammingLanguage> languages
    ) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return templateManagementService.getTemplatesByTypeAndLanguages(templateType, languages);
            } catch (Exception e) {
                logger.error("Failed to get available templates", e);
                throw new RuntimeException("Failed to get available templates", e);
            }
        });
    }
    
    /**
     * Get quality metrics for a generation
     */
    public CompletableFuture<QualityReport> getQualityReport(String generationId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return qualityValidationService.getQualityReport(generationId);
            } catch (Exception e) {
                logger.error("Failed to get quality report for: {}", generationId, e);
                throw new RuntimeException("Failed to get quality report", e);
            }
        });
    }
    
    /**
     * Get agent health status
     */
    public boolean isHealthy() {
        return isHealthy.get() && 
               agentGenerationService.isHealthy() &&
               generationOrchestrationService.isHealthy() &&
               templateManagementService.isHealthy() &&
               qualityValidationService.isHealthy() &&
               deploymentAutomationService.isHealthy() &&
               aiIntegrationService.isHealthy();
    }
    
    /**
     * Get comprehensive agent metrics
     */
    public Map<String, Object> getMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        metrics.put("agent_status", agentStatus.get().toString());
        metrics.put("is_healthy", isHealthy());
        metrics.put("is_initialized", isInitialized.get());
        metrics.put("total_agents_generated", totalAgentsGenerated);
        metrics.put("successful_generations", successfulGenerations);
        metrics.put("success_rate", calculateSuccessRate());
        metrics.put("total_code_lines_generated", totalCodeLinesGenerated);
        metrics.put("average_quality_score", averageQualityScore);
        metrics.put("average_generation_time_ms", averageGenerationTimeMs);
        
        // Service-specific metrics
        metrics.put("generation_service_metrics", agentGenerationService.getMetrics());
        metrics.put("orchestration_service_metrics", generationOrchestrationService.getMetrics());
        metrics.put("template_service_metrics", templateManagementService.getMetrics());
        metrics.put("quality_service_metrics", qualityValidationService.getMetrics());
        metrics.put("deployment_service_metrics", deploymentAutomationService.getMetrics());
        metrics.put("ai_service_metrics", aiIntegrationService.getMetrics());
        metrics.put("integration_service_metrics", agentIntegrationService.getMetrics());
        metrics.put("monitoring_service_metrics", monitoringService.getMetrics());
        
        return metrics;
    }
    
    /**
     * Get agent capabilities
     */
    public AgentCapabilities getCapabilities() {
        return new AgentCapabilities(
            "AFA-008",
            "AgentFactoryAgent",
            List.of(
                "AI_POWERED_CODE_GENERATION",
                "MULTI_LANGUAGE_SUPPORT",
                "TEMPLATE_MANAGEMENT",
                "QUALITY_VALIDATION",
                "DEPLOYMENT_AUTOMATION",
                "ARCHITECTURE_DESIGN",
                "TEST_GENERATION",
                "DOCUMENTATION_GENERATION"
            ),
            List.of(ProgrammingLanguage.PYTHON, ProgrammingLanguage.JAVA, 
                   ProgrammingLanguage.TYPESCRIPT, ProgrammingLanguage.GO),
            List.of(TemplateType.AGENT_TEMPLATE, TemplateType.FRAMEWORK_TEMPLATE,
                   TemplateType.INTEGRATION_TEMPLATE, TemplateType.DEPLOYMENT_TEMPLATE),
            new GenerationCapacityMetrics(
                100, // agents per hour
                successfulGenerations > 0 ? (double) successfulGenerations / totalAgentsGenerated * 100 : 0.0,
                averageGenerationTimeMs,
                averageQualityScore
            ),
            isHealthy() ? "AVAILABLE" : "DEGRADED",
            Instant.now()
        );
    }
    
    /**
     * Graceful shutdown
     */
    public CompletableFuture<Void> shutdown() {
        return CompletableFuture.runAsync(() -> {
            try {
                logger.info("Shutting down Agent Factory Agent...");
                agentStatus.set(AgentStatus.SHUTTING_DOWN);
                
                // Stop accepting new requests
                isHealthy.set(false);
                
                // Shutdown services gracefully
                aiIntegrationService.shutdown();
                deploymentAutomationService.shutdown();
                qualityValidationService.shutdown();
                templateManagementService.shutdown();
                agentGenerationService.shutdown();
                generationOrchestrationService.shutdown();
                agentIntegrationService.shutdown();
                monitoringService.shutdown();
                
                agentStatus.set(AgentStatus.STOPPED);
                logger.info("Agent Factory Agent shutdown completed");
                
            } catch (Exception e) {
                logger.error("Error during Agent Factory Agent shutdown", e);
                agentStatus.set(AgentStatus.ERROR);
            }
        });
    }
    
    // ===== Private Helper Methods =====
    
    private void validateGenerationRequest(AgentGenerationRequest request) {
        if (request.agentName() == null || request.agentName().trim().isEmpty()) {
            throw new IllegalArgumentException("Agent name is required");
        }
        if (request.description() == null || request.description().trim().isEmpty()) {
            throw new IllegalArgumentException("Agent description is required");
        }
        if (request.requirements() == null) {
            throw new IllegalArgumentException("Agent requirements are required");
        }
    }
    
    private void loadAndValidateTemplates() {
        try {
            logger.info("Loading and validating agent templates...");
            templateManagementService.loadAllTemplates();
            templateManagementService.validateTemplates();
            logger.info("Template loading and validation completed");
        } catch (Exception e) {
            logger.error("Failed to load and validate templates", e);
            throw new RuntimeException("Template initialization failed", e);
        }
    }
    
    private void initializeAIModels() {
        try {
            logger.info("Initializing AI models...");
            aiIntegrationService.initializeModels();
            logger.info("AI model initialization completed");
        } catch (Exception e) {
            logger.error("Failed to initialize AI models", e);
            throw new RuntimeException("AI model initialization failed", e);
        }
    }
    
    private GeneratedCodeResult applyQualityImprovements(
            GeneratedCodeResult generatedCode, 
            QualityReport qualityReport
    ) {
        try {
            logger.info("Applying {} quality improvements", qualityReport.improvementSuggestions().size());
            return aiIntegrationService.optimizeGeneratedCode(generatedCode, qualityReport.improvementSuggestions());
        } catch (Exception e) {
            logger.error("Failed to apply quality improvements", e);
            return generatedCode; // Return original if optimization fails
        }
    }
    
    private void prepareAgentIntegration(
            AgentSpecification agentSpec,
            GeneratedCodeResult generatedCode,
            DeploymentConfiguration deploymentConfig
    ) {
        try {
            agentIntegrationService.prepareIntegration(agentSpec, generatedCode, deploymentConfig);
        } catch (Exception e) {
            logger.error("Failed to prepare agent integration", e);
            // Non-blocking error, log and continue
        }
    }
    
    private void updateGenerationStatistics(long durationMs, double qualityScore, GeneratedCodeResult generatedCode) {
        totalAgentsGenerated++;
        
        if (qualityScore > 0.7) { // Consider successful if quality > 70%
            successfulGenerations++;
        }
        
        // Update averages
        averageGenerationTimeMs = (averageGenerationTimeMs * (totalAgentsGenerated - 1) + durationMs) / totalAgentsGenerated;
        averageQualityScore = (averageQualityScore * (totalAgentsGenerated - 1) + qualityScore) / totalAgentsGenerated;
        
        // Count lines of code
        totalCodeLinesGenerated += estimateCodeLines(generatedCode);
    }
    
    private long estimateCodeLines(GeneratedCodeResult generatedCode) {
        // Simple estimation based on generated files
        long lines = 0;
        
        if (generatedCode.pythonCode() != null) {
            lines += countLinesInPythonCode(generatedCode.pythonCode());
        }
        if (generatedCode.javaCode() != null) {
            lines += countLinesInJavaCode(generatedCode.javaCode());
        }
        if (generatedCode.typeScriptCode() != null) {
            lines += countLinesInTypeScriptCode(generatedCode.typeScriptCode());
        }
        if (generatedCode.goCode() != null) {
            lines += countLinesInGoCode(generatedCode.goCode());
        }
        
        return lines;
    }
    
    private long countLinesInPythonCode(PythonCodeResult pythonCode) {
        return countLinesInFile(pythonCode.mainApplication()) +
               countLinesInFile(pythonCode.agentImplementation()) +
               pythonCode.aiIntegration().stream().mapToLong(this::countLinesInFile).sum() +
               pythonCode.apiEndpoints().stream().mapToLong(this::countLinesInFile).sum() +
               pythonCode.dataModels().stream().mapToLong(this::countLinesInFile).sum();
    }
    
    private long countLinesInJavaCode(JavaCodeResult javaCode) {
        return countLinesInFile(javaCode.mainApplication()) +
               countLinesInFile(javaCode.agentImplementation()) +
               javaCode.controllers().stream().mapToLong(this::countLinesInFile).sum() +
               javaCode.services().stream().mapToLong(this::countLinesInFile).sum() +
               javaCode.models().stream().mapToLong(this::countLinesInFile).sum();
    }
    
    private long countLinesInTypeScriptCode(TypeScriptCodeResult tsCode) {
        return countLinesInFile(tsCode.mainApplication()) +
               countLinesInFile(tsCode.agentImplementation()) +
               tsCode.components().stream().mapToLong(this::countLinesInFile).sum() +
               tsCode.services().stream().mapToLong(this::countLinesInFile).sum();
    }
    
    private long countLinesInGoCode(GoCodeResult goCode) {
        return countLinesInFile(goCode.mainApplication()) +
               countLinesInFile(goCode.agentImplementation()) +
               goCode.handlers().stream().mapToLong(this::countLinesInFile).sum() +
               goCode.services().stream().mapToLong(this::countLinesInFile).sum();
    }
    
    private long countLinesInFile(GeneratedFile file) {
        if (file == null || file.content() == null) return 0;
        return file.content().split("\n").length;
    }
    
    private double calculateSuccessRate() {
        if (totalAgentsGenerated == 0) return 0.0;
        return (double) successfulGenerations / totalAgentsGenerated * 100.0;
    }
    
    private GenerationMetadata createGenerationMetadata(
            String requestId,
            List<AgentTemplate> templates,
            List<AIInsight> aiInsights,
            long totalDuration
    ) {
        return new GenerationMetadata(
            UUID.randomUUID().toString(),
            List.of(), // Phase results would be populated by orchestration service
            aiInsights.stream().map(AIInsight::aiModelUsed).distinct().toList(),
            templates.stream().map(AgentTemplate::templateId).toList(),
            aiInsights.stream().mapToInt(AIInsight::tokensUsed).sum(),
            aiInsights.stream().mapToDouble(AIInsight::costEstimation).sum(),
            new GenerationStatistics(
                0, // Will be calculated based on generated files
                (int) totalCodeLinesGenerated,
                List.of(ProgrammingLanguage.PYTHON, ProgrammingLanguage.JAVA),
                templates.size(),
                0 // Optimizations count
            ),
            0, // Error count
            0, // Warning count
            List.of() // Optimizations applied
        );
    }
    
    private QualityReport createErrorQualityReport(String errorMessage) {
        return new QualityReport(
            UUID.randomUUID().toString(),
            "ERROR",
            0.0,
            false,
            new CodeQualityMetrics(0, 0, 0, 0, 0, 1, 0, 0, 0, 0),
            null, null, null, null, null,
            List.of(new ImprovementSuggestion(
                "ERROR_RECOVERY",
                "Generation failed: " + errorMessage,
                SuggestionPriority.CRITICAL,
                "Immediate",
                "Fix blocking issue"
            )),
            Instant.now()
        );
    }
    
    private GenerationMetadata createErrorMetadata(String requestId, long duration) {
        return new GenerationMetadata(
            UUID.randomUUID().toString(),
            List.of(),
            List.of(),
            List.of(),
            0, 0.0,
            new GenerationStatistics(0, 0, List.of(), 0, 0),
            1, 0, List.of()
        );
    }
    
    private void storeGenerationResult(AgentGenerationResponse response) {
        try {
            monitoringService.recordGenerationResult(response);
        } catch (Exception e) {
            logger.error("Failed to store generation result", e);
            // Non-blocking error
        }
    }
    
    private void startHealthMonitoring() {
        // Implementation would start a background thread for health monitoring
        logger.info("Health monitoring started for Agent Factory Agent");
    }
    
    // ===== Supporting Record Types =====
    
    public record AgentCapabilities(
        String agentId,
        String agentType,
        List<String> capabilities,
        List<ProgrammingLanguage> supportedLanguages,
        List<TemplateType> supportedTemplates,
        GenerationCapacityMetrics capacityMetrics,
        String availabilityStatus,
        Instant lastUpdated
    ) {}
    
    public record GenerationCapacityMetrics(
        int agentsPerHour,
        double successRate,
        double averageGenerationTimeMs,
        double averageQualityScore
    ) {}
    
    public record GenerationProgress(
        String requestId,
        GenerationStatus status,
        GenerationPhase currentPhase,
        double completionPercentage,
        List<GenerationPhaseResult> completedPhases,
        String currentActivity,
        long estimatedRemainingTimeMs,
        List<GenerationError> errors,
        Instant lastUpdated
    ) {}
    
    public record DeploymentResult(
        String deploymentId,
        String generationId,
        DeploymentStatus status,
        String agentEndpoint,
        Map<String, String> connectionInfo,
        List<String> healthCheckUrls,
        Instant deployedAt
    ) {}
    
    // ===== Agent Status Enum =====
    
    public enum AgentStatus {
        INITIALIZING, ACTIVE, DEGRADED, SHUTTING_DOWN, STOPPED, ERROR
    }
    
    public enum DeploymentStatus {
        PENDING, IN_PROGRESS, COMPLETED, FAILED, ROLLED_BACK
    }
}