package ai.twodot.metaagent.platform.agents.dataprocessing.orchestration;

import ai.twodot.metaagent.platform.agents.dataprocessing.agent.DataProcessingAgent;
import ai.twodot.metaagent.platform.agents.dataprocessing.data.DataModels;
import org.springframework.stereotype.Service;

@Service
public class OrchestrationServiceImpl implements DataProcessingAgent.DataPipelineOrchestrator {
    @Override
    public DataModels.ProcessingResult executePipeline(DataModels.DataPipeline pipeline) {
        return null;
    }

    @Override
    public boolean isHealthy() {
        return true;
    }
}
