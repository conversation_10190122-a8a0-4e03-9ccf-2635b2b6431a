package ai.twodot.metaagent.platform.facade;

import ai.twodot.metaagent.platform.facade.communication.CommunicationFacade;
import ai.twodot.metaagent.platform.facade.monitoring.MonitoringFacade;
import ai.twodot.metaagent.platform.facade.orchestration.OrchestrationFacade;
import ai.twodot.metaagent.platform.facade.registry.AgentRegistryFacade;

public interface AgentFacade {
    
    AgentRegistryFacade getRegistry();
    
    CommunicationFacade getCommunication();
    
    OrchestrationFacade getOrchestration();
    
    MonitoringFacade getMonitoring();
}