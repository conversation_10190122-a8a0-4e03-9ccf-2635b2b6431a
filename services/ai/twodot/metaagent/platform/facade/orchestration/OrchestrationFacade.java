package ai.twodot.metaagent.platform.facade.orchestration;

import ai.twodot.metaagent.platform.core.models.enums.Priority;
import ai.twodot.metaagent.platform.core.models.enums.Status;

import java.time.Instant;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

public interface OrchestrationFacade {
    
    CompletableFuture<TaskResult> executeTask(Task task);
    
    CompletableFuture<TaskResult> executeWorkflow(Workflow workflow);
    
    TaskResult getTaskResult(String taskId);
    
    boolean cancelTask(String taskId);
    
    List<TaskResult> getTasksByStatus(Status status);
    
    List<TaskResult> getActiveTasksForAgent(String agentId);
    
    WorkflowResult getWorkflowResult(String workflowId);
    
    boolean cancelWorkflow(String workflowId);
    
    class Task {
        private final String taskId;
        private final String taskType;
        private final Object input;
        private final Set<String> requiredAgents;
        private final Priority priority;
        private final Instant deadline;
        
        public Task(String taskId, String taskType, Object input, Set<String> requiredAgents, Priority priority, Instant deadline) {
            this.taskId = taskId;
            this.taskType = taskType;
            this.input = input;
            this.requiredAgents = requiredAgents;
            this.priority = priority;
            this.deadline = deadline;
        }
        
        public String getTaskId() {
            return taskId;
        }
        
        public String getTaskType() {
            return taskType;
        }
        
        public Object getInput() {
            return input;
        }
        
        public Set<String> getRequiredAgents() {
            return requiredAgents;
        }
        
        public Priority getPriority() {
            return priority;
        }
        
        public Instant getDeadline() {
            return deadline;
        }
    }
    
    class TaskResult {
        private final String taskId;
        private final Status status;
        private final Object result;
        private final String errorMessage;
        private final Instant startTime;
        private final Instant endTime;
        
        public TaskResult(String taskId, Status status, Object result, String errorMessage, Instant startTime, Instant endTime) {
            this.taskId = taskId;
            this.status = status;
            this.result = result;
            this.errorMessage = errorMessage;
            this.startTime = startTime;
            this.endTime = endTime;
        }
        
        public String getTaskId() {
            return taskId;
        }
        
        public Status getStatus() {
            return status;
        }
        
        public Object getResult() {
            return result;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        public Instant getStartTime() {
            return startTime;
        }
        
        public Instant getEndTime() {
            return endTime;
        }
    }
    
    class Workflow {
        private final String workflowId;
        private final String workflowType;
        private final List<Task> tasks;
        private final Priority priority;
        
        public Workflow(String workflowId, String workflowType, List<Task> tasks, Priority priority) {
            this.workflowId = workflowId;
            this.workflowType = workflowType;
            this.tasks = tasks;
            this.priority = priority;
        }
        
        public String getWorkflowId() {
            return workflowId;
        }
        
        public String getWorkflowType() {
            return workflowType;
        }
        
        public List<Task> getTasks() {
            return tasks;
        }
        
        public Priority getPriority() {
            return priority;
        }
    }
    
    class WorkflowResult {
        private final String workflowId;
        private final Status status;
        private final List<TaskResult> taskResults;
        private final Instant startTime;
        private final Instant endTime;
        
        public WorkflowResult(String workflowId, Status status, List<TaskResult> taskResults, Instant startTime, Instant endTime) {
            this.workflowId = workflowId;
            this.status = status;
            this.taskResults = taskResults;
            this.startTime = startTime;
            this.endTime = endTime;
        }
        
        public String getWorkflowId() {
            return workflowId;
        }
        
        public Status getStatus() {
            return status;
        }
        
        public List<TaskResult> getTaskResults() {
            return taskResults;
        }
        
        public Instant getStartTime() {
            return startTime;
        }
        
        public Instant getEndTime() {
            return endTime;
        }
    }
}