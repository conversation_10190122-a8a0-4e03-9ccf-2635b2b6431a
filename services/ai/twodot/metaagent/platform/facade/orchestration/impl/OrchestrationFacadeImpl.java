package ai.twodot.metaagent.platform.facade.orchestration.impl;

import ai.twodot.metaagent.platform.core.models.enums.Status;
import ai.twodot.metaagent.platform.facade.orchestration.OrchestrationFacade;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

@Component
public class OrchestrationFacadeImpl implements OrchestrationFacade {
    
    private final ConcurrentMap<String, TaskResult> taskResults = new ConcurrentHashMap<>();
    private final ConcurrentMap<String, WorkflowResult> workflowResults = new ConcurrentHashMap<>();
    
    @Override
    public CompletableFuture<TaskResult> executeTask(Task task) {
        return CompletableFuture.supplyAsync(() -> {
            // Simulate task execution
            try {
                Thread.sleep(100); // Simulate work
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            
            TaskResult result = new TaskResult(
                task.getTaskId(),
                Status.COMPLETED,
                "Task executed successfully",
                null,
                Instant.now().minusSeconds(1),
                Instant.now()
            );
            
            taskResults.put(task.getTaskId(), result);
            return result;
        });
    }
    
    @Override
    public CompletableFuture<TaskResult> executeWorkflow(Workflow workflow) {
        return CompletableFuture.supplyAsync(() -> {
            // Execute all tasks in the workflow
            List<CompletableFuture<TaskResult>> taskFutures = workflow.getTasks().stream()
                    .map(this::executeTask)
                    .toList();
            
            // Wait for all tasks to complete
            CompletableFuture<Void> allTasks = CompletableFuture.allOf(
                taskFutures.toArray(new CompletableFuture[0])
            );
            
            try {
                allTasks.get();
            } catch (Exception e) {
                return new TaskResult(
                    workflow.getWorkflowId(),
                    Status.FAILED,
                    null,
                    "Workflow execution failed: " + e.getMessage(),
                    Instant.now().minusSeconds(5),
                    Instant.now()
                );
            }
            
            // Create workflow result
            List<TaskResult> results = taskFutures.stream()
                    .map(CompletableFuture::join)
                    .toList();
            
            WorkflowResult workflowResult = new WorkflowResult(
                workflow.getWorkflowId(),
                Status.COMPLETED,
                results,
                Instant.now().minusSeconds(5),
                Instant.now()
            );
            
            workflowResults.put(workflow.getWorkflowId(), workflowResult);
            
            return new TaskResult(
                workflow.getWorkflowId(),
                Status.COMPLETED,
                workflowResult,
                null,
                Instant.now().minusSeconds(5),
                Instant.now()
            );
        });
    }
    
    @Override
    public TaskResult getTaskResult(String taskId) {
        return taskResults.get(taskId);
    }
    
    @Override
    public boolean cancelTask(String taskId) {
        TaskResult result = taskResults.get(taskId);
        if (result != null && result.getStatus() == Status.PENDING) {
            taskResults.put(taskId, new TaskResult(
                taskId,
                Status.CANCELLED,
                null,
                "Task cancelled",
                result.getStartTime(),
                Instant.now()
            ));
            return true;
        }
        return false;
    }
    
    @Override
    public List<TaskResult> getTasksByStatus(Status status) {
        return taskResults.values().stream()
                .filter(result -> result.getStatus() == status)
                .toList();
    }
    
    @Override
    public List<TaskResult> getActiveTasksForAgent(String agentId) {
        return taskResults.values().stream()
                .filter(result -> result.getStatus() == Status.PENDING || result.getStatus() == Status.IN_PROGRESS)
                .toList();
    }
    
    @Override
    public WorkflowResult getWorkflowResult(String workflowId) {
        return workflowResults.get(workflowId);
    }
    
    @Override
    public boolean cancelWorkflow(String workflowId) {
        WorkflowResult result = workflowResults.get(workflowId);
        if (result != null && result.getStatus() == Status.PENDING) {
            workflowResults.put(workflowId, new WorkflowResult(
                workflowId,
                Status.CANCELLED,
                result.getTaskResults(),
                result.getStartTime(),
                Instant.now()
            ));
            return true;
        }
        return false;
    }
}