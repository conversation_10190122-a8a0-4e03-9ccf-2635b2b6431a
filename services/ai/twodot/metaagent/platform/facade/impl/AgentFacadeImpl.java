package ai.twodot.metaagent.platform.facade.impl;

import ai.twodot.metaagent.platform.facade.AgentFacade;
import ai.twodot.metaagent.platform.facade.communication.CommunicationFacade;
import ai.twodot.metaagent.platform.facade.monitoring.MonitoringFacade;
import ai.twodot.metaagent.platform.facade.orchestration.OrchestrationFacade;
import ai.twodot.metaagent.platform.facade.registry.AgentRegistryFacade;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class AgentFacadeImpl implements AgentFacade {
    
    @Autowired
    private AgentRegistryFacade registryFacade;
    
    @Autowired
    private CommunicationFacade communicationFacade;
    
    @Autowired
    private OrchestrationFacade orchestrationFacade;
    
    @Autowired
    private MonitoringFacade monitoringFacade;
    
    @Override
    public AgentRegistryFacade getRegistry() {
        return registryFacade;
    }
    
    @Override
    public CommunicationFacade getCommunication() {
        return communicationFacade;
    }
    
    @Override
    public OrchestrationFacade getOrchestration() {
        return orchestrationFacade;
    }
    
    @Override
    public MonitoringFacade getMonitoring() {
        return monitoringFacade;
    }
}