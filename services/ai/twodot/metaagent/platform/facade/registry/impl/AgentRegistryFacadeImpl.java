package ai.twodot.metaagent.platform.facade.registry.impl;

import ai.twodot.metaagent.platform.core.models.MetricsData;
import ai.twodot.metaagent.platform.facade.registry.AgentRegistryFacade;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

@Component
public class AgentRegistryFacadeImpl implements AgentRegistryFacade {
    
    private final ConcurrentMap<String, ServiceInfo> services = new ConcurrentHashMap<>();
    
    @Override
    public List<ServiceInfo> discoverServices() {
        return List.copyOf(services.values());
    }
    
    @Override
    public Optional<ServiceInfo> discoverService(String serviceType) {
        return services.values().stream()
                .filter(service -> service.getServiceType().equals(serviceType))
                .findFirst();
    }
    
    @Override
    public List<ServiceInfo> discoverServicesByCapability(String capability) {
        return services.values().stream()
                .filter(service -> service.getCapabilities().contains(capability))
                .toList();
    }
    
    @Override
    public boolean registerService(ServiceInfo serviceInfo) {
        services.put(serviceInfo.getServiceId(), serviceInfo);
        return true;
    }
    
    @Override
    public boolean unregisterService(String serviceId) {
        return services.remove(serviceId) != null;
    }
    
    @Override
    public Optional<ServiceInfo> getServiceInfo(String serviceId) {
        return Optional.ofNullable(services.get(serviceId));
    }
    
    @Override
    public List<ServiceInfo> getHealthyServices() {
        return services.values().stream()
                .filter(ServiceInfo::isHealthy)
                .toList();
    }
    
    @Override
    public List<ServiceInfo> getServicesByType(String serviceType) {
        return services.values().stream()
                .filter(service -> service.getServiceType().equals(serviceType))
                .toList();
    }
    
    @Override
    public boolean isServiceHealthy(String serviceId) {
        return services.containsKey(serviceId) && services.get(serviceId).isHealthy();
    }
    
    @Override
    public MetricsData getServiceMetrics(String serviceId) {
        // Simple implementation - return basic metrics
        MetricsData metrics = new MetricsData();
        metrics.setAgentId(serviceId);
        metrics.setAgentType("service");
        metrics.setHealthy(true);
        metrics.addMetric("status", "UP");
        metrics.addMetric("responseTime", 45.2);
        return metrics;
    }
}