package ai.twodot.metaagent.platform.facade.registry;

import ai.twodot.metaagent.platform.core.models.MetricsData;

import java.util.List;
import java.util.Optional;

public interface AgentRegistryFacade {
    
    List<ServiceInfo> discoverServices();
    
    Optional<ServiceInfo> discoverService(String serviceType);
    
    List<ServiceInfo> discoverServicesByCapability(String capability);
    
    boolean registerService(ServiceInfo serviceInfo);
    
    boolean unregisterService(String serviceId);
    
    Optional<ServiceInfo> getServiceInfo(String serviceId);
    
    List<ServiceInfo> getHealthyServices();
    
    List<ServiceInfo> getServicesByType(String serviceType);
    
    boolean isServiceHealthy(String serviceId);
    
    MetricsData getServiceMetrics(String serviceId);
    
    class ServiceInfo {
        private final String serviceId;
        private final String serviceType;
        private final String endpoint;
        private final List<String> capabilities;
        private final boolean healthy;
        
        public ServiceInfo(String serviceId, String serviceType, String endpoint, List<String> capabilities, boolean healthy) {
            this.serviceId = serviceId;
            this.serviceType = serviceType;
            this.endpoint = endpoint;
            this.capabilities = capabilities;
            this.healthy = healthy;
        }
        
        public String getServiceId() {
            return serviceId;
        }
        
        public String getServiceType() {
            return serviceType;
        }
        
        public String getEndpoint() {
            return endpoint;
        }
        
        public List<String> getCapabilities() {
            return capabilities;
        }
        
        public boolean isHealthy() {
            return healthy;
        }
    }
}