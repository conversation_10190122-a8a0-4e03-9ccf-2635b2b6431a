package ai.twodot.metaagent.platform.facade.communication;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Inter-Agent Communication Facade
 * 
 * Provides standardized communication interface between agents.
 * This facade abstracts the communication mechanism and ensures
 * seamless transition from monolith to microservices architecture.
 */
public interface InterAgentCommunicationFacade {
    
    // Security Monitor Agent Communications
    CompletableFuture<SecurityThreatAlert> reportSecurityThreat(String sourceAgentId, SecurityThreatData threatData);
    CompletableFuture<SecurityValidationResult> validateSecurityPolicy(String requestingAgentId, SecurityPolicyRequest request);
    CompletableFuture<IncidentResponse> handleSecurityIncident(String sourceAgentId, SecurityIncident incident);
    
    // Resource Manager Agent Communications
    CompletableFuture<ResourceAllocationResponse> requestResourceAllocation(String requestingAgentId, ResourceAllocationRequest request);
    CompletableFuture<ScalingDecision> requestAutoScaling(String serviceId, ScalingTrigger trigger);
    CompletableFuture<CostOptimizationResult> requestCostOptimization(String serviceId);
    
    // Data Processing Agent Communications
    CompletableFuture<DataProcessingResult> processData(String requestingAgentId, DataProcessingRequest request);
    CompletableFuture<DataQualityReport> validateDataQuality(String dataSourceId, DataQualityRequest request);
    CompletableFuture<StreamProcessingStatus> configureStreamProcessing(String requestingAgentId, StreamConfig config);
    
    // Knowledge Base Agent Communications
    CompletableFuture<KnowledgeExtractionResult> extractKnowledge(String requestingAgentId, KnowledgeExtractionRequest request);
    CompletableFuture<SemanticSearchResult> searchKnowledge(String requestingAgentId, SemanticSearchQuery query);
    CompletableFuture<RecommendationResult> getRecommendations(String requestingAgentId, RecommendationRequest request);
    
    // Task Orchestrator Agent Communications
    CompletableFuture<TaskDistributionResult> distributeTask(String requestingAgentId, TaskDistributionRequest request);
    CompletableFuture<WorkflowExecutionResult> executeWorkflow(String requestingAgentId, WorkflowDefinition workflow);
    CompletableFuture<SchedulingResult> scheduleTask(String requestingAgentId, TaskSchedulingRequest request);
    
    // Agent Factory Communications
    CompletableFuture<AgentGenerationResult> generateAgent(String requestingAgentId, AgentGenerationRequest request);
    CompletableFuture<DeploymentResult> deployAgent(String requestingAgentId, AgentDeploymentRequest request);
    CompletableFuture<AgentValidationResult> validateAgent(String requestingAgentId, AgentValidationRequest request);
    
    // Supreme Intelligence Agent Communications
    CompletableFuture<IntelligenceAnalysisResult> requestIntelligenceAnalysis(String requestingAgentId, IntelligenceRequest request);
    CompletableFuture<PlatformOptimizationResult> optimizePlatform(String requestingAgentId, OptimizationRequest request);
    CompletableFuture<StrategicDecisionResult> requestStrategicDecision(String requestingAgentId, StrategicDecisionRequest request);
    
    // Discovery and Registry Communications  
    CompletableFuture<ServiceRegistrationResult> registerService(String agentId, ServiceDefinition service);
    CompletableFuture<ServiceDiscoveryResult> discoverService(String requestingAgentId, ServiceDiscoveryQuery query);
    CompletableFuture<HealthCheckResult> performHealthCheck(String targetAgentId);
    
    // Generic message passing for extensibility
    CompletableFuture<GenericResponse> sendMessage(String sourceAgentId, String targetAgentId, String messageType, Map<String, Object> payload);
    
    // Record classes for communication data
    record SecurityThreatData(String threatId, String threatType, String severity, Map<String, Object> details) {}
    record SecurityThreatAlert(String alertId, String status, String message) {}
    record SecurityValidationResult(boolean valid, String reason, Map<String, String> violations) {}
    record SecurityPolicyRequest(String policyId, Map<String, Object> context) {}
    record SecurityIncident(String incidentId, String type, String severity, Map<String, Object> details) {}
    record IncidentResponse(String responseId, String action, String status) {}
    
    record ResourceAllocationRequest(String allocationId, String serviceId, Map<String, Object> requirements) {}
    record ResourceAllocationResponse(boolean success, String allocationId, Map<String, Object> allocatedResources) {}
    record ScalingTrigger(String type, Map<String, Object> metrics) {}
    record ScalingDecision(String serviceId, int currentInstances, int targetInstances, String reason) {}
    record CostOptimizationResult(String serviceId, double potentialSavings, Map<String, Object> recommendations) {}
    
    record DataProcessingRequest(String requestId, String dataSourceId, String processingType, Map<String, Object> parameters) {}
    record DataProcessingResult(String requestId, boolean success, Map<String, Object> results) {}
    record DataQualityRequest(String sourceId, Map<String, Object> qualityRules) {}
    record DataQualityReport(String sourceId, double qualityScore, Map<String, Object> issues) {}
    record StreamConfig(String streamId, Map<String, Object> configuration) {}
    record StreamProcessingStatus(String streamId, String status, Map<String, Object> metrics) {}
    
    record KnowledgeExtractionRequest(String requestId, String sourceType, String content) {}
    record KnowledgeExtractionResult(String requestId, boolean success, Map<String, Object> extractedKnowledge) {}
    record SemanticSearchQuery(String query, Map<String, Object> filters) {}
    record SemanticSearchResult(String queryId, java.util.List<Map<String, Object>> results) {}
    record RecommendationRequest(String requestId, String userId, String context, Map<String, Object> preferences) {}
    record RecommendationResult(String requestId, java.util.List<Map<String, Object>> recommendations) {}
    
    record TaskDistributionRequest(String taskId, String taskType, Map<String, Object> taskData) {}
    record TaskDistributionResult(String taskId, String assignedAgent, String status) {}
    record WorkflowDefinition(String workflowId, java.util.List<Map<String, Object>> steps) {}
    record WorkflowExecutionResult(String workflowId, String status, Map<String, Object> results) {}
    record TaskSchedulingRequest(String taskId, String scheduleExpression, Map<String, Object> taskConfig) {}
    record SchedulingResult(String taskId, String scheduleId, String status) {}
    
    record AgentGenerationRequest(String requestId, String agentType, Map<String, Object> specifications) {}
    record AgentGenerationResult(String requestId, boolean success, String generatedAgentId) {}
    record AgentDeploymentRequest(String agentId, String environment, Map<String, Object> deploymentConfig) {}
    record DeploymentResult(String deploymentId, String status, String endpoint) {}
    record AgentValidationRequest(String agentId, Map<String, Object> validationCriteria) {}
    record AgentValidationResult(String agentId, boolean valid, Map<String, String> validationResults) {}
    
    record IntelligenceRequest(String requestId, String analysisType, Map<String, Object> data) {}
    record IntelligenceAnalysisResult(String requestId, Map<String, Object> insights, Map<String, Object> predictions) {}
    record OptimizationRequest(String requestId, String scope, Map<String, Object> currentMetrics) {}
    record PlatformOptimizationResult(String requestId, Map<String, Object> optimizations, double expectedImpact) {}
    record StrategicDecisionRequest(String requestId, String decisionType, Map<String, Object> context) {}
    record StrategicDecisionResult(String requestId, String decision, String reasoning, double confidence) {}
    
    record ServiceDefinition(String serviceId, String serviceName, String endpoint, Map<String, Object> metadata) {}
    record ServiceRegistrationResult(String serviceId, String registrationId, String status) {}
    record ServiceDiscoveryQuery(String serviceType, Map<String, Object> criteria) {}
    record ServiceDiscoveryResult(String queryId, java.util.List<ServiceDefinition> services) {}
    record HealthCheckResult(String agentId, String status, Map<String, Object> healthMetrics) {}
    
    record GenericResponse(String messageId, boolean success, Map<String, Object> data) {}
}