package ai.twodot.metaagent.platform.facade.communication;

import ai.twodot.metaagent.platform.core.integration.a2a.models.A2AMessage;
import ai.twodot.metaagent.platform.core.integration.a2a.models.A2AResponse;
import ai.twodot.metaagent.platform.core.models.enums.MessageType;

import java.util.concurrent.CompletableFuture;

public interface CommunicationFacade {
    
    CompletableFuture<A2AResponse> sendMessage(String targetAgentId, MessageType type, Object payload);
    
    CompletableFuture<A2AResponse> sendRequest(String targetAgentId, Object request);
    
    void sendNotification(String targetAgentId, Object notification);
    
    void broadcast(MessageType type, Object payload);
    
    void registerMessageHandler(MessageType type, MessageHandler handler);
    
    void unregisterMessageHandler(MessageType type);
    
    CompletableFuture<A2AResponse> query(String targetAgentId, String query);
    
    void publishEvent(String eventType, Object eventData);
    
    void subscribeToEvents(String eventType, EventHandler handler);
    
    void unsubscribeFromEvents(String eventType);
    
    interface MessageHandler {
        A2AResponse handleMessage(A2AMessage message);
    }
    
    interface EventHandler {
        void handleEvent(String eventType, Object eventData);
    }
}