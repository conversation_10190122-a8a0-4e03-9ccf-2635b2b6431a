package ai.twodot.metaagent.platform.facade.monitoring;

import ai.twodot.metaagent.platform.core.models.MetricsData;

import java.time.Instant;
import java.util.List;
import java.util.Map;

public interface MonitoringFacade {
    
    PlatformHealth getOverallHealth();
    
    AgentHealth getAgentHealth(String agentId);
    
    List<AgentHealth> getAllAgentHealth();
    
    List<AgentHealth> getUnhealthyAgents();
    
    MetricsData getAgentMetrics(String agentId);
    
    List<MetricsData> getAllAgentMetrics();
    
    PlatformMetrics getPlatformMetrics();
    
    List<Alert> getActiveAlerts();
    
    List<Alert> getAlertsForAgent(String agentId);
    
    void acknowledgeAlert(String alertId);
    
    PerformanceReport getPerformanceReport();
    
    class PlatformHealth {
        private final boolean healthy;
        private final int totalAgents;
        private final int healthyAgents;
        private final int unhealthyAgents;
        private final Instant timestamp;
        
        public PlatformHealth(boolean healthy, int totalAgents, int healthyAgents, int unhealthyAgents, Instant timestamp) {
            this.healthy = healthy;
            this.totalAgents = totalAgents;
            this.healthyAgents = healthyAgents;
            this.unhealthyAgents = unhealthyAgents;
            this.timestamp = timestamp;
        }
        
        public boolean isHealthy() {
            return healthy;
        }
        
        public int getTotalAgents() {
            return totalAgents;
        }
        
        public int getHealthyAgents() {
            return healthyAgents;
        }
        
        public int getUnhealthyAgents() {
            return unhealthyAgents;
        }
        
        public Instant getTimestamp() {
            return timestamp;
        }
    }
    
    class AgentHealth {
        private final String agentId;
        private final String agentType;
        private final boolean healthy;
        private final String status;
        private final Instant lastCheckTime;
        private final String errorMessage;
        
        public AgentHealth(String agentId, String agentType, boolean healthy, String status, Instant lastCheckTime, String errorMessage) {
            this.agentId = agentId;
            this.agentType = agentType;
            this.healthy = healthy;
            this.status = status;
            this.lastCheckTime = lastCheckTime;
            this.errorMessage = errorMessage;
        }
        
        public String getAgentId() {
            return agentId;
        }
        
        public String getAgentType() {
            return agentType;
        }
        
        public boolean isHealthy() {
            return healthy;
        }
        
        public String getStatus() {
            return status;
        }
        
        public Instant getLastCheckTime() {
            return lastCheckTime;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
    }
    
    class PlatformMetrics {
        private final Map<String, Object> metrics;
        private final Instant timestamp;
        
        public PlatformMetrics(Map<String, Object> metrics, Instant timestamp) {
            this.metrics = metrics;
            this.timestamp = timestamp;
        }
        
        public Map<String, Object> getMetrics() {
            return metrics;
        }
        
        public Instant getTimestamp() {
            return timestamp;
        }
    }
    
    class Alert {
        private final String alertId;
        private final String agentId;
        private final String severity;
        private final String message;
        private final Instant timestamp;
        private final boolean acknowledged;
        
        public Alert(String alertId, String agentId, String severity, String message, Instant timestamp, boolean acknowledged) {
            this.alertId = alertId;
            this.agentId = agentId;
            this.severity = severity;
            this.message = message;
            this.timestamp = timestamp;
            this.acknowledged = acknowledged;
        }
        
        public String getAlertId() {
            return alertId;
        }
        
        public String getAgentId() {
            return agentId;
        }
        
        public String getSeverity() {
            return severity;
        }
        
        public String getMessage() {
            return message;
        }
        
        public Instant getTimestamp() {
            return timestamp;
        }
        
        public boolean isAcknowledged() {
            return acknowledged;
        }
    }
    
    class PerformanceReport {
        private final Map<String, Double> avgResponseTimes;
        private final Map<String, Long> requestCounts;
        private final Map<String, Double> errorRates;
        private final Instant reportTime;
        
        public PerformanceReport(Map<String, Double> avgResponseTimes, Map<String, Long> requestCounts, Map<String, Double> errorRates, Instant reportTime) {
            this.avgResponseTimes = avgResponseTimes;
            this.requestCounts = requestCounts;
            this.errorRates = errorRates;
            this.reportTime = reportTime;
        }
        
        public Map<String, Double> getAvgResponseTimes() {
            return avgResponseTimes;
        }
        
        public Map<String, Long> getRequestCounts() {
            return requestCounts;
        }
        
        public Map<String, Double> getErrorRates() {
            return errorRates;
        }
        
        public Instant getReportTime() {
            return reportTime;
        }
    }
}