package ai.twodot.metaagent.platform;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@ComponentScan(basePackages = {
    "com.multiagent.platform"
})
@EnableJpaRepositories
@EnableAsync
@EnableScheduling
public class MetaAgentPlatformApplication {

    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("🚀 Starting Meta-Agent Platform");
        System.out.println("========================================");
        
        SpringApplication.run(MetaAgentPlatformApplication.class, args);
        
        System.out.println("========================================");
        System.out.println("✅ Meta-Agent Platform Started Successfully");
        System.out.println("📊 Platform running on port 8080");
        System.out.println("🩺 Health check: http://localhost:8080/health");
        System.out.println("📈 Metrics: http://localhost:8081/metrics");
        System.out.println("========================================");
    }
}