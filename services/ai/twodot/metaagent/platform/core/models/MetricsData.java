package ai.twodot.metaagent.platform.core.models;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

public class MetricsData {
    private String agentId;
    private String agentType;
    private Instant timestamp;
    private boolean healthy;
    private Map<String, Object> metrics = new HashMap<>();
    
    public String getAgentId() {
        return agentId;
    }
    
    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }
    
    public String getAgentType() {
        return agentType;
    }
    
    public void setAgentType(String agentType) {
        this.agentType = agentType;
    }
    
    public Instant getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(Instant timestamp) {
        this.timestamp = timestamp;
    }
    
    public boolean isHealthy() {
        return healthy;
    }
    
    public void setHealthy(boolean healthy) {
        this.healthy = healthy;
    }
    
    public Map<String, Object> getMetrics() {
        return metrics;
    }
    
    public void setMetrics(Map<String, Object> metrics) {
        this.metrics = metrics;
    }
    
    public void addMetric(String key, Object value) {
        this.metrics.put(key, value);
    }
}