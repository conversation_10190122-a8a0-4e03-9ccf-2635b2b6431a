package ai.twodot.metaagent.platform.core.integration.a2a.models;

import ai.twodot.metaagent.platform.core.models.enums.MessageType;
import ai.twodot.metaagent.platform.core.models.enums.Priority;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class A2AMessage {
    private final String messageId;
    private final String sourceAgentId;
    private final String targetAgentId;
    private final MessageType messageType;
    private final Priority priority;
    private final Object payload;
    private final Instant timestamp;
    private final Map<String, String> metadata;
    
    private A2AMessage(Builder builder) {
        this.messageId = builder.messageId != null ? builder.messageId : UUID.randomUUID().toString();
        this.sourceAgentId = builder.sourceAgentId;
        this.targetAgentId = builder.targetAgentId;
        this.messageType = builder.messageType;
        this.priority = builder.priority != null ? builder.priority : Priority.MEDIUM;
        this.payload = builder.payload;
        this.timestamp = builder.timestamp != null ? builder.timestamp : Instant.now();
        this.metadata = new HashMap<>(builder.metadata);
    }
    
    public String getMessageId() {
        return messageId;
    }
    
    public String getSourceAgentId() {
        return sourceAgentId;
    }
    
    public String getTargetAgentId() {
        return targetAgentId;
    }
    
    public MessageType getMessageType() {
        return messageType;
    }
    
    public Priority getPriority() {
        return priority;
    }
    
    public Object getPayload() {
        return payload;
    }
    
    public Instant getTimestamp() {
        return timestamp;
    }
    
    public Map<String, String> getMetadata() {
        return new HashMap<>(metadata);
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    public static class Builder {
        private String messageId;
        private String sourceAgentId;
        private String targetAgentId;
        private MessageType messageType;
        private Priority priority;
        private Object payload;
        private Instant timestamp;
        private Map<String, String> metadata = new HashMap<>();
        
        public Builder messageId(String messageId) {
            this.messageId = messageId;
            return this;
        }
        
        public Builder from(String sourceAgentId) {
            this.sourceAgentId = sourceAgentId;
            return this;
        }
        
        public Builder to(String targetAgentId) {
            this.targetAgentId = targetAgentId;
            return this;
        }
        
        public Builder type(MessageType messageType) {
            this.messageType = messageType;
            return this;
        }
        
        public Builder priority(Priority priority) {
            this.priority = priority;
            return this;
        }
        
        public Builder payload(Object payload) {
            this.payload = payload;
            return this;
        }
        
        public Builder timestamp(Instant timestamp) {
            this.timestamp = timestamp;
            return this;
        }
        
        public Builder metadata(String key, String value) {
            this.metadata.put(key, value);
            return this;
        }
        
        public Builder metadata(Map<String, String> metadata) {
            this.metadata.putAll(metadata);
            return this;
        }
        
        public A2AMessage build() {
            if (sourceAgentId == null || targetAgentId == null || messageType == null || payload == null) {
                throw new IllegalStateException("Required fields: sourceAgentId, targetAgentId, messageType, payload");
            }
            return new A2AMessage(this);
        }
    }
}