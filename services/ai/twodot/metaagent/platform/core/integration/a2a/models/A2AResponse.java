package ai.twodot.metaagent.platform.core.integration.a2a.models;

import ai.twodot.metaagent.platform.core.models.enums.Status;

import java.time.Instant;

public class A2AResponse {
    private final String responseId;
    private final String requestId;
    private final String sourceAgentId;
    private final String targetAgentId;
    private final Status status;
    private final Object payload;
    private final String errorMessage;
    private final Instant timestamp;
    
    private A2AResponse(Builder builder) {
        this.responseId = builder.responseId;
        this.requestId = builder.requestId;
        this.sourceAgentId = builder.sourceAgentId;
        this.targetAgentId = builder.targetAgentId;
        this.status = builder.status;
        this.payload = builder.payload;
        this.errorMessage = builder.errorMessage;
        this.timestamp = builder.timestamp != null ? builder.timestamp : Instant.now();
    }
    
    public String getResponseId() {
        return responseId;
    }
    
    public String getRequestId() {
        return requestId;
    }
    
    public String getSourceAgentId() {
        return sourceAgentId;
    }
    
    public String getTargetAgentId() {
        return targetAgentId;
    }
    
    public Status getStatus() {
        return status;
    }
    
    public Object getPayload() {
        return payload;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public Instant getTimestamp() {
        return timestamp;
    }
    
    public boolean isSuccess() {
        return status == Status.COMPLETED;
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    public static class Builder {
        private String responseId;
        private String requestId;
        private String sourceAgentId;
        private String targetAgentId;
        private Status status;
        private Object payload;
        private String errorMessage;
        private Instant timestamp;
        
        public Builder responseId(String responseId) {
            this.responseId = responseId;
            return this;
        }
        
        public Builder requestId(String requestId) {
            this.requestId = requestId;
            return this;
        }
        
        public Builder from(String sourceAgentId) {
            this.sourceAgentId = sourceAgentId;
            return this;
        }
        
        public Builder to(String targetAgentId) {
            this.targetAgentId = targetAgentId;
            return this;
        }
        
        public Builder status(Status status) {
            this.status = status;
            return this;
        }
        
        public Builder payload(Object payload) {
            this.payload = payload;
            return this;
        }
        
        public Builder error(String errorMessage) {
            this.errorMessage = errorMessage;
            this.status = Status.FAILED;
            return this;
        }
        
        public Builder timestamp(Instant timestamp) {
            this.timestamp = timestamp;
            return this;
        }
        
        public A2AResponse build() {
            return new A2AResponse(this);
        }
    }
}