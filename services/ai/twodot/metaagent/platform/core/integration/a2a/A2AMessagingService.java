package ai.twodot.metaagent.platform.core.integration.a2a;

import ai.twodot.metaagent.platform.core.integration.a2a.models.A2AMessage;
import ai.twodot.metaagent.platform.core.integration.a2a.models.A2AResponse;

import java.util.concurrent.CompletableFuture;

public interface A2AMessagingService {
    
    CompletableFuture<A2AResponse> sendMessage(A2AMessage message);
    
    void sendAsyncMessage(A2AMessage message);
    
    void registerMessageHandler(String messageType, MessageHandler handler);
    
    void unregisterMessageHandler(String messageType);
    
    interface MessageHandler {
        A2AResponse handleMessage(A2AMessage message);
    }
}