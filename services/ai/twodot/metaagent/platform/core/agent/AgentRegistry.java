package ai.twodot.metaagent.platform.core.agent;

import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class AgentRegistry {
    private final ConcurrentHashMap<String, BaseAgent> agents = new ConcurrentHashMap<>();
    
    public void register(BaseAgent agent) {
        agents.put(agent.getAgentId(), agent);
    }
    
    public void unregister(String agentId) {
        agents.remove(agentId);
    }
    
    public BaseAgent getAgent(String agentId) {
        return agents.get(agentId);
    }
    
    public Collection<BaseAgent> getAllAgents() {
        return agents.values();
    }
    
    public Collection<BaseAgent> getAgentsByType(String agentType) {
        return agents.values().stream()
                .filter(agent -> agent.getAgentType().equals(agentType))
                .toList();
    }
    
    public boolean isRegistered(String agentId) {
        return agents.containsKey(agentId);
    }
}