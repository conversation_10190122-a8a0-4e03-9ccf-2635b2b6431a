package ai.twodot.metaagent.platform.core.agent;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

public class AgentContext {
    private final String agentId;
    private final String agentType;
    private final Instant startTime;
    private final Map<String, Object> properties;
    
    public AgentContext(String agentId, String agentType) {
        this.agentId = agentId;
        this.agentType = agentType;
        this.startTime = Instant.now();
        this.properties = new HashMap<>();
    }
    
    public String getAgentId() {
        return agentId;
    }
    
    public String getAgentType() {
        return agentType;
    }
    
    public Instant getStartTime() {
        return startTime;
    }
    
    public void setProperty(String key, Object value) {
        properties.put(key, value);
    }
    
    public Object getProperty(String key) {
        return properties.get(key);
    }
    
    public Map<String, Object> getProperties() {
        return new HashMap<>(properties);
    }
}