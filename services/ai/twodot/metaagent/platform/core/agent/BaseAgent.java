package ai.twodot.metaagent.platform.core.agent;

import ai.twodot.metaagent.platform.core.models.MetricsData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.time.Instant;
import java.util.concurrent.atomic.AtomicBoolean;

public abstract class BaseAgent implements AgentLifecycle {
    
    protected final Logger logger = LoggerFactory.getLogger(getClass());
    
    private final String agentId;
    private final String agentType;
    private final AtomicBoolean initialized = new AtomicBoolean(false);
    private final AtomicBoolean running = new AtomicBoolean(false);
    
    @Autowired
    private AgentRegistry agentRegistry;
    
    protected BaseAgent(String agentId, String agentType) {
        this.agentId = agentId;
        this.agentType = agentType;
    }
    
    @PostConstruct
    public void init() {
        try {
            logger.info("Initializing agent: {} ({})", agentId, agentType);
            initialize();
            agentRegistry.register(this);
            initialized.set(true);
            running.set(true);
            logger.info("Agent initialized successfully: {}", agentId);
        } catch (Exception e) {
            logger.error("Failed to initialize agent: {}", agentId, e);
            throw new RuntimeException("Agent initialization failed", e);
        }
    }
    
    @PreDestroy
    public void destroy() {
        try {
            logger.info("Shutting down agent: {}", agentId);
            running.set(false);
            shutdown();
            agentRegistry.unregister(agentId);
            logger.info("Agent shut down successfully: {}", agentId);
        } catch (Exception e) {
            logger.error("Error during agent shutdown: {}", agentId, e);
        }
    }
    
    @Override
    public String getAgentId() {
        return agentId;
    }
    
    @Override
    public String getAgentType() {
        return agentType;
    }
    
    @Override
    public boolean isHealthy() {
        return initialized.get() && running.get() && checkHealth();
    }
    
    @Override
    public MetricsData getMetrics() {
        MetricsData metrics = new MetricsData();
        metrics.setAgentId(agentId);
        metrics.setAgentType(agentType);
        metrics.setTimestamp(Instant.now());
        metrics.setHealthy(isHealthy());
        
        collectMetrics(metrics);
        
        return metrics;
    }
    
    protected abstract void collectMetrics(MetricsData metrics);
    
    protected abstract boolean checkHealth();
    
    protected AgentContext createContext() {
        return new AgentContext(agentId, agentType);
    }
}