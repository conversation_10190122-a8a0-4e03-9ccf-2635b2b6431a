package ai.twodot.metaagent.platform.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

@RestController
public class PlatformHealthController {

    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("platform", "Meta-Agent Platform");
        health.put("version", "1.0.0");
        health.put("timestamp", Instant.now());
        health.put("agents", "All agents unified");
        health.put("port", 8080);
        return health;
    }
    
    @GetMapping("/platform/info")
    public Map<String, Object> platformInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("platform", "Meta-Agent Platform");
        info.put("version", "1.0.0");
        info.put("architecture", "Service Facade Pattern");
        info.put("agents", new String[]{
            "Discovery Registry Agent",
            "Security Monitor Agent", 
            "Resource Manager Agent",
            "Data Processing Agent",
            "Knowledge Base Agent",
            "Task Orchestrator Agent",
            "Agent Factory Agent",
            "Supreme Intelligence Agent"
        });
        info.put("features", new String[]{
            "Unified Database",
            "Single Port Configuration",
            "Service Facade Layer",
            "Common Core Framework",
            "Integrated Monitoring"
        });
        return info;
    }
}