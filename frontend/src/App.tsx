import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

// Layout Components
import { Sidebar } from './components/Layout/Sidebar';
import { Header } from './components/Layout/Header';

// Page Components
import { Dashboard } from './pages/Dashboard';
import { SecurityMonitor } from './pages/SecurityMonitor';
import { ServiceDiscovery } from './pages/ServiceDiscovery';
import { KnowledgeBase } from './pages/KnowledgeBase';
import { DataProcessing } from './pages/DataProcessing';
import { TaskOrchestrator } from './pages/TaskOrchestrator';
import { ResourceManager } from './pages/ResourceManager';
import { AgentFactory } from './pages/AgentFactory';
import { SupremeIntelligence } from './pages/SupremeIntelligence';
import { Analytics } from './pages/Analytics';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 30000, // 30 seconds
      retry: 2,
    },
  },
});

function App() {
  const [sidebarOpen, setSidebarOpen] = useState(true);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <div className="min-h-screen bg-background">
          {/* Sidebar */}
          <Sidebar open={sidebarOpen} onClose={() => setSidebarOpen(false)} />
          
          {/* Main Content */}
          <div 
            className={`transition-all duration-300 ${
              sidebarOpen ? 'ml-80' : 'ml-0'
            }`}
          >
            {/* Header */}
            <Header onMenuClick={toggleSidebar} />
            
            {/* Page Content */}
            <main className="p-6">
              <Routes>
                <Route path="/" element={<Dashboard />} />
                <Route path="/security" element={<SecurityMonitor />} />
                <Route path="/discovery" element={<ServiceDiscovery />} />
                <Route path="/knowledge" element={<KnowledgeBase />} />
                <Route path="/data-processing" element={<DataProcessing />} />
                <Route path="/orchestrator" element={<TaskOrchestrator />} />
                <Route path="/resources" element={<ResourceManager />} />
                <Route path="/agent-factory" element={<AgentFactory />} />
                <Route path="/supreme" element={<SupremeIntelligence />} />
                <Route path="/analytics" element={<Analytics />} />
              </Routes>
            </main>
          </div>
        </div>
        
        {/* React Query DevTools */}
        <ReactQueryDevtools initialIsOpen={false} />
      </Router>
    </QueryClientProvider>
  );
}

export default App;