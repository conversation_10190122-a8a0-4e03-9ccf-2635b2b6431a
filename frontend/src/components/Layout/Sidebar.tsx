import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { cn } from '@/lib/utils';
import {
  BarChart3,
  Brain,
  Database,
  Factory,
  GitBranch,
  LayoutDashboard,
  MemoryStick,
  Search,
  Shield,
  Star,
} from 'lucide-react';

const menuItems = [
  { id: 'dashboard', text: 'Platform Dashboard', icon: LayoutDashboard, path: '/' },
  { id: 'security', text: 'Security Monitor', icon: Shield, path: '/security' },
  { id: 'discovery', text: 'Service Discovery', icon: Search, path: '/discovery' },
  { id: 'knowledge', text: 'Knowledge Base', icon: Brain, path: '/knowledge' },
  { id: 'data-processing', text: 'Data Processing', icon: Database, path: '/data-processing' },
  { id: 'orchestrator', text: 'Task Orchestrator', icon: GitBranch, path: '/orchestrator' },
  { id: 'resources', text: 'Resource Manager', icon: MemoryStick, path: '/resources' },
  { id: 'agent-factory', text: 'Agent Factory', icon: Factory, path: '/agent-factory' },
  { id: 'supreme', text: 'Supreme Intelligence', icon: Star, path: '/supreme' },
  { id: 'analytics', text: 'Analytics', icon: BarChart3, path: '/analytics' },
];

interface SidebarProps {
  open: boolean;
  onClose: () => void;
}

export const Sidebar: React.FC<SidebarProps> = ({ open }) => {
  const location = useLocation();
  const navigate = useNavigate();

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  return (
    <div
      className={cn(
        'fixed left-0 top-0 h-full bg-card border-r border-border transition-transform duration-300 z-40',
        'w-80',
        open ? 'translate-x-0' : '-translate-x-full'
      )}
    >
      {/* Header */}
      <div className="p-6 border-b border-border">
        <h1 className="text-2xl font-bold gradient-text">
          Meta-Agent Platform
        </h1>
        <p className="text-sm text-muted-foreground mt-1">
          AI-Powered Multi-Agent System
        </p>
      </div>

      {/* Navigation */}
      <nav className="p-4">
        <ul className="space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = location.pathname === item.path;
            
            return (
              <li key={item.id}>
                <button
                  onClick={() => handleNavigation(item.path)}
                  className={cn(
                    'w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 text-left',
                    'hover:bg-accent hover:text-accent-foreground',
                    isActive 
                      ? 'bg-primary text-primary-foreground shadow-lg' 
                      : 'text-muted-foreground hover:text-foreground'
                  )}
                >
                  <Icon className="h-5 w-5" />
                  <span className="text-sm font-medium">{item.text}</span>
                </button>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Footer */}
      <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-border">
        <div className="text-xs text-muted-foreground text-center">
          <div>Platform v1.0.0</div>
          <div className="mt-1">Real-time AI Operations</div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;