import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn, formatNumber } from '@/lib/utils';
import { usePlatformHealth, useAIHealth, usePlatformMetrics } from '@/hooks/useApi';
import {
  Menu,
  Bell,
  Settings,
  TrendingUp,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Activity,
} from 'lucide-react';

interface HeaderProps {
  onMenuClick: () => void;
}

export const Header: React.FC<HeaderProps> = ({ onMenuClick }) => {
  const { data: javaHealth } = usePlatformHealth();
  const { data: aiHealth } = useAIHealth();
  const { data: metrics } = usePlatformMetrics();

  const isHealthy = javaHealth?.status === 'UP' && aiHealth?.status === 'healthy';
  const totalEngines = metrics?.platform_metrics?.total_engines || 0;
  const healthyEngines = metrics?.platform_metrics?.healthy_engines || 0;
  const totalRequests = metrics?.platform_metrics?.total_requests || 0;
  const errorRate = metrics?.platform_metrics?.overall_error_rate || 0;

  const getHealthStatus = () => {
    if (isHealthy && healthyEngines === totalEngines) {
      return { 
        icon: CheckCircle, 
        text: 'All Systems Operational', 
        variant: 'success' as const,
        className: 'text-green-500' 
      };
    }
    if (healthyEngines > 0) {
      return { 
        icon: AlertTriangle, 
        text: 'Some Issues Detected', 
        variant: 'warning' as const,
        className: 'text-yellow-500' 
      };
    }
    return { 
      icon: XCircle, 
      text: 'System Issues', 
      variant: 'error' as const,
      className: 'text-red-500' 
    };
  };

  const healthStatus = getHealthStatus();
  const HealthIcon = healthStatus.icon;

  return (
    <header className="bg-card border-b border-border px-6 py-4">
      <div className="flex items-center justify-between">
        {/* Left Section */}
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={onMenuClick}
            className="hover:bg-accent"
          >
            <Menu className="h-5 w-5" />
          </Button>
          
          <div>
            <h1 className="text-xl font-semibold">Platform Control Center</h1>
            <p className="text-sm text-muted-foreground">
              Real-time monitoring and management
            </p>
          </div>
        </div>

        {/* Right Section - Status Indicators */}
        <div className="flex items-center space-x-4">
          {/* Platform Health */}
          <div className="flex items-center space-x-2">
            <HealthIcon className={cn('h-4 w-4', healthStatus.className)} />
            <Badge variant={healthStatus.variant}>
              {healthStatus.text}
            </Badge>
          </div>

          {/* Engines Status */}
          <Badge variant="outline" className="flex items-center space-x-1">
            <Activity className="h-3 w-3" />
            <span>{healthyEngines}/{totalEngines} Engines</span>
          </Badge>

          {/* Request Counter */}
          <Badge variant="outline" className="flex items-center space-x-1">
            <TrendingUp className="h-3 w-3" />
            <span>{formatNumber(totalRequests)} Requests</span>
          </Badge>

          {/* Error Rate */}
          <Badge 
            variant={errorRate < 0.01 ? 'success' : errorRate < 0.05 ? 'warning' : 'error'}
            className="flex items-center space-x-1"
          >
            <span>{(errorRate * 100).toFixed(2)}% Errors</span>
          </Badge>

          {/* Action Buttons */}
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="icon" className="relative">
              <Bell className="h-4 w-4" />
              {/* Notification indicator */}
              <span className="absolute -top-1 -right-1 h-2 w-2 bg-red-500 rounded-full animate-pulse" />
            </Button>
            
            <Button variant="ghost" size="icon">
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;