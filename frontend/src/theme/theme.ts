import { createTheme } from '@mui/material/styles';

export const theme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#00D4AA',
      light: '#4DFFCD',
      dark: '#00A87A',
      contrastText: '#000000',
    },
    secondary: {
      main: '#FF6B35',
      light: '#FF9D6B',
      dark: '#C73E0A',
      contrastText: '#FFFFFF',
    },
    background: {
      default: '#0A0E27',
      paper: '#1A1E3A',
    },
    text: {
      primary: '#FFFFFF',
      secondary: '#B8C2E8',
    },
    error: {
      main: '#FF4444',
      light: '#FF7777',
      dark: '#CC2222',
    },
    warning: {
      main: '#FFA726',
      light: '#FFD95A',
      dark: '#F57C00',
    },
    info: {
      main: '#29B6F6',
      light: '#73E8FF',
      dark: '#0086C3',
    },
    success: {
      main: '#66BB6A',
      light: '#98EE99',
      dark: '#338A3E',
    },
  },
  typography: {
    fontFamily: [
      'Inter',
      '-apple-system',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Roboto',
      '"Helvetica Neue"',
      'Arial',
      'sans-serif',
    ].join(','),
    h1: {
      fontSize: '2.5rem',
      fontWeight: 700,
      letterSpacing: '-0.02em',
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 600,
      letterSpacing: '-0.01em',
    },
    h3: {
      fontSize: '1.5rem',
      fontWeight: 600,
    },
    h4: {
      fontSize: '1.25rem',
      fontWeight: 600,
    },
    h5: {
      fontSize: '1.125rem',
      fontWeight: 500,
    },
    h6: {
      fontSize: '1rem',
      fontWeight: 500,
    },
    body1: {
      fontSize: '0.875rem',
      lineHeight: 1.5,
    },
    body2: {
      fontSize: '0.75rem',
      lineHeight: 1.4,
    },
  },
  shape: {
    borderRadius: 12,
  },
  components: {
    MuiCard: {
      styleOverrides: {
        root: {
          backgroundColor: '#1A1E3A',
          border: '1px solid #2A2E4A',
          borderRadius: 16,
          '&:hover': {
            borderColor: '#00D4AA',
            transform: 'translateY(-2px)',
            transition: 'all 0.3s ease-in-out',
          },
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: 'none',
          fontWeight: 500,
          padding: '8px 16px',
        },
        contained: {
          backgroundColor: '#00D4AA',
          color: '#000000',
          '&:hover': {
            backgroundColor: '#00A87A',
            transform: 'translateY(-1px)',
            boxShadow: '0 4px 12px rgba(0, 212, 170, 0.3)',
          },
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 6,
        },
        filled: {
          backgroundColor: '#2A2E4A',
          color: '#FFFFFF',
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundColor: '#1A1E3A',
          border: '1px solid #2A2E4A',
        },
      },
    },
    MuiDrawer: {
      styleOverrides: {
        paper: {
          backgroundColor: '#0F1329',
          borderRight: '1px solid #2A2E4A',
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundColor: '#0F1329',
          borderBottom: '1px solid #2A2E4A',
          boxShadow: 'none',
        },
      },
    },
    MuiTableHead: {
      styleOverrides: {
        root: {
          backgroundColor: '#2A2E4A',
        },
      },
    },
    MuiTableRow: {
      styleOverrides: {
        root: {
          '&:hover': {
            backgroundColor: '#252952',
          },
        },
      },
    },
  },
});

export default theme;