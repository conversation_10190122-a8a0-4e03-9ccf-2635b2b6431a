// API Types for Meta-Agent Platform

// Platform Health Types
export interface PlatformHealth {
  port: number;
  version: string;
  platform: string;
  status: string;
  timestamp: string;
  agents: string;
}

export interface AIHealth {
  status: string;
  service: string;
  version: string;
  engines: Record<string, EngineStatus>;
  timestamp: number;
}

export interface EngineStatus {
  engine_name: string;
  healthy: boolean;
  uptime_seconds: number;
  request_count: number;
  error_count: number;
  error_rate: number;
  status: string;
  subsystems?: Record<string, boolean>;
  capabilities?: Record<string, boolean>;
  overall_ai_health?: boolean;
}

// Platform Capabilities
export interface PlatformCapabilities {
  platform_name: string;
  version: string;
  engines: Record<string, EngineCapabilities>;
  total_engines: number;
  supported_protocols: string[];
  ai_frameworks: string[];
  timestamp: string;
}

export interface EngineCapabilities {
  capabilities: string[];
  ai_technologies: string[];
}

// Platform Metrics
export interface PlatformMetrics {
  platform_metrics: {
    platform_status: string;
    uptime_seconds: number;
    total_engines: number;
    healthy_engines: number;
    total_requests: number;
    total_errors: number;
    overall_error_rate: number;
    engines: Record<string, EngineStatus>;
  };
  collection_timestamp: string;
}

// Security Types
export interface SecurityMetrics {
  active_threats: number;
  threat_analysis_count: number;
  risk_assessments: number;
  security_events_processed: number;
  average_threat_confidence: number;
  high_risk_events: number;
  timestamp: string;
}

export interface ThreatDetectionRequest {
  event: {
    event_id: string;
    event_type: string;
    severity: string;
    source_ip: string;
    target_ip?: string;
    user_agent?: string;
    raw_data: string;
  };
}

export interface ThreatDetectionResponse {
  threat_detected: boolean;
  threat_type: string;
  confidence_score: number;
  risk_level: string;
  analysis_method: string;
  details: {
    individual_results: any[];
    threat_votes: number;
    total_votes: number;
    ensemble_weights: Record<string, number>;
  };
  timestamp: string;
  processing_time_ms: number;
}

// Service Discovery Types
export interface ServiceMatchingRequest {
  requirements: {
    description: string;
    capabilities: string[];
    protocols: string[];
    min_availability: number;
    max_response_time: number;
  };
  available_services: ServiceDefinition[];
}

export interface ServiceDefinition {
  id: string;
  name: string;
  description: string;
  capabilities: string[];
  endpoints: Array<{ protocol: string }>;
  metrics: {
    availability: number;
    response_time_p95: number;
    error_rate: number;
  };
}

export interface ServiceMatchingResponse {
  matched_services: Array<{
    service: ServiceDefinition;
    similarity_score: number;
    compatibility_analysis: {
      capability_match: number;
      protocol_match: number;
      performance_score: number;
      overall_score: number;
    };
    recommendation: string;
  }>;
  analysis_summary: {
    total_services_analyzed: number;
    best_match_score: number;
    matching_method: string;
    processing_time_ms: number;
  };
  timestamp: string;
}

// Risk Assessment Types
export interface RiskAssessmentRequest {
  type: string;
  event: {
    event_id: string;
    event_type: string;
    severity: string;
    source_ip: string;
    raw_data: string;
  };
}

export interface RiskAssessmentResponse {
  success: boolean;
  risk_assessment: {
    overall_risk_score: number;
    risk_level: string;
    risk_factors: {
      threat_confidence: number;
      severity_score: number;
      time_criticality: number;
      source_reputation: number;
    };
    threat_indicators: {
      threat_detected: boolean;
      threat_type: string;
      confidence: number;
    };
    recommendations: string[];
  };
  event_id: string;
  assessment_timestamp: string;
}

// Data Processing Types
export interface DataProcessingRequest {
  operation: string;
  data: {
    metrics?: number[];
    threshold?: number;
  };
}

export interface DataProcessingResponse {
  engine_name: string;
  result: {
    success: boolean;
    result: {
      message: string;
      request: DataProcessingRequest;
    };
    processing_time: number;
    engine: string;
  };
  timestamp: string;
}

// Knowledge Base Types
export interface KnowledgeBaseRequest {
  operation: string;
  query: string;
  context: string;
}

export interface KnowledgeBaseResponse {
  engine_name: string;
  result: {
    success: boolean;
    result: {
      message: string;
      request: KnowledgeBaseRequest;
    };
    processing_time: number;
    engine: string;
  };
  timestamp: string;
}

// Task Orchestrator Types
export interface TaskOrchestratorRequest {
  operation: string;
  workflow: {
    tasks: string[];
    dependencies: string[][];
    resources: {
      cpu: number;
      memory: number;
    };
  };
}

export interface TaskOrchestratorResponse {
  engine_name: string;
  result: {
    success: boolean;
    result: {
      message: string;
      request: TaskOrchestratorRequest;
    };
    processing_time: number;
    engine: string;
  };
  timestamp: string;
}