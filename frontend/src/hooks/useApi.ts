import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  platformApi,
  securityApi,
  discoveryApi,
  dataProcessingApi,
  knowledgeBaseApi,
  orchestratorApi,
  resourceManagerApi,
  agentFactoryApi,
  supremeIntelligenceApi,
} from '../services/api';

// Platform hooks
export const usePlatformHealth = () => {
  return useQuery({
    queryKey: ['platform-health'],
    queryFn: platformApi.getJavaHealth,
    refetchInterval: 10000, // Refresh every 10 seconds
  });
};

export const useAIHealth = () => {
  return useQuery({
    queryKey: ['ai-health'],
    queryFn: platformApi.getAIHealth,
    refetchInterval: 10000,
  });
};

export const usePlatformCapabilities = () => {
  return useQuery({
    queryKey: ['platform-capabilities'],
    queryFn: platformApi.getCapabilities,
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
  });
};

export const usePlatformMetrics = () => {
  return useQuery({
    queryKey: ['platform-metrics'],
    queryFn: platformApi.getMetrics,
    refetchInterval: 5000, // Refresh every 5 seconds
  });
};

export const useEnginesStatus = () => {
  return useQuery({
    queryKey: ['engines-status'],
    queryFn: platformApi.getEnginesStatus,
    refetchInterval: 10000,
  });
};

// Security hooks
export const useSecurityMetrics = () => {
  return useQuery({
    queryKey: ['security-metrics'],
    queryFn: securityApi.getMetrics,
    refetchInterval: 5000,
  });
};

export const useSecurityCapabilities = () => {
  return useQuery({
    queryKey: ['security-capabilities'],
    queryFn: securityApi.getCapabilities,
    staleTime: 5 * 60 * 1000,
  });
};

export const useThreatDetection = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: securityApi.detectThreat,
    onSuccess: () => {
      // Invalidate security metrics to refresh
      queryClient.invalidateQueries({ queryKey: ['security-metrics'] });
    },
  });
};

export const useRiskAssessment = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: securityApi.assessRisk,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['security-metrics'] });
    },
  });
};

export const useThreatIntelligence = () => {
  return useQuery({
    queryKey: ['threat-intelligence'],
    queryFn: securityApi.getThreatIntelligence,
    refetchInterval: 30000, // Refresh every 30 seconds
  });
};

// Discovery hooks
export const useDiscoveryCapabilities = () => {
  return useQuery({
    queryKey: ['discovery-capabilities'],
    queryFn: discoveryApi.getCapabilities,
    staleTime: 5 * 60 * 1000,
  });
};

export const useServiceMatching = () => {
  return useMutation({
    mutationFn: discoveryApi.matchServices,
  });
};

export const useServiceOptimization = () => {
  return useMutation({
    mutationFn: discoveryApi.optimizeService,
  });
};

export const useHealthPrediction = () => {
  return useMutation({
    mutationFn: discoveryApi.predictHealth,
  });
};

// Data Processing hooks
export const useDataProcessing = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: dataProcessingApi.processData,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['processing-history'] });
    },
  });
};

export const useProcessingHistory = () => {
  return useQuery({
    queryKey: ['processing-history'],
    queryFn: dataProcessingApi.getProcessingHistory,
    refetchInterval: 15000,
  });
};

// Knowledge Base hooks
export const useKnowledgeProcessing = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: knowledgeBaseApi.processKnowledge,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['knowledge-history'] });
    },
  });
};

export const useKnowledgeHistory = () => {
  return useQuery({
    queryKey: ['knowledge-history'],
    queryFn: knowledgeBaseApi.getKnowledgeHistory,
    refetchInterval: 15000,
  });
};

// Task Orchestrator hooks
export const useWorkflowProcessing = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: orchestratorApi.processWorkflow,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workflow-history'] });
      queryClient.invalidateQueries({ queryKey: ['active-workflows'] });
    },
  });
};

export const useWorkflowHistory = () => {
  return useQuery({
    queryKey: ['workflow-history'],
    queryFn: orchestratorApi.getWorkflowHistory,
    refetchInterval: 15000,
  });
};

export const useActiveWorkflows = () => {
  return useQuery({
    queryKey: ['active-workflows'],
    queryFn: orchestratorApi.getActiveWorkflows,
    refetchInterval: 10000,
  });
};

// Resource Manager hooks
export const useResourceMetrics = () => {
  return useQuery({
    queryKey: ['resource-metrics'],
    queryFn: resourceManagerApi.getMetrics,
    refetchInterval: 5000,
  });
};

export const useCapacityPrediction = () => {
  return useMutation({
    mutationFn: resourceManagerApi.predictCapacity,
  });
};

// Agent Factory hooks
export const useAgentTemplates = () => {
  return useQuery({
    queryKey: ['agent-templates'],
    queryFn: agentFactoryApi.getTemplates,
    staleTime: 5 * 60 * 1000,
  });
};

export const useCreateAgent = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: agentFactoryApi.createAgent,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['platform-metrics'] });
    },
  });
};

// Supreme Intelligence hooks
export const useSupremeIntelligenceOverview = () => {
  return useQuery({
    queryKey: ['supreme-intelligence-overview'],
    queryFn: supremeIntelligenceApi.getOverview,
    refetchInterval: 10000,
  });
};

export const useStrategicPlan = () => {
  return useQuery({
    queryKey: ['strategic-plan'],
    queryFn: supremeIntelligenceApi.getStrategicPlan,
    refetchInterval: 30000,
  });
};