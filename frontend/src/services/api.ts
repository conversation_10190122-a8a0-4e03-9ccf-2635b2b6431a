import axios from 'axios';

// Simple type definitions
export interface PlatformHealth {
  port: number;
  version: string;
  platform: string;
  status: string;
  timestamp: string;
  agents: string;
}

export interface AIHealth {
  status: string;
  service: string;
  version: string;
  engines: Record<string, any>;
  timestamp: number;
}

export interface PlatformMetrics {
  platform_metrics: {
    platform_status: string;
    uptime_seconds: number;
    total_engines: number;
    healthy_engines: number;
    total_requests: number;
    total_errors: number;
    overall_error_rate: number;
    engines: Record<string, any>;
  };
  collection_timestamp: string;
}

export interface PlatformCapabilities {
  platform_name: string;
  version: string;
  engines: Record<string, any>;
  total_engines: number;
  supported_protocols: string[];
  ai_frameworks: string[];
  timestamp: string;
}

// API Configuration
const JAVA_API_URL = 'http://localhost:8080';
const PYTHON_AI_URL = 'http://localhost:8081';

const javaApi = axios.create({
  baseURL: JAVA_API_URL,
  timeout: 10000,
});

const pythonAi = axios.create({
  baseURL: PYTHON_AI_URL,
  timeout: 30000,
});

// Platform Health & Status APIs
export const platformApi = {
  // Java Platform Health
  getJavaHealth: async (): Promise<PlatformHealth> => {
    const response = await javaApi.get('/health');
    return response.data;
  },

  // Python AI Platform Health
  getAIHealth: async (): Promise<AIHealth> => {
    const response = await pythonAi.get('/health');
    return response.data;
  },

  // Platform Capabilities
  getCapabilities: async (): Promise<PlatformCapabilities> => {
    const response = await pythonAi.get('/ai/platform/capabilities');
    return response.data;
  },

  // Platform Metrics
  getMetrics: async (): Promise<PlatformMetrics> => {
    const response = await pythonAi.get('/ai/platform/metrics');
    return response.data;
  },

  // AI Engines Status
  getEnginesStatus: async () => {
    const response = await pythonAi.get('/ai/platform/engines');
    return response.data;
  },

  // Specific Engine Status
  getEngineStatus: async (engineName: string) => {
    const response = await pythonAi.get(`/ai/platform/engines/${engineName}`);
    return response.data;
  },
};

// Security Monitor APIs
export const securityApi = {
  // Security Capabilities
  getCapabilities: async () => {
    const response = await pythonAi.get('/ai/security/capabilities');
    return response.data;
  },

  // Security Metrics
  getMetrics: async () => {
    const response = await pythonAi.get('/ai/security/metrics');
    return response.data;
  },

  // Threat Detection
  detectThreat: async (request: any) => {
    const response = await pythonAi.post('/ai/security/detect-threat', request);
    return response.data;
  },

  // Risk Assessment
  assessRisk: async (request: any) => {
    const response = await pythonAi.post('/ai/security/assess-risk', request);
    return response.data;
  },

  // Analyze Events (Batch)
  analyzeEvents: async (events: any[]) => {
    const response = await pythonAi.post('/ai/security/analyze-events', { events });
    return response.data;
  },

  // Threat Intelligence
  getThreatIntelligence: async () => {
    const response = await pythonAi.get('/ai/security/threat-intelligence');
    return response.data;
  },
};

// Discovery Registry APIs
export const discoveryApi = {
  // Discovery Capabilities
  getCapabilities: async () => {
    const response = await pythonAi.get('/ai/discovery/capabilities');
    return response.data;
  },

  // Service Matching
  matchServices: async (request: any) => {
    const response = await pythonAi.post('/ai/discovery/match-services', request);
    return response.data;
  },

  // Service Optimization
  optimizeService: async (serviceData: any) => {
    const response = await pythonAi.post('/ai/discovery/optimize-service', serviceData);
    return response.data;
  },

  // Health Prediction
  predictHealth: async (serviceData: any) => {
    const response = await pythonAi.post('/ai/discovery/predict-health', serviceData);
    return response.data;
  },
};

// Data Processing APIs
export const dataProcessingApi = {
  // Process Data
  processData: async (request: any) => {
    const response = await pythonAi.post('/ai/platform/engines/data_processing/process', request);
    return response.data;
  },

  // Get Processing History
  getProcessingHistory: async () => {
    try {
      const response = await pythonAi.get('/ai/platform/engines/data_processing/history');
      return response.data;
    } catch (error) {
      return [];
    }
  },
};

// Knowledge Base APIs
export const knowledgeBaseApi = {
  // Process Knowledge Request
  processKnowledge: async (request: any) => {
    const response = await pythonAi.post('/ai/platform/engines/knowledge_base/process', request);
    return response.data;
  },

  // Get Knowledge History
  getKnowledgeHistory: async () => {
    try {
      const response = await pythonAi.get('/ai/platform/engines/knowledge_base/history');
      return response.data;
    } catch (error) {
      return [];
    }
  },
};

// Task Orchestrator APIs
export const orchestratorApi = {
  // Process Workflow
  processWorkflow: async (request: any) => {
    const response = await pythonAi.post('/ai/platform/engines/task_orchestrator/process', request);
    return response.data;
  },

  // Get Workflow History
  getWorkflowHistory: async () => {
    try {
      const response = await pythonAi.get('/ai/platform/engines/task_orchestrator/history');
      return response.data;
    } catch (error) {
      return [];
    }
  },

  // Get Active Workflows
  getActiveWorkflows: async () => {
    try {
      const response = await pythonAi.get('/ai/platform/engines/task_orchestrator/active');
      return response.data;
    } catch (error) {
      return [];
    }
  },
};

// Resource Manager APIs
export const resourceManagerApi = {
  // Get Resource Metrics
  getMetrics: async () => {
    try {
      const response = await pythonAi.get('/ai/platform/engines/resource_manager/metrics');
      return response.data;
    } catch (error) {
      return { cpu_usage: 0, memory_usage: 0, network_usage: 0, disk_usage: 0 };
    }
  },

  // Predict Capacity
  predictCapacity: async (data: any) => {
    try {
      const response = await pythonAi.post('/ai/platform/engines/resource_manager/predict', data);
      return response.data;
    } catch (error) {
      return { prediction: 'Service temporarily unavailable' };
    }
  },
};

// Agent Factory APIs
export const agentFactoryApi = {
  // Get Agent Templates
  getTemplates: async () => {
    try {
      const response = await pythonAi.get('/ai/platform/engines/agent_factory/templates');
      return response.data;
    } catch (error) {
      return [];
    }
  },

  // Create Agent
  createAgent: async (template: any) => {
    try {
      const response = await pythonAi.post('/ai/platform/engines/agent_factory/create', template);
      return response.data;
    } catch (error) {
      return { success: false, message: 'Service temporarily unavailable' };
    }
  },
};

// Supreme Intelligence APIs
export const supremeIntelligenceApi = {
  // Get Intelligence Overview
  getOverview: async () => {
    try {
      const response = await pythonAi.get('/ai/platform/engines/supreme_intelligence/overview');
      return response.data;
    } catch (error) {
      return { 
        consciousness_level: 'Active',
        active_decisions: 0,
        coordination_score: 85,
        crisis_alerts: 0 
      };
    }
  },

  // Get Strategic Plan
  getStrategicPlan: async () => {
    try {
      const response = await pythonAi.get('/ai/platform/engines/supreme_intelligence/strategic-plan');
      return response.data;
    } catch (error) {
      return { 
        current_objectives: [],
        planned_actions: [],
        risk_assessments: []
      };
    }
  },
};