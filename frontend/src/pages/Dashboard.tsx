import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { cn, formatNumber, formatDuration, getStatusColor } from '@/lib/utils';
import {
  usePlatformHealth,
  useAIHealth,
  usePlatformMetrics,
  useEnginesStatus,
  usePlatformCapabilities,
} from '@/hooks/useApi';
import {
  Activity,
  Brain,
  CheckCircle,
  Clock,
  Cpu,
  Database,
  RefreshCw,
  Server,
  TrendingUp,
  Zap,
  AlertCircle,
  Shield,
  Search,
  GitBranch,
} from 'lucide-react';

export const Dashboard: React.FC = () => {
  const { data: javaHealth, isLoading: javaLoading } = usePlatformHealth();
  const { data: aiHealth, isLoading: aiLoading } = useAIHealth();
  const { data: metrics, isLoading: metricsLoading } = usePlatformMetrics();
  const { data: enginesStatus, isLoading: enginesLoading } = useEnginesStatus();
  const { data: capabilities } = usePlatformCapabilities();

  const isLoading = javaLoading || aiLoading || metricsLoading || enginesLoading;

  // Platform metrics
  const totalEngines = metrics?.platform_metrics?.total_engines || 0;
  const healthyEngines = metrics?.platform_metrics?.healthy_engines || 0;
  const totalRequests = metrics?.platform_metrics?.total_requests || 0;
  const totalErrors = metrics?.platform_metrics?.total_errors || 0;
  const errorRate = metrics?.platform_metrics?.overall_error_rate || 0;
  const uptime = metrics?.platform_metrics?.uptime_seconds || 0;

  // Get key engine metrics
  const keyEngines = [
    {
      name: 'Security Monitor',
      key: 'security_monitor',
      icon: Shield,
      description: 'Threat detection and security analysis',
    },
    {
      name: 'Discovery Registry',
      key: 'discovery_registry',
      icon: Search,
      description: 'Service discovery and optimization',
    },
    {
      name: 'Task Orchestrator',
      key: 'task_orchestrator',
      icon: GitBranch,
      description: 'Workflow optimization and scheduling',
    },
    {
      name: 'Data Processing',
      key: 'data_processing',
      icon: Database,
      description: 'AI-powered data transformation',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold gradient-text">Platform Dashboard</h1>
          <p className="text-muted-foreground">
            Real-time overview of your Meta-Agent Platform
          </p>
        </div>
        <Button variant="outline" size="sm" className="flex items-center space-x-2">
          <RefreshCw className="h-4 w-4" />
          <span>Refresh</span>
        </Button>
      </div>

      {/* Platform Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Java Platform Status */}
        <Card className="card-hover">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Java Platform</CardTitle>
            <Server className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <div
                className={cn(
                  'h-2 w-2 rounded-full',
                  javaHealth?.status === 'UP' ? 'bg-green-500 animate-pulse' : 'bg-red-500'
                )}
              />
              <div className="text-2xl font-bold">
                {isLoading ? '...' : javaHealth?.status || 'Unknown'}
              </div>
            </div>
            <p className="text-xs text-muted-foreground">
              Port {javaHealth?.port || '8080'} • Version {javaHealth?.version || '1.0.0'}
            </p>
          </CardContent>
        </Card>

        {/* AI Platform Status */}
        <Card className="card-hover">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">AI Platform</CardTitle>
            <Brain className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <div
                className={cn(
                  'h-2 w-2 rounded-full',
                  aiHealth?.status === 'healthy' ? 'bg-green-500 animate-pulse' : 'bg-red-500'
                )}
              />
              <div className="text-2xl font-bold">
                {isLoading ? '...' : aiHealth?.status || 'Unknown'}
              </div>
            </div>
            <p className="text-xs text-muted-foreground">
              {totalEngines} AI Engines • Version {aiHealth?.version || '1.0.0'}
            </p>
          </CardContent>
        </Card>

        {/* Total Requests */}
        <Card className="card-hover">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isLoading ? '...' : formatNumber(totalRequests)}
            </div>
            <p className="text-xs text-muted-foreground">
              {formatNumber(totalErrors)} errors ({(errorRate * 100).toFixed(2)}%)
            </p>
          </CardContent>
        </Card>

        {/* System Uptime */}
        <Card className="card-hover">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Uptime</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isLoading ? '...' : formatDuration(uptime)}
            </div>
            <p className="text-xs text-muted-foreground">
              Continuous operation
            </p>
          </CardContent>
        </Card>
      </div>

      {/* AI Engines Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Engine Status Grid */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Activity className="h-5 w-5" />
              <span>AI Engines Status</span>
            </CardTitle>
            <CardDescription>
              {healthyEngines}/{totalEngines} engines operational
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              {keyEngines.map((engine) => {
                const engineData = metrics?.platform_metrics?.engines?.[engine.key];
                const isHealthy = engineData?.healthy;
                const Icon = engine.icon;

                return (
                  <div
                    key={engine.key}
                    className="p-4 border border-border rounded-lg hover:border-primary/50 transition-colors"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <Icon className="h-5 w-5 text-muted-foreground" />
                      <Badge
                        variant={isHealthy ? 'success' : 'error'}
                        className="text-xs"
                      >
                        {isHealthy ? 'Healthy' : 'Error'}
                      </Badge>
                    </div>
                    <h4 className="font-medium text-sm">{engine.name}</h4>
                    <p className="text-xs text-muted-foreground mt-1">
                      {engineData?.request_count || 0} requests
                    </p>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Platform Capabilities */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Zap className="h-5 w-5" />
              <span>AI Capabilities</span>
            </CardTitle>
            <CardDescription>
              Advanced AI technologies available
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {capabilities?.ai_frameworks?.map((framework) => (
                <div key={framework} className="flex items-center justify-between">
                  <span className="text-sm">{framework}</span>
                  <Badge variant="outline">Active</Badge>
                </div>
              ))}
              
              <div className="pt-4 border-t">
                <div className="text-sm text-muted-foreground mb-2">
                  Supported Protocols
                </div>
                <div className="flex flex-wrap gap-2">
                  {capabilities?.supported_protocols?.map((protocol) => (
                    <Badge key={protocol} variant="secondary" className="text-xs">
                      {protocol}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity & System Health */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* System Health */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Cpu className="h-5 w-5" />
              <span>System Health Metrics</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(metrics?.platform_metrics?.engines || {}).map(([key, engine]) => (
                <div key={key} className="flex items-center justify-between p-3 border border-border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div
                      className={cn(
                        'h-2 w-2 rounded-full',
                        engine.healthy ? 'bg-green-500' : 'bg-red-500'
                      )}
                    />
                    <div>
                      <div className="font-medium text-sm">{engine.engine_name}</div>
                      <div className="text-xs text-muted-foreground">
                        Uptime: {formatDuration(engine.uptime_seconds)}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">{engine.request_count} requests</div>
                    <div className="text-xs text-muted-foreground">
                      {(engine.error_rate * 100).toFixed(1)}% error rate
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common platform operations
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button className="w-full justify-start" variant="outline">
              <Shield className="h-4 w-4 mr-2" />
              Security Scan
            </Button>
            <Button className="w-full justify-start" variant="outline">
              <Search className="h-4 w-4 mr-2" />
              Service Discovery
            </Button>
            <Button className="w-full justify-start" variant="outline">
              <Database className="h-4 w-4 mr-2" />
              Process Data
            </Button>
            <Button className="w-full justify-start" variant="outline">
              <GitBranch className="h-4 w-4 mr-2" />
              Run Workflow
            </Button>
            
            <div className="pt-3 border-t">
              <h4 className="text-sm font-medium mb-2">System Status</h4>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Overall Health</span>
                  <Badge variant={healthyEngines === totalEngines ? 'success' : 'warning'}>
                    {healthyEngines === totalEngines ? 'Excellent' : 'Good'}
                  </Badge>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>Performance</span>
                  <Badge variant={errorRate < 0.01 ? 'success' : 'warning'}>
                    {errorRate < 0.01 ? 'Optimal' : 'Good'}
                  </Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;