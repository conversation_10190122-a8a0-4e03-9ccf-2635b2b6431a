/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/agent/SecurityMonitorAgent.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/facade/monitoring/MonitoringFacade.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/orchestrator/data/OrchestrationModels.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/orchestrator/scheduling/IntelligentSchedulingService.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/knowledgebase/agent/KnowledgeBaseAgent.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/knowledgebase/impl/RecommendationEngineImpl.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/factory/deployment/DeploymentAutomationService.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/resourcemanager/cloud/CloudProviderInterface.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/integration/SecurityEventStreamer.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/integration/SecurityAgentIntegrationService.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/MetaAgentPlatformApplication.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/security/SecurityResponseType.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/dataprocessing/a2a/A2AIntegrationService.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/factory/template/TemplateManagementService.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/test/java/com/multiagent/platform/controller/PlatformHealthControllerTest.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/dataprocessing/streaming/TransformationServiceImpl.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/agent/AgentHealthStatus.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/integration/DiscoveryRegistryClient.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/knowledgebase/impl/KnowledgeExtractionOrchestratorImpl.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/factory/data/GenerationModels.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/dataprocessing/data/WindowConfig.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/orchestrator/performance/PerformancePredictionService.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/test/java/com/multiagent/platform/facade/registry/impl/AgentRegistryFacadeImplTest.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/core/controller/BaseHealthController.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/dataprocessing/integration/MultiSourceDataIntegrator.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/orchestrator/orchestration/TaskDistributionService.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/security/BehavioralAnalysisResult.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/resourcemanager/resource/ResourceAllocation.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/security/IncidentResponseResult.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/spia/data/IntelligenceModels.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/core/integration/a2a/models/A2AMessage.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/resourcemanager/a2a/CostBenefitAnalyzerClient.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/resourcemanager/a2a/SecurityMonitoringAgentClient.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/ai/ThreatDetectionService.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/knowledgebase/impl/KnowledgeStorageManagerImpl.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/security/SecurityEntityType.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/security/SecurityLocation.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/integration/CommunicationBrokerClient.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/knowledgebase/impl/AiKnowledgeProcessorImpl.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/resourcemanager/a2a/A2AProtocol.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/security/SecurityEventType.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/security/BehavioralAnalysisService.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/resourcemanager/a2a/A2ACommunicationService.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/security/RiskLevel.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/facade/AgentFacade.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/knowledgebase/config/SecurityConfig.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/core/agent/AgentRegistry.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/orchestrator/integration/OrchestratorAgentIntegrationService.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/test/java/com/multiagent/platform/integration/MetaAgentPlatformIntegrationTest.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/controller/PlatformHealthController.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/dataprocessing/ai/AiTransformationEngineImpl.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/factory/generation/AgentGenerationService.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/core/agent/BaseAgent.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/security/SecurityResponse.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/factory/quality/QualityValidationService.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/dataprocessing/data/DataModels.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/monitoring/SecurityMetricsCollectorImpl.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/resourcemanager/a2a/A2AWorkflowCoordinator.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/core/agent/AgentLifecycle.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/dataprocessing/streaming/QualityServiceImpl.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/core/agent/AgentContext.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/test/java/com/multiagent/platform/core/integration/a2a/models/A2AResponseTest.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/test/java/com/multiagent/platform/core/models/MetricsDataTest.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/ai/ThreatDetectionServiceImpl.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/factory/orchestration/GenerationOrchestrationService.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/core/models/enums/Status.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/resourcemanager/cloud/KubernetesCloudProvider.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/resourcemanager/a2a/DeploymentRiskAssessorClient.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/security/IncidentResponseService.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/factory/agent/AgentFactoryAgent.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/factory/monitoring/GenerationMonitoringService.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/security/IncidentResponseServiceImpl.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/facade/orchestration/impl/OrchestrationFacadeImpl.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/facade/communication/CommunicationFacade.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/security/BehavioralAnalysisServiceImpl.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/security/SecurityEventProcessorImpl.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/factory/integration/FactoryAgentIntegrationService.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/factory/ai/AIIntegrationService.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/dataprocessing/streaming/StreamProcessingEngineImpl.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/dataprocessing/orchestration/OrchestrationServiceImpl.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/resourcemanager/cloud/MultiCloudManager.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/security/SecurityPolicyService.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/security/SecurityEventProcessor.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/core/models/enums/MessageType.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/resourcemanager/ai/CapacityPredictionService.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/facade/registry/AgentRegistryFacade.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/orchestrator/agent/TaskOrchestratorAgent.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/dataprocessing/monitoring/DataProcessingMonitoringService.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/core/integration/a2a/A2AMessagingService.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/facade/registry/impl/AgentRegistryFacadeImpl.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/knowledgebase/impl/SemanticProcessingEngineImpl.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/knowledgebase/impl/PatternRecognitionEngineImpl.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/core/models/MetricsData.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/core/models/enums/Priority.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/orchestrator/workflow/WorkflowOrchestrationService.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/resourcemanager/ai/CapacityPredictionServiceImpl.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/security/SecuritySeverity.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/dataprocessing/agent/DataProcessingAgent.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/resourcemanager/cloud/GcpCloudProvider.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/security/ThreatType.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/resourcemanager/agent/ResourceManagerAgent.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/intelligence/ThreatIntelligenceService.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/security/SecurityEventSource.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/dataprocessing/data/DataSource.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/orchestrator/monitoring/OrchestrationMonitoringService.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/facade/communication/impl/InterAgentCommunicationFacadeImpl.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/facade/monitoring/impl/MonitoringFacadeImpl.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/facade/impl/AgentFacadeImpl.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/security/SecurityMetrics.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/resourcemanager/prediction/CapacityPredictionService.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/knowledgebase/data/KnowledgeModels.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/facade/orchestration/OrchestrationFacade.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/core/integration/a2a/models/A2AResponse.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/config/WebClientConfig.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/monitoring/SecurityMetricsCollector.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/security/SecurityResponseStatus.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/spia/agent/SupremePlatformIntelligenceAgent.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/orchestrator/ai/OrchestrationAIService.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/resourcemanager/monitoring/ResourceMonitoringService.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/facade/communication/impl/CommunicationFacadeImpl.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/resourcemanager/optimization/CostOptimizationService.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/security/SecurityEvent.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/dataprocessing/quality/DataQualityAnalyzerImpl.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/intelligence/ThreatIntelligenceServiceImpl.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/test/java/com/multiagent/platform/core/controller/BaseHealthControllerTest.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/security/ThreatAnalysisResult.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/test/java/com/multiagent/platform/facade/communication/impl/CommunicationFacadeImplTest.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/test/java/com/multiagent/platform/facade/monitoring/impl/MonitoringFacadeImplTest.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/core/communication/AgentCommunicationService.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/facade/communication/InterAgentCommunicationFacade.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/securitymonitor/security/SecurityEventResult.java
/Users/<USER>/workspaces/g/meta-agent-platform/services/com/multiagent/platform/agents/resourcemanager/scaling/AutoScalingService.java
