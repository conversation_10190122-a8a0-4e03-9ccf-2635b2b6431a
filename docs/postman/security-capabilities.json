{"engine_name": "SecurityMonitor", "capabilities": ["threat_detection", "event_analysis", "risk_assessment", "behavioral_analysis", "anomaly_detection"], "ai_technologies": ["isolation_forest", "lstm_neural_networks", "nlp_analysis", "ensemble_methods", "hybrid_ai_reasoning"], "supported_operations": ["detect-threat", "analyze-events", "assess-risk", "threat-intelligence", "metrics"], "threat_types": ["anomalous_behavior", "suspicious_content", "ai_detected_threat", "multiple_threats"], "risk_levels": ["LOW", "MEDIUM", "HIGH", "CRITICAL"]}