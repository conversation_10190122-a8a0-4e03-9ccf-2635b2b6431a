{"info": {"name": "Meta-Agent Platform API Collection", "description": "Comprehensive test collection for the unified Meta-Agent Platform with Java services and Python AI engines", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "java_platform_url", "value": "http://localhost:8080", "type": "string"}, {"key": "python_ai_url", "value": "http://localhost:8081", "type": "string"}], "item": [{"name": "1. Platform Health Checks", "item": [{"name": "Java Platform Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{java_platform_url}}/health", "host": ["{{java_platform_url}}"], "path": ["health"]}, "description": "Check health of Java platform with unified agents"}, "response": []}, {"name": "Python AI Platform Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{python_ai_url}}/health", "host": ["{{python_ai_url}}"], "path": ["health"]}, "description": "Check health of Python AI platform with 8 AI engines"}, "response": []}, {"name": "Platform Engines Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{python_ai_url}}/ai/platform/engines", "host": ["{{python_ai_url}}"], "path": ["ai", "platform", "engines"]}, "description": "Get detailed status of all AI engines"}, "response": []}]}, {"name": "2. Discovery Registry AI", "item": [{"name": "Discovery Capabilities", "request": {"method": "GET", "header": [], "url": {"raw": "{{python_ai_url}}/ai/discovery/capabilities", "host": ["{{python_ai_url}}"], "path": ["ai", "discovery", "capabilities"]}, "description": "Get Discovery Registry AI capabilities"}, "response": []}, {"name": "Service Matching (AI)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"requirements\": {\n    \"description\": \"Looking for a high-performance API gateway service\",\n    \"capabilities\": [\"routing\", \"load_balancing\", \"security\"],\n    \"protocols\": [\"HTTP\", \"HTTPS\"],\n    \"min_availability\": 0.99,\n    \"max_response_time\": 100\n  },\n  \"available_services\": [\n    {\n      \"id\": \"nginx-gateway\",\n      \"name\": \"NGINX API Gateway\", \n      \"description\": \"High-performance API gateway with load balancing\",\n      \"capabilities\": [\"routing\", \"load_balancing\", \"caching\"],\n      \"endpoints\": [{\"protocol\": \"HTTP\"}, {\"protocol\": \"HTTPS\"}],\n      \"metrics\": {\"availability\": 0.995, \"response_time_p95\": 45, \"error_rate\": 0.001}\n    },\n    {\n      \"id\": \"kong-gateway\",\n      \"name\": \"Kong Gateway\",\n      \"description\": \"Cloud-native API gateway platform\", \n      \"capabilities\": [\"routing\", \"security\", \"analytics\"],\n      \"endpoints\": [{\"protocol\": \"HTTP\"}],\n      \"metrics\": {\"availability\": 0.98, \"response_time_p95\": 80, \"error_rate\": 0.005}\n    }\n  ]\n}"}, "url": {"raw": "{{python_ai_url}}/ai/discovery/match-services", "host": ["{{python_ai_url}}"], "path": ["ai", "discovery", "match-services"]}, "description": "AI-powered semantic service matching using sentence transformers"}, "response": []}, {"name": "Service Optimization (AI)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"service_name\": \"api-gateway\",\n  \"current_config\": {\n    \"timeout\": 5000,\n    \"retries\": 3,\n    \"load_balancer\": \"round_robin\"\n  },\n  \"metrics\": {\n    \"response_time_p95\": 250,\n    \"error_rate\": 0.02,\n    \"cpu_usage\": 0.75\n  }\n}"}, "url": {"raw": "{{python_ai_url}}/ai/discovery/optimize-service", "host": ["{{python_ai_url}}"], "path": ["ai", "discovery", "optimize-service"]}, "description": "AI-powered service optimization recommendations"}, "response": []}, {"name": "Health Prediction (AI)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"service_name\": \"payment-service\",\n  \"metrics\": {\n    \"cpu_usage\": 0.85,\n    \"memory_usage\": 0.78,\n    \"response_time\": 450,\n    \"error_rate\": 0.015,\n    \"request_rate\": 1250\n  },\n  \"time_horizon\": \"1h\"\n}"}, "url": {"raw": "{{python_ai_url}}/ai/discovery/predict-health", "host": ["{{python_ai_url}}"], "path": ["ai", "discovery", "predict-health"]}, "description": "AI-powered health prediction using ML models"}, "response": []}]}, {"name": "3. Security Monitor AI", "item": [{"name": "Security Capabilities", "request": {"method": "GET", "header": [], "url": {"raw": "{{python_ai_url}}/ai/security/capabilities", "host": ["{{python_ai_url}}"], "path": ["ai", "security", "capabilities"]}, "description": "Get Security Monitor AI capabilities"}, "response": []}, {"name": "Threat Detection (Hybrid AI)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"event\": {\n    \"event_id\": \"evt_001\",\n    \"event_type\": \"login_attempt\",\n    \"severity\": \"MEDIUM\",\n    \"source_ip\": \"*************\",\n    \"target_ip\": \"********\",\n    \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64)\",\n    \"raw_data\": \"Failed login attempt from suspicious IP address\"\n  }\n}"}, "url": {"raw": "{{python_ai_url}}/ai/security/detect-threat", "host": ["{{python_ai_url}}"], "path": ["ai", "security", "detect-threat"]}, "description": "Hybrid AI threat detection using Isolation Forest + NLP + AI Reasoning"}, "response": []}, {"name": "Risk Assessment (AI)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"event\",\n  \"event\": {\n    \"event_id\": \"critical_001\", \n    \"event_type\": \"unauthorized_access\",\n    \"severity\": \"CRITICAL\",\n    \"source_ip\": \"***********\",\n    \"raw_data\": \"Multiple failed authentication attempts detected from external IP\"\n  }\n}"}, "url": {"raw": "{{python_ai_url}}/ai/security/assess-risk", "host": ["{{python_ai_url}}"], "path": ["ai", "security", "assess-risk"]}, "description": "AI-powered risk assessment with threat scoring"}, "response": []}, {"name": "Security Metrics", "request": {"method": "GET", "header": [], "url": {"raw": "{{python_ai_url}}/ai/security/metrics", "host": ["{{python_ai_url}}"], "path": ["ai", "security", "metrics"]}, "description": "Get comprehensive security monitoring metrics"}, "response": []}, {"name": "Threat Intelligence", "request": {"method": "GET", "header": [], "url": {"raw": "{{python_ai_url}}/ai/security/threat-intelligence", "host": ["{{python_ai_url}}"], "path": ["ai", "security", "threat-intelligence"]}, "description": "Get current threat intelligence summary"}, "response": []}]}, {"name": "4. Platform Management", "item": [{"name": "Platform Overview", "request": {"method": "GET", "header": [], "url": {"raw": "{{python_ai_url}}/ai/platform/", "host": ["{{python_ai_url}}"], "path": ["ai", "platform", ""]}, "description": "Get basic platform information"}, "response": []}, {"name": "Platform Capabilities", "request": {"method": "GET", "header": [], "url": {"raw": "{{python_ai_url}}/ai/platform/capabilities", "host": ["{{python_ai_url}}"], "path": ["ai", "platform", "capabilities"]}, "description": "Get all AI capabilities available on the platform"}, "response": []}, {"name": "Platform Metrics", "request": {"method": "GET", "header": [], "url": {"raw": "{{python_ai_url}}/ai/platform/metrics", "host": ["{{python_ai_url}}"], "path": ["ai", "platform", "metrics"]}, "description": "Get comprehensive platform metrics"}, "response": []}, {"name": "Specific Engine Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{python_ai_url}}/ai/platform/engines/discovery_registry", "host": ["{{python_ai_url}}"], "path": ["ai", "platform", "engines", "discovery_registry"]}, "description": "Get detailed status of a specific AI engine"}, "response": []}]}, {"name": "5. Advanced AI Operations", "item": [{"name": "Data Processing Engine", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"operation\": \"anomaly_detection\",\n  \"data\": {\n    \"metrics\": [1.2, 1.5, 1.3, 8.7, 1.4, 1.6],\n    \"threshold\": 2.0\n  }\n}"}, "url": {"raw": "{{python_ai_url}}/ai/platform/engines/data_processing/process", "host": ["{{python_ai_url}}"], "path": ["ai", "platform", "engines", "data_processing", "process"]}, "description": "Test data processing AI engine"}, "response": []}, {"name": "Knowledge Base Engine", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"operation\": \"knowledge_extraction\",\n  \"query\": \"What are the best practices for microservices architecture?\",\n  \"context\": \"cloud computing\"\n}"}, "url": {"raw": "{{python_ai_url}}/ai/platform/engines/knowledge_base/process", "host": ["{{python_ai_url}}"], "path": ["ai", "platform", "engines", "knowledge_base", "process"]}, "description": "Test knowledge base AI engine"}, "response": []}, {"name": "Task Orchestrator Engine", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"operation\": \"workflow_optimization\",\n  \"workflow\": {\n    \"tasks\": [\"build\", \"test\", \"deploy\"],\n    \"dependencies\": [[\"build\", \"test\"], [\"test\", \"deploy\"]],\n    \"resources\": {\"cpu\": 4, \"memory\": 8}\n  }\n}"}, "url": {"raw": "{{python_ai_url}}/ai/platform/engines/task_orchestrator/process", "host": ["{{python_ai_url}}"], "path": ["ai", "platform", "engines", "task_orchestrator", "process"]}, "description": "Test task orchestrator AI engine"}, "response": []}]}]}