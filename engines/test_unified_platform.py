#!/usr/bin/env python3
"""
Test script for the unified AI platform - verifies all engines can be imported and initialized.
"""

import asyncio
import sys
import traceback
from ai_platform.engines import (
    DiscoveryRegistryEngine,
    SecurityMonitorEngine,
    DataProcessingEngine,
    KnowledgeBaseEngine,
    TaskOrchestratorEngine,
    ResourceManagerEngine,
    AgentFactoryEngine,
    SupremePlatformIntelligenceEngine
)

async def test_engine_initialization():
    """Test that all engines can be created and initialized."""
    
    engines = [
        ("Discovery Registry", DiscoveryRegistryEngine),
        ("Security Monitor", SecurityMonitorEngine),
        ("Data Processing", DataProcessingEngine),
        ("Knowledge Base", KnowledgeBaseEngine),
        ("Task Orchestrator", TaskOrchestratorEngine),
        ("Resource Manager", ResourceManagerEngine),
        ("Agent Factory", AgentFactoryEngine),
        ("Supreme Platform Intelligence", SupremePlatformIntelligenceEngine)
    ]
    
    print("🚀 Testing Unified AI Platform Engines")
    print("=" * 50)
    
    results = []
    
    for name, engine_class in engines:
        try:
            print(f"Testing {name} Engine...")
            
            # Create engine instance
            engine = engine_class()
            print(f"  ✅ {name} Engine created successfully")
            
            # Test capabilities
            capabilities = await engine.get_capabilities()
            print(f"  📋 Capabilities: {len(capabilities)} available")
            
            # Test health before initialization
            health_before = await engine.is_healthy()
            print(f"  🔍 Health before init: {'Healthy' if health_before else 'Not Ready'}")
            
            # Attempt initialization
            init_success = await engine.initialize()
            print(f"  🔧 Initialization: {'Success' if init_success else 'Failed'}")
            
            # Test health after initialization
            health_after = await engine.is_healthy()
            print(f"  ❤️  Health after init: {'Healthy' if health_after else 'Unhealthy'}")
            
            # Get metrics
            metrics = await engine.get_metrics()
            print(f"  📊 Metrics available: {'Yes' if metrics else 'No'}")
            
            # Shutdown
            await engine.shutdown()
            print(f"  🔄 Shutdown: Complete")
            
            results.append((name, True, None))
            print(f"  ✅ {name} Engine test PASSED\n")
            
        except Exception as e:
            error_msg = f"{str(e)}"
            results.append((name, False, error_msg))
            print(f"  ❌ {name} Engine test FAILED: {error_msg}")
            print(f"     Traceback: {traceback.format_exc()}\n")
    
    # Summary
    print("=" * 50)
    print("📈 UNIFIED PLATFORM TEST RESULTS")
    print("=" * 50)
    
    passed = sum(1 for _, success, _ in results if success)
    total = len(results)
    
    for name, success, error in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status:8} {name}")
        if error:
            print(f"         Error: {error}")
    
    print(f"\n🎯 Test Summary: {passed}/{total} engines passed")
    
    if passed == total:
        print("🎉 ALL ENGINES WORKING - Unified Platform Ready!")
        return True
    else:
        print("⚠️  Some engines failed - Review errors above")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(test_engine_initialization())
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Test script failed: {e}")
        traceback.print_exc()
        sys.exit(1)