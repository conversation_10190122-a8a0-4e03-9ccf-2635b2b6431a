"""
Threat Detection AI Models

Advanced machine learning models for real-time threat detection and classification.
Includes ensemble methods, deep learning, and hybrid AI approaches.
"""

import asyncio
import json
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ThreatDetectionResult:
    """Result of threat detection analysis"""
    threat_detected: bool
    threat_type: str
    confidence_score: float
    risk_level: str
    analysis_method: str
    details: Dict[str, Any]
    timestamp: datetime
    processing_time_ms: float

@dataclass
class SecurityEvent:
    """Security event data structure for ML processing"""
    event_id: str
    event_type: str
    severity: str
    source_ip: str
    target_ip: str
    user_agent: str
    timestamp: datetime
    raw_data: str
    metadata: Dict[str, Any]

class IsolationForestThreatDetector:
    """Isolation Forest-based anomaly detection for threat identification"""
    
    def __init__(self, contamination: float = 0.1, n_estimators: int = 100):
        try:
            from sklearn.ensemble import IsolationForest
            from sklearn.preprocessing import StandardScaler
            
            self.model = IsolationForest(
                contamination=contamination,
                n_estimators=n_estimators,
                random_state=42,
                n_jobs=-1
            )
            self.scaler = StandardScaler()
        except ImportError:
            logger.warning("scikit-learn not available, using mock implementation")
            self.model = None
            self.scaler = None
            
        self.is_trained = False
        
    def train(self, training_data: pd.DataFrame) -> Dict[str, Any]:
        """Train the isolation forest model"""
        try:
            if self.model is None:
                return {
                    "model_type": "IsolationForest",
                    "training_samples": len(training_data),
                    "status": "sklearn not available - using mock implementation"
                }
                
            logger.info("Training Isolation Forest threat detection model...")
            
            # Prepare features
            features = self._extract_features(training_data)
            
            # Scale features
            features_scaled = self.scaler.fit_transform(features)
            
            # Train model
            self.model.fit(features_scaled)
            self.is_trained = True
            
            # Validate model
            anomaly_scores = self.model.decision_function(features_scaled)
            predictions = self.model.predict(features_scaled)
            
            normal_count = np.sum(predictions == 1)
            anomaly_count = np.sum(predictions == -1)
            
            logger.info(f"Model trained successfully. Normal: {normal_count}, Anomalies: {anomaly_count}")
            
            return {
                "model_type": "IsolationForest",
                "training_samples": len(training_data),
                "normal_samples": normal_count,
                "anomaly_samples": anomaly_count,
                "contamination_rate": anomaly_count / len(training_data),
                "feature_count": features.shape[1]
            }
            
        except Exception as e:
            logger.error(f"Error training Isolation Forest model: {e}")
            raise
    
    def detect_threat(self, event: SecurityEvent) -> ThreatDetectionResult:
        """Detect threats in security event using isolation forest"""
        start_time = datetime.now()
        
        try:
            if self.model is None:
                # Mock implementation when sklearn not available
                return self._mock_detection(event, start_time)
                
            if not self.is_trained:
                raise ValueError("Model not trained. Call train() first.")
            
            # Extract features from event
            features = self._extract_event_features(event)
            features_scaled = self.scaler.transform([features])
            
            # Get anomaly score and prediction
            anomaly_score = self.model.decision_function(features_scaled)[0]
            prediction = self.model.predict(features_scaled)[0]
            
            # Convert to threat detection result
            threat_detected = prediction == -1
            confidence_score = abs(anomaly_score)
            
            # Determine risk level based on anomaly score
            if anomaly_score < -0.5:
                risk_level = "CRITICAL"
            elif anomaly_score < -0.3:
                risk_level = "HIGH"
            elif anomaly_score < -0.1:
                risk_level = "MEDIUM"
            else:
                risk_level = "LOW"
            
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            
            return ThreatDetectionResult(
                threat_detected=threat_detected,
                threat_type="ANOMALOUS_BEHAVIOR" if threat_detected else "NORMAL",
                confidence_score=confidence_score,
                risk_level=risk_level,
                analysis_method="IsolationForest",
                details={
                    "anomaly_score": anomaly_score,
                    "features": features.tolist(),
                    "threshold": -0.1
                },
                timestamp=datetime.now(),
                processing_time_ms=processing_time
            )
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            logger.error(f"Error in threat detection: {e}")
            
            return ThreatDetectionResult(
                threat_detected=False,
                threat_type="ERROR",
                confidence_score=0.0,
                risk_level="UNKNOWN",
                analysis_method="IsolationForest",
                details={"error": str(e)},
                timestamp=datetime.now(),
                processing_time_ms=processing_time
            )
    
    def _mock_detection(self, event: SecurityEvent, start_time: datetime) -> ThreatDetectionResult:
        """Mock detection when sklearn is not available"""
        # Simple rule-based mock detection
        threat_detected = False
        confidence_score = 0.3
        risk_level = "LOW"
        
        # Basic threat detection rules
        if event.severity in ["HIGH", "CRITICAL"]:
            threat_detected = True
            confidence_score = 0.7
            risk_level = "HIGH"
        elif "attack" in event.raw_data.lower() or "malware" in event.raw_data.lower():
            threat_detected = True
            confidence_score = 0.8
            risk_level = "CRITICAL"
            
        processing_time = (datetime.now() - start_time).total_seconds() * 1000
        
        return ThreatDetectionResult(
            threat_detected=threat_detected,
            threat_type="RULE_BASED_DETECTION" if threat_detected else "NORMAL",
            confidence_score=confidence_score,
            risk_level=risk_level,
            analysis_method="IsolationForest_Mock",
            details={
                "mock_mode": True,
                "sklearn_available": False
            },
            timestamp=datetime.now(),
            processing_time_ms=processing_time
        )
    
    def _extract_features(self, data: pd.DataFrame) -> np.ndarray:
        """Extract numerical features from security event data"""
        features = []
        
        for _, row in data.iterrows():
            event_features = self._extract_row_features(row)
            features.append(event_features)
        
        return np.array(features)
    
    def _extract_event_features(self, event: SecurityEvent) -> np.ndarray:
        """Extract features from single security event"""
        features = []
        
        # Time-based features
        hour = event.timestamp.hour
        day_of_week = event.timestamp.weekday()
        features.extend([hour, day_of_week])
        
        # IP-based features
        if event.source_ip:
            ip_parts = event.source_ip.split('.')
            if len(ip_parts) == 4:
                features.extend([int(part) for part in ip_parts])
            else:
                features.extend([0, 0, 0, 0])
        else:
            features.extend([0, 0, 0, 0])
        
        # Event type encoding
        event_type_encoding = hash(event.event_type) % 1000
        features.append(event_type_encoding)
        
        # Severity encoding
        severity_map = {"INFO": 0, "LOW": 1, "MEDIUM": 2, "HIGH": 3, "CRITICAL": 4}
        severity_encoding = severity_map.get(event.severity, 0)
        features.append(severity_encoding)
        
        # User agent features
        if event.user_agent:
            ua_length = len(event.user_agent)
            ua_hash = hash(event.user_agent) % 1000
            features.extend([ua_length, ua_hash])
        else:
            features.extend([0, 0])
        
        # Raw data features
        if event.raw_data:
            data_length = len(event.raw_data)
            features.append(data_length)
        else:
            features.append(0)
        
        return np.array(features, dtype=float)
    
    def _extract_row_features(self, row) -> List[float]:
        """Extract features from pandas row"""
        features = []
        
        # Add basic numerical features
        for col in row.index:
            if pd.api.types.is_numeric_dtype(type(row[col])):
                features.append(float(row[col]))
            elif isinstance(row[col], str):
                features.append(float(hash(row[col]) % 1000))
            else:
                features.append(0.0)
        
        return features


class SecurityNLPAnalyzer:
    """NLP-based security event analysis"""
    
    def __init__(self):
        self.tokenizer = None
        self.model = None
        self.threat_keywords = [
            "attack", "malware", "virus", "trojan", "phishing", "breach",
            "intrusion", "exploit", "vulnerability", "suspicious", "anomaly",
            "unauthorized", "malicious", "injection", "xss", "csrf", "ddos"
        ]
    
    async def initialize(self) -> None:
        """Initialize NLP models"""
        try:
            logger.info("Initializing SecurityNLP Analyzer...")
            
            # For now, use keyword-based approach
            # In production, load transformer models here
            logger.info("SecurityNLP Analyzer initialized successfully (keyword-based)")
            
        except Exception as e:
            logger.error(f"Error initializing NLP analyzer: {e}")
            # Continue with keyword-based analysis
    
    async def analyze_event(self, event: SecurityEvent) -> ThreatDetectionResult:
        """Analyze security event using NLP techniques"""
        start_time = datetime.now()
        
        try:
            # Combine text data from event
            text_data = f"{event.event_type} {event.raw_data or ''} {event.user_agent or ''}".lower()
            
            # Keyword-based threat detection
            threat_score = self._calculate_threat_score(text_data)
            
            # Determine threat status
            threat_detected = threat_score > 0.3
            
            # Map score to risk level
            if threat_score > 0.8:
                risk_level = "CRITICAL"
            elif threat_score > 0.6:
                risk_level = "HIGH"
            elif threat_score > 0.4:
                risk_level = "MEDIUM"
            else:
                risk_level = "LOW"
            
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            
            return ThreatDetectionResult(
                threat_detected=threat_detected,
                threat_type="SUSPICIOUS_CONTENT" if threat_detected else "NORMAL",
                confidence_score=threat_score,
                risk_level=risk_level,
                analysis_method="NLP",
                details={
                    "threat_keywords_found": self._find_threat_keywords(text_data),
                    "text_length": len(text_data),
                    "threat_score": threat_score
                },
                timestamp=datetime.now(),
                processing_time_ms=processing_time
            )
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            logger.error(f"Error in NLP analysis: {e}")
            
            return ThreatDetectionResult(
                threat_detected=False,
                threat_type="ERROR",
                confidence_score=0.0,
                risk_level="UNKNOWN",
                analysis_method="NLP",
                details={"error": str(e)},
                timestamp=datetime.now(),
                processing_time_ms=processing_time
            )
    
    def _calculate_threat_score(self, text: str) -> float:
        """Calculate threat score based on keyword presence and patterns"""
        if not text:
            return 0.0
        
        threat_score = 0.0
        
        # Check for threat keywords
        for keyword in self.threat_keywords:
            if keyword in text:
                threat_score += 0.1
        
        # Pattern-based scoring
        if "failed" in text and "login" in text:
            threat_score += 0.2
        
        if "unauthorized" in text:
            threat_score += 0.3
        
        if "error" in text and "sql" in text:
            threat_score += 0.4
        
        # Normalize score
        return min(threat_score, 1.0)
    
    def _find_threat_keywords(self, text: str) -> List[str]:
        """Find threat keywords in text"""
        found_keywords = []
        for keyword in self.threat_keywords:
            if keyword in text:
                found_keywords.append(keyword)
        return found_keywords


class AIReasoningEngine:
    """AI-powered reasoning engine for advanced threat analysis"""
    
    def __init__(self):
        self.initialized = False
    
    async def initialize(self) -> None:
        """Initialize AI reasoning clients"""
        try:
            logger.info("Initializing AI Reasoning Engine...")
            self.initialized = True
            logger.info("AI Reasoning Engine initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing AI reasoning engine: {e}")
            self.initialized = False
    
    async def analyze_threat(self, event: SecurityEvent) -> ThreatDetectionResult:
        """Analyze threat using AI reasoning"""
        start_time = datetime.now()
        
        try:
            if not self.initialized:
                await self.initialize()
            
            # Rule-based reasoning (placeholder for LLM integration)
            threat_indicators = self._analyze_threat_indicators(event)
            risk_score = self._calculate_risk_score(threat_indicators)
            
            threat_detected = risk_score > 0.5
            
            # Map risk score to level
            if risk_score > 0.8:
                risk_level = "CRITICAL"
            elif risk_score > 0.6:
                risk_level = "HIGH"
            elif risk_score > 0.4:
                risk_level = "MEDIUM"
            else:
                risk_level = "LOW"
            
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            
            return ThreatDetectionResult(
                threat_detected=threat_detected,
                threat_type="AI_DETECTED_THREAT" if threat_detected else "NORMAL",
                confidence_score=risk_score,
                risk_level=risk_level,
                analysis_method="AI_Reasoning",
                details={
                    "threat_indicators": threat_indicators,
                    "reasoning": "Rule-based analysis with AI reasoning patterns"
                },
                timestamp=datetime.now(),
                processing_time_ms=processing_time
            )
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            logger.error(f"Error in AI reasoning analysis: {e}")
            
            return ThreatDetectionResult(
                threat_detected=False,
                threat_type="ERROR",
                confidence_score=0.0,
                risk_level="UNKNOWN",
                analysis_method="AI_Reasoning",
                details={"error": str(e)},
                timestamp=datetime.now(),
                processing_time_ms=processing_time
            )
    
    def _analyze_threat_indicators(self, event: SecurityEvent) -> Dict[str, Any]:
        """Analyze various threat indicators in the event"""
        indicators = {
            "temporal_anomaly": False,
            "geographic_anomaly": False,
            "behavioral_anomaly": False,
            "technical_anomaly": False
        }
        
        # Temporal analysis
        hour = event.timestamp.hour
        if hour < 6 or hour > 22:  # Outside normal business hours
            indicators["temporal_anomaly"] = True
        
        # Technical analysis
        if event.severity in ["HIGH", "CRITICAL"]:
            indicators["technical_anomaly"] = True
        
        # Behavioral analysis
        if "failed" in event.raw_data.lower() and "login" in event.raw_data.lower():
            indicators["behavioral_anomaly"] = True
        
        return indicators
    
    def _calculate_risk_score(self, indicators: Dict[str, Any]) -> float:
        """Calculate overall risk score from threat indicators"""
        score = 0.0
        
        if indicators["temporal_anomaly"]:
            score += 0.2
        if indicators["geographic_anomaly"]:
            score += 0.3
        if indicators["behavioral_anomaly"]:
            score += 0.4
        if indicators["technical_anomaly"]:
            score += 0.3
        
        return min(score, 1.0)


class HybridThreatDetectionEngine:
    """Hybrid AI threat detection engine combining multiple approaches"""
    
    def __init__(self):
        self.isolation_forest = IsolationForestThreatDetector()
        self.nlp_analyzer = SecurityNLPAnalyzer()
        self.ai_reasoner = AIReasoningEngine()
        
    async def initialize(self) -> None:
        """Initialize all AI models"""
        logger.info("Initializing Hybrid Threat Detection Engine...")
        
        try:
            # Initialize NLP analyzer
            await self.nlp_analyzer.initialize()
            
            # Initialize AI reasoner
            await self.ai_reasoner.initialize()
            
            logger.info("Hybrid Threat Detection Engine initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing threat detection engine: {e}")
            raise
    
    async def detect_threat(self, event: SecurityEvent) -> ThreatDetectionResult:
        """Comprehensive threat detection using multiple AI approaches"""
        start_time = datetime.now()
        
        try:
            # Run multiple detection methods in parallel
            tasks = [
                self._isolation_forest_detection(event),
                self._nlp_analysis(event),
                self._ai_reasoning_analysis(event)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Combine results using ensemble method
            final_result = self._combine_detection_results(results, event)
            
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            final_result.processing_time_ms = processing_time
            
            return final_result
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            logger.error(f"Error in hybrid threat detection: {e}")
            
            return ThreatDetectionResult(
                threat_detected=False,
                threat_type="ERROR",
                confidence_score=0.0,
                risk_level="UNKNOWN",
                analysis_method="HybridEngine",
                details={"error": str(e)},
                timestamp=datetime.now(),
                processing_time_ms=processing_time
            )
    
    async def _isolation_forest_detection(self, event: SecurityEvent) -> ThreatDetectionResult:
        """Run isolation forest detection"""
        if self.isolation_forest.is_trained or self.isolation_forest.model is None:
            return self.isolation_forest.detect_threat(event)
        else:
            return ThreatDetectionResult(
                threat_detected=False,
                threat_type="MODEL_NOT_TRAINED",
                confidence_score=0.0,
                risk_level="UNKNOWN",
                analysis_method="IsolationForest",
                details={"error": "Model not trained"},
                timestamp=datetime.now(),
                processing_time_ms=0.0
            )
    
    async def _nlp_analysis(self, event: SecurityEvent) -> ThreatDetectionResult:
        """Run NLP-based threat analysis"""
        return await self.nlp_analyzer.analyze_event(event)
    
    async def _ai_reasoning_analysis(self, event: SecurityEvent) -> ThreatDetectionResult:
        """Run AI reasoning-based analysis"""
        return await self.ai_reasoner.analyze_threat(event)
    
    def _combine_detection_results(self, results: List, event: SecurityEvent) -> ThreatDetectionResult:
        """Combine results from multiple detection methods"""
        valid_results = [r for r in results if isinstance(r, ThreatDetectionResult)]
        
        if not valid_results:
            return ThreatDetectionResult(
                threat_detected=False,
                threat_type="NO_VALID_ANALYSIS",
                confidence_score=0.0,
                risk_level="UNKNOWN",
                analysis_method="HybridEngine",
                details={"error": "No valid analysis results"},
                timestamp=datetime.now(),
                processing_time_ms=0.0
            )
        
        # Weighted ensemble approach
        threat_votes = sum(1 for r in valid_results if r.threat_detected)
        total_votes = len(valid_results)
        
        # Calculate weighted confidence
        weights = {"IsolationForest": 0.3, "IsolationForest_Mock": 0.3, "NLP": 0.3, "AI_Reasoning": 0.4}
        weighted_confidence = 0.0
        
        for result in valid_results:
            weight = weights.get(result.analysis_method, 0.1)
            weighted_confidence += result.confidence_score * weight
        
        # Determine final threat status
        threat_detected = threat_votes > total_votes / 2
        
        # Determine risk level
        max_risk_level = max(valid_results, key=lambda r: self._risk_level_score(r.risk_level)).risk_level
        
        # Combine threat types
        threat_types = [r.threat_type for r in valid_results if r.threat_detected]
        combined_threat_type = "MULTIPLE_THREATS" if len(threat_types) > 1 else (threat_types[0] if threat_types else "NORMAL")
        
        return ThreatDetectionResult(
            threat_detected=threat_detected,
            threat_type=combined_threat_type,
            confidence_score=weighted_confidence,
            risk_level=max_risk_level,
            analysis_method="HybridEnsemble",
            details={
                "individual_results": [r.details for r in valid_results],
                "threat_votes": threat_votes,
                "total_votes": total_votes,
                "ensemble_weights": weights
            },
            timestamp=datetime.now(),
            processing_time_ms=0.0  # Will be set by caller
        )
    
    def _risk_level_score(self, risk_level: str) -> int:
        """Convert risk level to numerical score for comparison"""
        risk_scores = {"LOW": 1, "MEDIUM": 2, "HIGH": 3, "CRITICAL": 4, "UNKNOWN": 0}
        return risk_scores.get(risk_level, 0)