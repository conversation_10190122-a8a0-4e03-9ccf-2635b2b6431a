"""
Security Monitor AI Engine

Main engine for security threat detection and analysis.
Integrates multiple AI models for comprehensive security monitoring.
"""

from typing import Dict, Any
import asyncio
import logging
from datetime import datetime

from ai_platform.core.base_engine import BaseAIEngine
from .threat_detection_models import (
    HybridThreatDetectionEngine,
    SecurityEvent,
    ThreatDetectionResult
)

logger = logging.getLogger(__name__)


class SecurityMonitorEngine(BaseAIEngine):
    """
    Security Monitor AI Engine that provides threat detection and analysis capabilities.
    
    Capabilities:
    - Real-time threat detection using multiple AI models
    - Hybrid analysis combining ML and NLP approaches
    - Security event processing and classification
    - Risk assessment and threat scoring
    """
    
    def __init__(self):
        super().__init__("SecurityMonitor")
        self.threat_engine = HybridThreatDetectionEngine()
        
    async def initialize(self) -> bool:
        """Initialize the security monitor engine and all AI models."""
        try:
            logger.info("Initializing Security Monitor AI Engine...")
            
            # Initialize threat detection engine
            await self.threat_engine.initialize()
            
            logger.info("Security Monitor AI Engine initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Security Monitor AI Engine: {e}")
            return False
    
    async def process(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process security-related AI requests.
        
        Supported operations:
        - threat_detection: Analyze security events for threats
        - event_analysis: Comprehensive security event analysis
        - risk_assessment: Assess risk levels of security events
        """
        operation = request.get("operation", "threat_detection")
        
        if operation == "threat_detection":
            return await self._detect_threat(request)
        elif operation == "event_analysis":
            return await self._analyze_event(request)
        elif operation == "risk_assessment":
            return await self._assess_risk(request)
        else:
            return {
                "success": False,
                "error": f"Unsupported operation: {operation}",
                "supported_operations": ["threat_detection", "event_analysis", "risk_assessment"]
            }
    
    async def cleanup(self) -> None:
        """Clean up resources when shutting down."""
        try:
            logger.info("Cleaning up Security Monitor AI Engine...")
            # Cleanup threat detection resources if needed
            logger.info("Security Monitor AI Engine cleanup complete")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
    
    async def _detect_threat(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Detect threats in security events."""
        try:
            # Extract event data from request
            event_data = request.get("event", {})
            
            # Create security event object
            security_event = SecurityEvent(
                event_id=event_data.get("event_id", "unknown"),
                event_type=event_data.get("event_type", "unknown"),
                severity=event_data.get("severity", "INFO"),
                source_ip=event_data.get("source_ip", ""),
                target_ip=event_data.get("target_ip", ""),
                user_agent=event_data.get("user_agent", ""),
                timestamp=datetime.fromisoformat(event_data.get("timestamp", datetime.now().isoformat())),
                raw_data=event_data.get("raw_data", ""),
                metadata=event_data.get("metadata", {})
            )
            
            # Perform threat detection
            result = await self.threat_engine.detect_threat(security_event)
            
            return {
                "success": True,
                "threat_detected": result.threat_detected,
                "threat_type": result.threat_type,
                "confidence_score": result.confidence_score,
                "risk_level": result.risk_level,
                "analysis_method": result.analysis_method,
                "details": result.details,
                "timestamp": result.timestamp.isoformat(),
                "processing_time_ms": result.processing_time_ms
            }
            
        except Exception as e:
            logger.error(f"Error in threat detection: {e}")
            return {
                "success": False,
                "error": str(e),
                "threat_detected": False
            }
    
    async def _analyze_event(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Perform comprehensive analysis of security events."""
        try:
            events_data = request.get("events", [])
            if not isinstance(events_data, list):
                events_data = [events_data]
            
            analysis_results = []
            
            for event_data in events_data:
                # Create security event
                security_event = SecurityEvent(
                    event_id=event_data.get("event_id", "unknown"),
                    event_type=event_data.get("event_type", "unknown"),
                    severity=event_data.get("severity", "INFO"),
                    source_ip=event_data.get("source_ip", ""),
                    target_ip=event_data.get("target_ip", ""),
                    user_agent=event_data.get("user_agent", ""),
                    timestamp=datetime.fromisoformat(event_data.get("timestamp", datetime.now().isoformat())),
                    raw_data=event_data.get("raw_data", ""),
                    metadata=event_data.get("metadata", {})
                )
                
                # Analyze event
                threat_result = await self.threat_engine.detect_threat(security_event)
                
                analysis_results.append({
                    "event_id": security_event.event_id,
                    "threat_analysis": {
                        "threat_detected": threat_result.threat_detected,
                        "threat_type": threat_result.threat_type,
                        "confidence_score": threat_result.confidence_score,
                        "risk_level": threat_result.risk_level,
                        "analysis_method": threat_result.analysis_method,
                        "details": threat_result.details
                    },
                    "event_summary": {
                        "event_type": security_event.event_type,
                        "severity": security_event.severity,
                        "timestamp": security_event.timestamp.isoformat()
                    },
                    "processing_time_ms": threat_result.processing_time_ms
                })
            
            # Generate summary statistics
            total_events = len(analysis_results)
            threats_detected = sum(1 for r in analysis_results if r["threat_analysis"]["threat_detected"])
            avg_confidence = sum(r["threat_analysis"]["confidence_score"] for r in analysis_results) / total_events if total_events > 0 else 0
            
            return {
                "success": True,
                "analysis_summary": {
                    "total_events": total_events,
                    "threats_detected": threats_detected,
                    "threat_percentage": (threats_detected / total_events * 100) if total_events > 0 else 0,
                    "average_confidence": avg_confidence
                },
                "event_analyses": analysis_results
            }
            
        except Exception as e:
            logger.error(f"Error in event analysis: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _assess_risk(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Assess risk levels for security events or systems."""
        try:
            assessment_type = request.get("type", "event")  # event, system, network
            
            if assessment_type == "event":
                return await self._assess_event_risk(request)
            elif assessment_type == "system":
                return await self._assess_system_risk(request)
            elif assessment_type == "network":
                return await self._assess_network_risk(request)
            else:
                return {
                    "success": False,
                    "error": f"Unsupported risk assessment type: {assessment_type}",
                    "supported_types": ["event", "system", "network"]
                }
                
        except Exception as e:
            logger.error(f"Error in risk assessment: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _assess_event_risk(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Assess risk for specific events."""
        event_data = request.get("event", {})
        
        # Create security event
        security_event = SecurityEvent(
            event_id=event_data.get("event_id", "unknown"),
            event_type=event_data.get("event_type", "unknown"),
            severity=event_data.get("severity", "INFO"),
            source_ip=event_data.get("source_ip", ""),
            target_ip=event_data.get("target_ip", ""),
            user_agent=event_data.get("user_agent", ""),
            timestamp=datetime.fromisoformat(event_data.get("timestamp", datetime.now().isoformat())),
            raw_data=event_data.get("raw_data", ""),
            metadata=event_data.get("metadata", {})
        )
        
        # Perform threat detection for risk assessment
        threat_result = await self.threat_engine.detect_threat(security_event)
        
        # Calculate comprehensive risk score
        risk_factors = {
            "threat_confidence": threat_result.confidence_score,
            "severity_score": self._severity_to_score(security_event.severity),
            "time_criticality": self._calculate_time_criticality(security_event.timestamp),
            "source_reputation": self._assess_source_reputation(security_event.source_ip)
        }
        
        overall_risk_score = sum(risk_factors.values()) / len(risk_factors)
        
        return {
            "success": True,
            "risk_assessment": {
                "overall_risk_score": overall_risk_score,
                "risk_level": threat_result.risk_level,
                "risk_factors": risk_factors,
                "threat_indicators": {
                    "threat_detected": threat_result.threat_detected,
                    "threat_type": threat_result.threat_type,
                    "confidence": threat_result.confidence_score
                },
                "recommendations": self._generate_risk_recommendations(overall_risk_score, threat_result)
            },
            "event_id": security_event.event_id,
            "assessment_timestamp": datetime.now().isoformat()
        }
    
    async def _assess_system_risk(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Assess overall system risk."""
        system_data = request.get("system", {})
        
        # Mock system risk assessment
        system_id = system_data.get("system_id", "unknown")
        recent_events = system_data.get("recent_events", [])
        
        risk_score = 0.3  # Base risk
        
        # Analyze recent events
        if recent_events:
            high_severity_count = sum(1 for event in recent_events if event.get("severity") in ["HIGH", "CRITICAL"])
            risk_score += (high_severity_count / len(recent_events)) * 0.4
        
        risk_level = "LOW"
        if risk_score > 0.7:
            risk_level = "CRITICAL"
        elif risk_score > 0.5:
            risk_level = "HIGH"
        elif risk_score > 0.3:
            risk_level = "MEDIUM"
        
        return {
            "success": True,
            "system_risk_assessment": {
                "system_id": system_id,
                "overall_risk_score": risk_score,
                "risk_level": risk_level,
                "factors": {
                    "recent_events_count": len(recent_events),
                    "high_severity_events": sum(1 for event in recent_events if event.get("severity") in ["HIGH", "CRITICAL"]),
                    "base_risk": 0.3
                },
                "recommendations": [
                    "Monitor system activity closely",
                    "Review security configurations",
                    "Update security policies"
                ]
            },
            "assessment_timestamp": datetime.now().isoformat()
        }
    
    async def _assess_network_risk(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Assess network-wide risk."""
        network_data = request.get("network", {})
        
        # Mock network risk assessment
        network_id = network_data.get("network_id", "unknown")
        traffic_anomalies = network_data.get("traffic_anomalies", 0)
        failed_connections = network_data.get("failed_connections", 0)
        
        risk_score = 0.2  # Base network risk
        
        # Factor in anomalies
        if traffic_anomalies > 10:
            risk_score += 0.3
        elif traffic_anomalies > 5:
            risk_score += 0.2
        
        # Factor in failed connections
        if failed_connections > 50:
            risk_score += 0.4
        elif failed_connections > 20:
            risk_score += 0.2
        
        risk_level = "LOW"
        if risk_score > 0.7:
            risk_level = "CRITICAL"
        elif risk_score > 0.5:
            risk_level = "HIGH"
        elif risk_score > 0.3:
            risk_level = "MEDIUM"
        
        return {
            "success": True,
            "network_risk_assessment": {
                "network_id": network_id,
                "overall_risk_score": risk_score,
                "risk_level": risk_level,
                "factors": {
                    "traffic_anomalies": traffic_anomalies,
                    "failed_connections": failed_connections,
                    "base_risk": 0.2
                },
                "recommendations": [
                    "Implement network monitoring",
                    "Review firewall rules",
                    "Analyze traffic patterns",
                    "Check for unauthorized access"
                ]
            },
            "assessment_timestamp": datetime.now().isoformat()
        }
    
    def _severity_to_score(self, severity: str) -> float:
        """Convert severity level to numerical score."""
        severity_scores = {
            "INFO": 0.1,
            "LOW": 0.3,
            "MEDIUM": 0.5,
            "HIGH": 0.7,
            "CRITICAL": 0.9
        }
        return severity_scores.get(severity, 0.1)
    
    def _calculate_time_criticality(self, timestamp: datetime) -> float:
        """Calculate time-based criticality."""
        # Events outside business hours are more critical
        hour = timestamp.hour
        if hour < 6 or hour > 22:
            return 0.7
        elif hour < 8 or hour > 18:
            return 0.4
        else:
            return 0.2
    
    def _assess_source_reputation(self, source_ip: str) -> float:
        """Assess source IP reputation (mock implementation)."""
        # In production, this would check against threat intelligence feeds
        if not source_ip:
            return 0.3
        
        # Mock reputation scoring
        if source_ip.startswith("10.") or source_ip.startswith("192.168."):
            return 0.1  # Internal IP - lower risk
        else:
            return 0.5  # External IP - higher risk
    
    def _generate_risk_recommendations(self, risk_score: float, threat_result: ThreatDetectionResult) -> list:
        """Generate risk-based recommendations."""
        recommendations = []
        
        if risk_score > 0.7:
            recommendations.extend([
                "Immediate investigation required",
                "Isolate affected systems",
                "Alert security team"
            ])
        elif risk_score > 0.5:
            recommendations.extend([
                "Monitor closely",
                "Review security logs",
                "Consider additional controls"
            ])
        else:
            recommendations.extend([
                "Continue monitoring",
                "Regular security review"
            ])
        
        if threat_result.threat_detected:
            recommendations.append(f"Address {threat_result.threat_type} threat")
        
        return recommendations