"""
Capacity Prediction Engine
AI-powered capacity prediction using LSTM and Prophet models
"""

import numpy as np
import pandas as pd
import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_absolute_error, mean_squared_error
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LST<PERSON>, <PERSON><PERSON>, Dropout
import prophet
from prophet import Prophet

logger = logging.getLogger(__name__)

class CapacityPredictionEngine:
    """AI-powered capacity prediction engine using multiple ML models"""
    
    def __init__(self):
        self.lstm_model = None
        self.prophet_model = None
        self.rf_model = None
        self.scaler = StandardScaler()
        self.is_initialized = False
        self.prediction_cache = {}
        
        # Model parameters
        self.lstm_lookback = 24  # Hours to look back
        self.prophet_seasonality = True
        self.rf_features = 10
        
        # Performance metrics
        self.predictions_made = 0
        self.total_accuracy = 0.0
        self.model_performance = {}
    
    async def initialize(self):
        """Initialize the capacity prediction models"""
        try:
            logger.info("Initializing Capacity Prediction Engine...")
            
            # Initialize LSTM model for time series prediction
            await self._initialize_lstm_model()
            
            # Initialize Prophet model for seasonal patterns
            await self._initialize_prophet_model()
            
            # Initialize Random Forest for feature-based prediction
            await self._initialize_rf_model()
            
            self.is_initialized = True
            logger.info("Capacity Prediction Engine initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Capacity Prediction Engine: {e}")
            raise
    
    async def shutdown(self):
        """Cleanup resources"""
        logger.info("Shutting down Capacity Prediction Engine...")
        self.lstm_model = None
        self.prophet_model = None
        self.rf_model = None
        self.is_initialized = False
    
    async def predict_capacity(
        self,
        service_context: Dict[str, Any],
        historical_metrics: List[Dict[str, Any]],
        prediction_horizon: str,
        target_sla: Dict[str, float],
        business_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Predict future capacity requirements using ensemble of AI models
        """
        if not self.is_initialized:
            raise RuntimeError("Capacity Prediction Engine not initialized")
        
        try:
            service_id = service_context["service_id"]
            logger.info(f"Predicting capacity for service {service_id}, horizon: {prediction_horizon}")
            
            # Prepare data
            df = self._prepare_data(historical_metrics)
            
            # Generate predictions using multiple models
            lstm_prediction = await self._predict_with_lstm(df, prediction_horizon)
            prophet_prediction = await self._predict_with_prophet(df, prediction_horizon)
            rf_prediction = await self._predict_with_rf(df, service_context, prediction_horizon)
            
            # Ensemble predictions
            ensemble_prediction = self._ensemble_predictions(
                lstm_prediction, prophet_prediction, rf_prediction
            )
            
            # Apply business context and SLA requirements
            adjusted_prediction = self._apply_business_context(
                ensemble_prediction, target_sla, business_context
            )
            
            # Generate recommendations and risk assessment
            recommendations = self._generate_recommendations(
                adjusted_prediction, service_context, target_sla
            )
            risk_factors = self._assess_risks(adjusted_prediction, historical_metrics)
            
            # Calculate cost impact
            cost_impact = self._calculate_cost_impact(adjusted_prediction, service_context)
            
            # Calculate confidence score
            confidence = self._calculate_confidence(
                lstm_prediction, prophet_prediction, rf_prediction
            )
            
            self.predictions_made += 1
            
            result = {
                "requirements": adjusted_prediction,
                "confidence": confidence,
                "accuracy": self._get_historical_accuracy(),
                "recommendations": recommendations,
                "risks": risk_factors,
                "cost_impact": cost_impact,
                "model_details": {
                    "lstm": lstm_prediction,
                    "prophet": prophet_prediction,
                    "random_forest": rf_prediction,
                    "ensemble_weights": {"lstm": 0.4, "prophet": 0.3, "rf": 0.3}
                }
            }
            
            # Cache prediction for future reference
            cache_key = f"{service_id}_{prediction_horizon}_{datetime.now().hour}"
            self.prediction_cache[cache_key] = result
            
            return result
            
        except Exception as e:
            logger.error(f"Capacity prediction failed: {e}")
            raise
    
    def _prepare_data(self, historical_metrics: List[Dict[str, Any]]) -> pd.DataFrame:
        """Prepare historical metrics for ML models"""
        df = pd.DataFrame(historical_metrics)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df = df.sort_values('timestamp')
        
        # Add time-based features
        df['hour'] = df['timestamp'].dt.hour
        df['day_of_week'] = df['timestamp'].dt.dayofweek
        df['day_of_month'] = df['timestamp'].dt.day
        df['month'] = df['timestamp'].dt.month
        
        # Add rolling averages
        for col in ['cpu_utilization', 'memory_utilization', 'request_rate']:
            if col in df.columns:
                df[f'{col}_rolling_3h'] = df[col].rolling(window=3).mean()
                df[f'{col}_rolling_24h'] = df[col].rolling(window=24).mean()
        
        # Fill missing values
        df = df.fillna(method='forward').fillna(method='backward')
        
        return df
    
    async def _predict_with_lstm(self, df: pd.DataFrame, horizon: str) -> Dict[str, float]:
        """Predict using LSTM model for time series"""
        try:
            if self.lstm_model is None:
                return self._fallback_prediction(df, "lstm")
            
            # Prepare sequences for LSTM
            features = ['cpu_utilization', 'memory_utilization', 'request_rate', 'response_time_ms']
            sequences = []
            
            for feature in features:
                if feature in df.columns:
                    values = df[feature].values.reshape(-1, 1)
                    scaled_values = self.scaler.fit_transform(values)
                    sequences.append(scaled_values[-self.lstm_lookback:])
            
            if not sequences:
                return self._fallback_prediction(df, "lstm")
            
            # Create input sequence
            X = np.array(sequences).transpose(1, 0, 2)
            
            # Predict
            prediction = self.lstm_model.predict(X.reshape(1, X.shape[0], X.shape[1]))
            
            # Convert horizon to prediction steps
            steps = self._horizon_to_steps(horizon)
            
            # Scale predictions based on horizon
            scaling_factor = min(1.0 + (steps / 24) * 0.1, 1.5)  # Gradual increase
            
            return {
                "cpu": float(prediction[0][0] * scaling_factor),
                "memory": float(prediction[0][1] * scaling_factor) if len(prediction[0]) > 1 else 70.0,
                "storage": float(prediction[0][2] * scaling_factor) if len(prediction[0]) > 2 else 50.0,
                "network": float(prediction[0][3] * scaling_factor) if len(prediction[0]) > 3 else 30.0
            }
            
        except Exception as e:
            logger.warning(f"LSTM prediction failed: {e}, using fallback")
            return self._fallback_prediction(df, "lstm")
    
    async def _predict_with_prophet(self, df: pd.DataFrame, horizon: str) -> Dict[str, float]:
        """Predict using Prophet model for seasonal patterns"""
        try:
            predictions = {}
            
            for metric in ['cpu_utilization', 'memory_utilization']:
                if metric not in df.columns:
                    continue
                
                # Prepare data for Prophet
                prophet_df = df[['timestamp', metric]].rename(
                    columns={'timestamp': 'ds', metric: 'y'}
                )
                
                # Create and fit Prophet model
                model = Prophet(
                    yearly_seasonality=True,
                    weekly_seasonality=True,
                    daily_seasonality=True,
                    changepoint_prior_scale=0.05
                )
                
                model.fit(prophet_df)
                
                # Make future predictions
                steps = self._horizon_to_steps(horizon)
                future = model.make_future_dataframe(periods=steps, freq='H')
                forecast = model.predict(future)
                
                # Get the prediction for the target horizon
                predicted_value = forecast['yhat'].iloc[-1]
                predictions[metric.replace('_utilization', '')] = max(0, float(predicted_value))
            
            # Add derived predictions
            if 'cpu' in predictions:
                predictions['storage'] = predictions['cpu'] * 0.7  # Estimate based on CPU
                predictions['network'] = predictions['cpu'] * 0.4  # Estimate based on CPU
            
            return predictions
            
        except Exception as e:
            logger.warning(f"Prophet prediction failed: {e}, using fallback")
            return self._fallback_prediction(df, "prophet")
    
    async def _predict_with_rf(
        self, 
        df: pd.DataFrame, 
        service_context: Dict[str, Any], 
        horizon: str
    ) -> Dict[str, float]:
        """Predict using Random Forest based on service features"""
        try:
            if self.rf_model is None:
                return self._fallback_prediction(df, "rf")
            
            # Create feature vector
            features = []
            
            # Time features
            now = datetime.now()
            features.extend([
                now.hour,
                now.weekday(),
                now.day,
                now.month
            ])
            
            # Service features
            features.extend([
                service_context.get('instance_count', 1),
                hash(service_context.get('instance_type', 'unknown')) % 100,
                hash(service_context.get('cloud_provider', 'unknown')) % 10,
                self._horizon_to_steps(horizon)
            ])
            
            # Historical features
            if len(df) > 0:
                features.extend([
                    df['cpu_utilization'].mean(),
                    df['memory_utilization'].mean(),
                    df['cpu_utilization'].std() if len(df) > 1 else 0,
                    df['memory_utilization'].std() if len(df) > 1 else 0
                ])
            else:
                features.extend([50.0, 60.0, 10.0, 15.0])  # Default values
            
            # Pad or truncate features to match model input
            features = features[:self.rf_features] + [0] * max(0, self.rf_features - len(features))
            
            # Predict
            prediction = self.rf_model.predict([features])[0]
            
            return {
                "cpu": max(0, min(100, float(prediction[0]) if hasattr(prediction, '__len__') else float(prediction))),
                "memory": max(0, min(100, float(prediction[1]) if hasattr(prediction, '__len__') and len(prediction) > 1 else 70.0)),
                "storage": max(0, min(100, float(prediction[2]) if hasattr(prediction, '__len__') and len(prediction) > 2 else 50.0)),
                "network": max(0, min(100, float(prediction[3]) if hasattr(prediction, '__len__') and len(prediction) > 3 else 30.0))
            }
            
        except Exception as e:
            logger.warning(f"Random Forest prediction failed: {e}, using fallback")
            return self._fallback_prediction(df, "rf")
    
    def _ensemble_predictions(
        self, 
        lstm_pred: Dict[str, float], 
        prophet_pred: Dict[str, float], 
        rf_pred: Dict[str, float]
    ) -> Dict[str, float]:
        """Combine predictions from multiple models"""
        weights = {"lstm": 0.4, "prophet": 0.3, "rf": 0.3}
        ensemble = {}
        
        all_resources = set(lstm_pred.keys()) | set(prophet_pred.keys()) | set(rf_pred.keys())
        
        for resource in all_resources:
            values = []
            used_weights = []
            
            if resource in lstm_pred:
                values.append(lstm_pred[resource])
                used_weights.append(weights["lstm"])
            
            if resource in prophet_pred:
                values.append(prophet_pred[resource])
                used_weights.append(weights["prophet"])
            
            if resource in rf_pred:
                values.append(rf_pred[resource])
                used_weights.append(weights["rf"])
            
            if values:
                # Normalize weights
                total_weight = sum(used_weights)
                normalized_weights = [w / total_weight for w in used_weights]
                
                # Weighted average
                ensemble[resource] = sum(v * w for v, w in zip(values, normalized_weights))
            else:
                ensemble[resource] = 50.0  # Default value
        
        return ensemble
    
    def _apply_business_context(
        self, 
        prediction: Dict[str, float], 
        target_sla: Dict[str, float], 
        business_context: Dict[str, Any]
    ) -> Dict[str, float]:
        """Apply business context and SLA requirements to predictions"""
        adjusted = prediction.copy()
        
        # Apply SLA buffer
        sla_buffer = target_sla.get('buffer_percentage', 20) / 100
        for resource in adjusted:
            adjusted[resource] *= (1 + sla_buffer)
        
        # Apply business events multiplier
        business_multiplier = business_context.get('expected_load_multiplier', 1.0)
        for resource in adjusted:
            adjusted[resource] *= business_multiplier
        
        # Ensure minimum viable resources
        adjusted['cpu'] = max(adjusted.get('cpu', 0), 10.0)  # Minimum 10% CPU
        adjusted['memory'] = max(adjusted.get('memory', 0), 20.0)  # Minimum 20% memory
        
        return adjusted
    
    def _generate_recommendations(
        self, 
        prediction: Dict[str, float], 
        service_context: Dict[str, Any], 
        target_sla: Dict[str, float]
    ) -> List[str]:
        """Generate actionable recommendations based on predictions"""
        recommendations = []
        
        # High resource usage recommendations
        if prediction.get('cpu', 0) > 80:
            recommendations.append("Consider horizontal scaling due to high predicted CPU usage")
        
        if prediction.get('memory', 0) > 85:
            recommendations.append("Memory optimization recommended - consider memory-optimized instances")
        
        # Low resource usage recommendations
        if all(prediction.get(r, 100) < 30 for r in ['cpu', 'memory']):
            recommendations.append("Consider downsizing instances due to low predicted resource usage")
        
        # SLA-based recommendations
        target_response_time = target_sla.get('max_response_time_ms', 1000)
        if target_response_time < 100:
            recommendations.append("Use high-performance compute instances for strict latency requirements")
        
        # Cloud-specific recommendations
        cloud_provider = service_context.get('cloud_provider', '').lower()
        if cloud_provider == 'aws':
            recommendations.append("Consider AWS Auto Scaling Groups for dynamic capacity management")
        elif cloud_provider == 'gcp':
            recommendations.append("Consider GCP Managed Instance Groups for automatic scaling")
        elif cloud_provider == 'azure':
            recommendations.append("Consider Azure Virtual Machine Scale Sets for elastic scaling")
        
        return recommendations
    
    def _assess_risks(
        self, 
        prediction: Dict[str, float], 
        historical_metrics: List[Dict[str, Any]]
    ) -> List[str]:
        """Assess risks based on predictions and historical patterns"""
        risks = []
        
        # High utilization risks
        if prediction.get('cpu', 0) > 90:
            risks.append("HIGH: CPU utilization may exceed safe limits")
        
        if prediction.get('memory', 0) > 95:
            risks.append("CRITICAL: Memory utilization may cause system instability")
        
        # Volatility risks
        if len(historical_metrics) > 10:
            df = pd.DataFrame(historical_metrics)
            cpu_std = df['cpu_utilization'].std() if 'cpu_utilization' in df.columns else 0
            if cpu_std > 20:
                risks.append("MEDIUM: High CPU utilization volatility detected")
        
        # Capacity planning risks
        if len(historical_metrics) < 24:
            risks.append("LOW: Limited historical data may affect prediction accuracy")
        
        return risks
    
    def _calculate_cost_impact(
        self, 
        prediction: Dict[str, float], 
        service_context: Dict[str, Any]
    ) -> Dict[str, float]:
        """Calculate estimated cost impact of predicted resource usage"""
        # Simplified cost calculation (would be enhanced with real pricing data)
        base_cost = 100.0  # Base monthly cost
        
        cpu_factor = prediction.get('cpu', 50) / 50.0
        memory_factor = prediction.get('memory', 50) / 50.0
        
        estimated_cost = base_cost * max(cpu_factor, memory_factor)
        
        return {
            "estimated_monthly_cost": estimated_cost,
            "cost_change_percentage": (estimated_cost - base_cost) / base_cost * 100,
            "cost_optimization_potential": max(0, 20 - (estimated_cost - base_cost) / base_cost * 100)
        }
    
    def _calculate_confidence(
        self, 
        lstm_pred: Dict[str, float], 
        prophet_pred: Dict[str, float], 
        rf_pred: Dict[str, float]
    ) -> float:
        """Calculate confidence score based on model agreement"""
        all_resources = set(lstm_pred.keys()) | set(prophet_pred.keys()) | set(rf_pred.keys())
        
        agreements = []
        for resource in all_resources:
            values = []
            if resource in lstm_pred:
                values.append(lstm_pred[resource])
            if resource in prophet_pred:
                values.append(prophet_pred[resource])
            if resource in rf_pred:
                values.append(rf_pred[resource])
            
            if len(values) > 1:
                # Calculate coefficient of variation
                mean_val = np.mean(values)
                std_val = np.std(values)
                cv = std_val / mean_val if mean_val > 0 else 1
                agreement = max(0, 1 - cv)
                agreements.append(agreement)
        
        return np.mean(agreements) if agreements else 0.7  # Default confidence
    
    def _get_historical_accuracy(self) -> float:
        """Get historical prediction accuracy"""
        if self.predictions_made == 0:
            return 0.85  # Default accuracy
        return min(0.99, self.total_accuracy / self.predictions_made)
    
    def _horizon_to_steps(self, horizon: str) -> int:
        """Convert prediction horizon to number of time steps"""
        horizon_map = {
            "1h": 1,
            "24h": 24,
            "7d": 168,  # 7 * 24
            "30d": 720   # 30 * 24
        }
        return horizon_map.get(horizon, 24)
    
    def _fallback_prediction(self, df: pd.DataFrame, model_name: str) -> Dict[str, float]:
        """Fallback prediction when models fail"""
        logger.warning(f"Using fallback prediction for {model_name}")
        
        # Use simple statistical prediction based on historical data
        if len(df) > 0:
            cpu_mean = df['cpu_utilization'].mean() if 'cpu_utilization' in df.columns else 50
            memory_mean = df['memory_utilization'].mean() if 'memory_utilization' in df.columns else 60
        else:
            cpu_mean, memory_mean = 50, 60
        
        return {
            "cpu": min(100, cpu_mean * 1.1),  # 10% buffer
            "memory": min(100, memory_mean * 1.1),
            "storage": min(100, cpu_mean * 0.8),
            "network": min(100, cpu_mean * 0.6)
        }
    
    async def _initialize_lstm_model(self):
        """Initialize LSTM model for time series prediction"""
        try:
            self.lstm_model = Sequential([
                LSTM(50, return_sequences=True, input_shape=(self.lstm_lookback, 4)),
                Dropout(0.2),
                LSTM(50, return_sequences=False),
                Dropout(0.2),
                Dense(25),
                Dense(4)  # Predict CPU, Memory, Storage, Network
            ])
            
            self.lstm_model.compile(optimizer='adam', loss='mean_squared_error')
            logger.info("LSTM model initialized successfully")
            
        except Exception as e:
            logger.warning(f"Failed to initialize LSTM model: {e}")
            self.lstm_model = None
    
    async def _initialize_prophet_model(self):
        """Initialize Prophet model for seasonal forecasting"""
        try:
            # Prophet models will be created dynamically for each prediction
            self.prophet_model = True  # Flag to indicate Prophet is available
            logger.info("Prophet model initialized successfully")
            
        except Exception as e:
            logger.warning(f"Failed to initialize Prophet model: {e}")
            self.prophet_model = None
    
    async def _initialize_rf_model(self):
        """Initialize Random Forest model for feature-based prediction"""
        try:
            self.rf_model = RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                n_jobs=-1
            )
            
            # Train with synthetic data for now (would use real data in production)
            X_synthetic = np.random.rand(1000, self.rf_features)
            y_synthetic = np.random.rand(1000, 4) * 100  # 4 resource types
            
            self.rf_model.fit(X_synthetic, y_synthetic)
            logger.info("Random Forest model initialized successfully")
            
        except Exception as e:
            logger.warning(f"Failed to initialize Random Forest model: {e}")
            self.rf_model = None
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get engine performance metrics"""
        return {
            "predictions_made": self.predictions_made,
            "average_accuracy": self._get_historical_accuracy(),
            "models_available": {
                "lstm": self.lstm_model is not None,
                "prophet": self.prophet_model is not None,
                "random_forest": self.rf_model is not None
            },
            "cache_size": len(self.prediction_cache),
            "is_initialized": self.is_initialized
        }