"""Resource Manager Agent Engine - Main interface for resource management AI capabilities."""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from ...core.base_engine import BaseAIEngine
from .capacity_predictor import CapacityPredictor

logger = logging.getLogger(__name__)


class ResourceManagerEngine(BaseAIEngine):
    """Main engine for resource management AI capabilities."""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__("resource_manager_agent", config)
        self.capacity_predictor = CapacityPredictor()
        
    async def initialize(self) -> bool:
        """Initialize the resource manager components."""
        try:
            logger.info("Initializing Resource Manager Engine...")
            
            await self.capacity_predictor.initialize()
            
            self.is_initialized = True
            logger.info("✅ Resource Manager Engine initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Resource Manager Engine: {e}")
            self.is_initialized = False
            return False
    
    async def shutdown(self):
        """Shutdown all components."""
        try:
            await self.capacity_predictor.shutdown()
            self.is_initialized = False
            logger.info("Resource Manager Engine shutdown completed")
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
    
    async def is_healthy(self) -> bool:
        """Check if all components are healthy."""
        if not self.is_initialized:
            return False
        
        try:
            return await self.capacity_predictor.is_healthy()
        except Exception:
            return False
    
    async def get_capabilities(self) -> List[str]:
        """Get list of supported capabilities."""
        return [
            "capacity_prediction",
            "resource_optimization",
            "load_forecasting",
            "resource_allocation",
            "performance_monitoring",
            "scaling_recommendations"
        ]
    
    async def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Process a resource management request."""
        if not self.is_initialized:
            raise RuntimeError("Resource Manager Engine not initialized")
        
        request_type = request.get("type")
        
        try:
            if request_type == "predict_capacity":
                return await self._handle_capacity_prediction(request)
            elif request_type == "optimize_resources":
                return await self._handle_resource_optimization(request)
            elif request_type == "forecast_load":
                return await self._handle_load_forecasting(request)
            elif request_type == "recommend_scaling":
                return await self._handle_scaling_recommendations(request)
            else:
                raise ValueError(f"Unsupported request type: {request_type}")
                
        except Exception as e:
            logger.error(f"Request processing failed: {e}")
            return {
                "status": "error",
                "error": str(e),
                "request_type": request_type
            }
    
    async def _handle_capacity_prediction(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle capacity prediction requests."""
        resource_type = request.get("resource_type", "cpu")
        historical_metrics = request.get("historical_metrics", [])
        time_horizon = request.get("time_horizon", "1h")
        prediction_config = request.get("config", {})
        
        result = await self.capacity_predictor.predict_capacity(
            resource_type=resource_type,
            historical_metrics=historical_metrics,
            time_horizon=time_horizon,
            config=prediction_config
        )
        
        return {
            "status": "success",
            "type": "capacity_prediction",
            "result": result
        }
    
    async def _handle_resource_optimization(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle resource optimization requests."""
        current_resources = request.get("current_resources", {})
        workload_requirements = request.get("workload_requirements", {})
        optimization_goals = request.get("optimization_goals", ["cost", "performance"])
        constraints = request.get("constraints", {})
        
        result = await self.capacity_predictor.optimize_resources(
            current_resources=current_resources,
            workload_requirements=workload_requirements,
            optimization_goals=optimization_goals,
            constraints=constraints
        )
        
        return {
            "status": "success",
            "type": "resource_optimization", 
            "result": result
        }
    
    async def _handle_load_forecasting(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle load forecasting requests."""
        service_id = request.get("service_id", "unknown")
        historical_load = request.get("historical_load", [])
        forecast_horizon = request.get("forecast_horizon", "24h")
        config = request.get("config", {})
        
        result = await self.capacity_predictor.forecast_load(
            service_id=service_id,
            historical_load=historical_load,
            forecast_horizon=forecast_horizon,
            config=config
        )
        
        return {
            "status": "success",
            "type": "load_forecasting",
            "result": result
        }
    
    async def _handle_scaling_recommendations(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle scaling recommendation requests."""
        service_metrics = request.get("service_metrics", {})
        performance_targets = request.get("performance_targets", {})
        cost_constraints = request.get("cost_constraints", {})
        
        result = await self.capacity_predictor.recommend_scaling(
            service_metrics=service_metrics,
            performance_targets=performance_targets,
            cost_constraints=cost_constraints
        )
        
        return {
            "status": "success",
            "type": "scaling_recommendations",
            "result": result
        }
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get metrics from capacity predictor."""
        try:
            predictor_metrics = await self.capacity_predictor.get_metrics()
            
            return {
                "engine_status": {
                    "is_initialized": self.is_initialized,
                    "is_healthy": await self.is_healthy()
                },
                "capacity_prediction": predictor_metrics
            }
        except Exception as e:
            logger.error(f"Failed to get metrics: {e}")
            return {
                "engine_status": {
                    "is_initialized": self.is_initialized,
                    "is_healthy": False,
                    "error": str(e)
                }
            }