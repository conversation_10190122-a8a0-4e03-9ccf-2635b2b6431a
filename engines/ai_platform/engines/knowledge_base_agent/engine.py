"""Knowledge Base Agent Engine - Main interface for knowledge processing AI capabilities."""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from ...core.base_engine import BaseAIEngine
from .knowledge_extraction_engine import KnowledgeExtractionEngine
from .pattern_recognition_engine import PatternRecognitionEngine
from .semantic_processing_engine import SemanticProcessingEngine
from .recommendation_engine import RecommendationEngine

logger = logging.getLogger(__name__)


class KnowledgeBaseEngine(BaseAIEngine):
    """Main engine for knowledge base AI capabilities."""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__("knowledge_base_agent", config)
        self.knowledge_extractor = KnowledgeExtractionEngine()
        self.pattern_recognizer = PatternRecognitionEngine()
        self.semantic_processor = SemanticProcessingEngine()
        self.recommender = RecommendationEngine()
        
    async def initialize(self) -> bool:
        """Initialize all knowledge base components."""
        try:
            logger.info("Initializing Knowledge Base Engine...")
            
            # Initialize all components
            await asyncio.gather(
                self.knowledge_extractor.initialize(),
                self.pattern_recognizer.initialize(),
                self.semantic_processor.initialize(),
                self.recommender.initialize()
            )
            
            self.is_initialized = True
            logger.info("✅ Knowledge Base Engine initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Knowledge Base Engine: {e}")
            self.is_initialized = False
            return False
    
    async def shutdown(self):
        """Shutdown all components."""
        try:
            await asyncio.gather(
                self.knowledge_extractor.shutdown(),
                self.pattern_recognizer.shutdown(),
                self.semantic_processor.shutdown(),
                self.recommender.shutdown(),
                return_exceptions=True
            )
            self.is_initialized = False
            logger.info("Knowledge Base Engine shutdown completed")
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
    
    async def is_healthy(self) -> bool:
        """Check if all components are healthy."""
        if not self.is_initialized:
            return False
        
        try:
            health_checks = await asyncio.gather(
                self.knowledge_extractor.is_healthy(),
                self.pattern_recognizer.is_healthy(),
                self.semantic_processor.is_healthy(),
                self.recommender.is_healthy(),
                return_exceptions=True
            )
            return all(isinstance(result, bool) and result for result in health_checks)
        except Exception:
            return False
    
    async def get_capabilities(self) -> List[str]:
        """Get list of supported capabilities."""
        return [
            "knowledge_extraction",
            "pattern_recognition",
            "semantic_processing",
            "recommendation_generation",
            "entity_extraction",
            "relationship_discovery"
        ]
    
    async def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Process a knowledge base request."""
        if not self.is_initialized:
            raise RuntimeError("Knowledge Base Engine not initialized")
        
        request_type = request.get("type")
        
        try:
            if request_type == "extract_knowledge":
                return await self._handle_knowledge_extraction(request)
            elif request_type == "recognize_patterns":
                return await self._handle_pattern_recognition(request)
            elif request_type == "process_semantics":
                return await self._handle_semantic_processing(request)
            elif request_type == "generate_recommendations":
                return await self._handle_recommendation_generation(request)
            elif request_type == "analyze_content":
                return await self._handle_content_analysis(request)
            else:
                raise ValueError(f"Unsupported request type: {request_type}")
                
        except Exception as e:
            logger.error(f"Request processing failed: {e}")
            return {
                "status": "error",
                "error": str(e),
                "request_type": request_type
            }
    
    async def _handle_knowledge_extraction(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle knowledge extraction requests."""
        content = request.get("content", "")
        content_type = request.get("content_type", "text")
        extraction_config = request.get("config", {})
        
        result = await self.knowledge_extractor.extract_knowledge(
            content=content,
            content_type=content_type,
            config=extraction_config
        )
        
        return {
            "status": "success",
            "type": "knowledge_extraction",
            "result": result
        }
    
    async def _handle_pattern_recognition(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle pattern recognition requests."""
        data = request.get("data", [])
        pattern_types = request.get("pattern_types", ["sequence", "frequency", "anomaly"])
        config = request.get("config", {})
        
        result = await self.pattern_recognizer.recognize_patterns(
            data=data,
            pattern_types=pattern_types,
            config=config
        )
        
        return {
            "status": "success",
            "type": "pattern_recognition",
            "result": result
        }
    
    async def _handle_semantic_processing(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle semantic processing requests."""
        text = request.get("text", "")
        operations = request.get("operations", ["similarity", "sentiment", "entities"])
        config = request.get("config", {})
        
        result = await self.semantic_processor.process_text(
            text=text,
            operations=operations,
            config=config
        )
        
        return {
            "status": "success",
            "type": "semantic_processing",
            "result": result
        }
    
    async def _handle_recommendation_generation(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle recommendation generation requests."""
        user_profile = request.get("user_profile", {})
        context = request.get("context", {})
        recommendation_type = request.get("recommendation_type", "general")
        config = request.get("config", {})
        
        result = await self.recommender.generate_recommendations(
            user_profile=user_profile,
            context=context,
            recommendation_type=recommendation_type,
            config=config
        )
        
        return {
            "status": "success",
            "type": "recommendation_generation",
            "result": result
        }
    
    async def _handle_content_analysis(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle comprehensive content analysis requests."""
        content = request.get("content", "")
        analysis_types = request.get("analysis_types", ["knowledge", "patterns", "semantics"])
        
        results = {}
        tasks = []
        
        if "knowledge" in analysis_types:
            tasks.append(("knowledge", self.knowledge_extractor.extract_knowledge(content)))
        if "patterns" in analysis_types:
            tasks.append(("patterns", self.pattern_recognizer.recognize_patterns([content])))
        if "semantics" in analysis_types:
            tasks.append(("semantics", self.semantic_processor.process_text(content)))
        
        # Execute all tasks in parallel
        completed_tasks = await asyncio.gather(
            *[task[1] for task in tasks], return_exceptions=True
        )
        
        for i, (name, _) in enumerate(tasks):
            result = completed_tasks[i]
            if not isinstance(result, Exception):
                results[name] = result
            else:
                results[name] = {"error": str(result)}
        
        return {
            "status": "success",
            "type": "content_analysis",
            "result": {
                "content_summary": {
                    "length": len(content),
                    "type": "text"
                },
                "analysis_results": results
            }
        }
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get combined metrics from all components."""
        try:
            extraction_metrics = await self.knowledge_extractor.get_metrics()
            pattern_metrics = await self.pattern_recognizer.get_metrics()
            semantic_metrics = await self.semantic_processor.get_metrics()
            recommendation_metrics = await self.recommender.get_metrics()
            
            return {
                "engine_status": {
                    "is_initialized": self.is_initialized,
                    "is_healthy": await self.is_healthy()
                },
                "knowledge_extraction": extraction_metrics,
                "pattern_recognition": pattern_metrics,
                "semantic_processing": semantic_metrics,
                "recommendation_engine": recommendation_metrics
            }
        except Exception as e:
            logger.error(f"Failed to get metrics: {e}")
            return {
                "engine_status": {
                    "is_initialized": self.is_initialized,
                    "is_healthy": False,
                    "error": str(e)
                }
            }