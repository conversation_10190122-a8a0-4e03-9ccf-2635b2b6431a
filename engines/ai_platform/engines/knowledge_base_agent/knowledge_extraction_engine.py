"""
Knowledge Extraction Engine - AI-powered knowledge extraction from multiple sources.
"""

import asyncio
import json
import time
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from dataclasses import dataclass

import openai
from loguru import logger
import pandas as pd
from sentence_transformers import SentenceTransformer
import spacy
from transformers import pipeline

from ..config.settings import get_settings
from ..utils.metrics import MetricsCollector


@dataclass
class KnowledgeItem:
    """Extracted knowledge item."""
    knowledge_id: str
    title: str
    content: str
    content_type: str
    entities: List[Dict[str, Any]]
    relationships: List[Dict[str, Any]]
    classification: Dict[str, Any]
    quality_score: float
    confidence_score: float
    vector_embedding: Optional[List[float]] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class ExtractionRequest:
    """Knowledge extraction request."""
    extraction_id: str
    content: str
    source_type: str
    extraction_config: Dict[str, Any]
    target_categories: List[str]
    context: Optional[str] = None


@dataclass
class ExtractionResult:
    """Knowledge extraction result."""
    extraction_id: str
    status: str
    extracted_knowledge: List[KnowledgeItem]
    extraction_metrics: Dict[str, Any]
    quality_assessment: Dict[str, Any]
    processing_time_ms: int
    error_messages: List[str]


class KnowledgeExtractionEngine:
    """
    AI-powered knowledge extraction engine.
    
    Capabilities:
    - Multi-format document processing
    - Entity recognition and relationship mapping
    - Content classification and quality assessment
    - Vector embedding generation
    - Real-time knowledge extraction
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.metrics = MetricsCollector("knowledge_extraction")
        
        # AI models
        self.openai_client = None
        self.embedding_model = None
        self.nlp_model = None
        self.classifier = None
        
        # State tracking
        self.is_initialized = False
        self.extractions_completed = 0
        self.total_processing_time = 0.0
        
        logger.info("Knowledge Extraction Engine created")
    
    async def initialize(self):
        """Initialize the knowledge extraction engine."""
        try:
            logger.info("Initializing Knowledge Extraction Engine...")
            
            # Initialize OpenAI client
            if self.settings.openai_api_key:
                self.openai_client = openai.AsyncOpenAI(
                    api_key=self.settings.openai_api_key
                )
                logger.info("✅ OpenAI client initialized")
            
            # Initialize embedding model
            self.embedding_model = SentenceTransformer('all-mpnet-base-v2')
            logger.info("✅ Sentence transformer model loaded")
            
            # Initialize NLP model for entity recognition
            try:
                self.nlp_model = spacy.load("en_core_web_sm")
            except OSError:
                logger.warning("spaCy model not found, downloading...")
                import subprocess
                subprocess.run(["python", "-m", "spacy", "download", "en_core_web_sm"])
                self.nlp_model = spacy.load("en_core_web_sm")
            logger.info("✅ spaCy NLP model loaded")
            
            # Initialize text classifier
            self.classifier = pipeline(
                "zero-shot-classification",
                model="facebook/bart-large-mnli"
            )
            logger.info("✅ Text classifier initialized")
            
            self.is_initialized = True
            logger.info("🎯 Knowledge Extraction Engine initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Knowledge Extraction Engine: {e}")
            raise
    
    async def shutdown(self):
        """Shutdown the knowledge extraction engine."""
        logger.info("Shutting down Knowledge Extraction Engine...")
        self.is_initialized = False
        logger.info("✅ Knowledge Extraction Engine shutdown complete")
    
    async def extract_knowledge(self, request: ExtractionRequest) -> ExtractionResult:
        """
        Extract knowledge from content using AI.
        
        Args:
            request: Extraction request with content and configuration
            
        Returns:
            ExtractionResult with extracted knowledge items
        """
        if not self.is_initialized:
            raise RuntimeError("Knowledge Extraction Engine not initialized")
        
        start_time = time.time()
        
        try:
            logger.info(f"Extracting knowledge: {request.extraction_id}")
            
            # 1. Preprocess content
            processed_content = await self._preprocess_content(request.content)
            
            # 2. Extract entities and relationships
            entities = await self._extract_entities(processed_content)
            relationships = await self._extract_relationships(processed_content, entities)
            
            # 3. Classify content
            classification = await self._classify_content(
                processed_content, request.target_categories
            )
            
            # 4. Generate embeddings
            embeddings = await self._generate_embeddings(processed_content)
            
            # 5. Extract structured knowledge using LLM
            structured_knowledge = await self._extract_structured_knowledge(
                processed_content, entities, relationships, request.context
            )
            
            # 6. Assess quality
            quality_score = await self._assess_quality(
                processed_content, structured_knowledge
            )
            
            # 7. Create knowledge items
            knowledge_items = []
            for i, knowledge in enumerate(structured_knowledge):
                item = KnowledgeItem(
                    knowledge_id=f"{request.extraction_id}_{i}",
                    title=knowledge.get("title", f"Knowledge Item {i+1}"),
                    content=knowledge.get("content", ""),
                    content_type=request.source_type,
                    entities=entities,
                    relationships=relationships,
                    classification=classification,
                    quality_score=quality_score,
                    confidence_score=knowledge.get("confidence", 0.8),
                    vector_embedding=embeddings,
                    metadata={
                        "extraction_method": "ai_powered",
                        "extraction_timestamp": datetime.now().isoformat(),
                        "source_length": len(request.content),
                        "processing_version": "1.0.0"
                    }
                )
                knowledge_items.append(item)
            
            processing_time = int((time.time() - start_time) * 1000)
            
            # 8. Create extraction metrics
            extraction_metrics = {
                "total_content_processed": len(request.content),
                "knowledge_items_extracted": len(knowledge_items),
                "entities_identified": len(entities),
                "relationships_mapped": len(relationships),
                "average_quality_score": quality_score,
                "average_confidence_score": sum(
                    item.confidence_score for item in knowledge_items
                ) / len(knowledge_items) if knowledge_items else 0.0
            }
            
            # 9. Quality assessment
            quality_assessment = {
                "overall_quality_score": quality_score,
                "completeness_score": await self._assess_completeness(knowledge_items),
                "accuracy_score": await self._assess_accuracy(knowledge_items),
                "relevance_score": await self._assess_relevance(knowledge_items, request.context),
                "consistency_score": await self._assess_consistency(knowledge_items),
                "quality_issues": [],
                "improvement_recommendations": await self._generate_improvement_recommendations(
                    knowledge_items, quality_score
                )
            }
            
            # Update metrics
            self.extractions_completed += 1
            self.total_processing_time += processing_time
            
            result = ExtractionResult(
                extraction_id=request.extraction_id,
                status="completed",
                extracted_knowledge=knowledge_items,
                extraction_metrics=extraction_metrics,
                quality_assessment=quality_assessment,
                processing_time_ms=processing_time,
                error_messages=[]
            )
            
            logger.info(f"✅ Knowledge extraction completed: {len(knowledge_items)} items in {processing_time}ms")
            return result
            
        except Exception as e:
            processing_time = int((time.time() - start_time) * 1000)
            error_msg = f"Knowledge extraction failed: {str(e)}"
            logger.error(error_msg)
            
            return ExtractionResult(
                extraction_id=request.extraction_id,
                status="failed",
                extracted_knowledge=[],
                extraction_metrics={},
                quality_assessment={},
                processing_time_ms=processing_time,
                error_messages=[error_msg]
            )
    
    async def _preprocess_content(self, content: str) -> str:
        """Preprocess content for extraction."""
        # Basic text cleaning and normalization
        processed = content.strip()
        processed = ' '.join(processed.split())  # Normalize whitespace
        return processed
    
    async def _extract_entities(self, content: str) -> List[Dict[str, Any]]:
        """Extract named entities from content."""
        if not self.nlp_model:
            return []
        
        doc = self.nlp_model(content)
        entities = []
        
        for ent in doc.ents:
            entity = {
                "entity_id": f"ent_{len(entities)}",
                "entity_type": ent.label_,
                "entity_value": ent.text,
                "entity_context": ent.sent.text if ent.sent else "",
                "confidence": 0.9,  # Simplified confidence
                "start_position": ent.start_char,
                "end_position": ent.end_char
            }
            entities.append(entity)
        
        return entities
    
    async def _extract_relationships(self, content: str, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Extract relationships between entities."""
        relationships = []
        
        # Simple relationship extraction based on entity proximity
        for i, entity1 in enumerate(entities):
            for j, entity2 in enumerate(entities[i+1:], i+1):
                # Check if entities are in the same sentence
                if abs(entity1["start_position"] - entity2["start_position"]) < 200:
                    relationship = {
                        "relationship_id": f"rel_{len(relationships)}",
                        "source_entity": entity1["entity_value"],
                        "target_entity": entity2["entity_value"],
                        "relationship_type": "co_occurrence",
                        "relationship_strength": 0.7,
                        "context": content[
                            min(entity1["start_position"], entity2["start_position"]):
                            max(entity1["end_position"], entity2["end_position"])
                        ],
                        "created_at": datetime.now().isoformat()
                    }
                    relationships.append(relationship)
        
        return relationships[:10]  # Limit to top 10 relationships
    
    async def _classify_content(self, content: str, target_categories: List[str]) -> Dict[str, Any]:
        """Classify content into categories."""
        if not self.classifier or not target_categories:
            return {
                "primary_category": "general",
                "secondary_categories": [],
                "knowledge_type": "factual",
                "complexity_score": 0.5,
                "relevance_score": 0.8,
                "importance_level": "medium"
            }
        
        try:
            result = self.classifier(content[:512], target_categories)  # Limit text length
            
            return {
                "primary_category": result["labels"][0] if result["labels"] else "general",
                "secondary_categories": result["labels"][1:3] if len(result["labels"]) > 1 else [],
                "knowledge_type": "factual",
                "complexity_score": min(0.9, len(content) / 1000),  # Simple complexity metric
                "relevance_score": result["scores"][0] if result["scores"] else 0.8,
                "importance_level": "high" if result["scores"][0] > 0.8 else "medium"
            }
        except Exception as e:
            logger.warning(f"Classification failed: {e}")
            return {
                "primary_category": "general",
                "secondary_categories": [],
                "knowledge_type": "factual",
                "complexity_score": 0.5,
                "relevance_score": 0.8,
                "importance_level": "medium"
            }
    
    async def _generate_embeddings(self, content: str) -> List[float]:
        """Generate vector embeddings for content."""
        if not self.embedding_model:
            return []
        
        try:
            # Generate embeddings using sentence transformer
            embeddings = self.embedding_model.encode(content[:512])  # Limit text length
            return embeddings.tolist()
        except Exception as e:
            logger.warning(f"Embedding generation failed: {e}")
            return []
    
    async def _extract_structured_knowledge(
        self, content: str, entities: List[Dict], relationships: List[Dict], context: Optional[str]
    ) -> List[Dict[str, Any]]:
        """Extract structured knowledge using LLM."""
        if not self.openai_client:
            # Fallback to simple extraction
            return [{
                "title": "Extracted Knowledge",
                "content": content[:500],
                "confidence": 0.7,
                "type": "factual"
            }]
        
        try:
            prompt = f"""
            Extract structured knowledge from the following content:
            
            Content: {content[:1000]}
            Context: {context or 'General knowledge extraction'}
            Entities Found: {[e['entity_value'] for e in entities[:5]]}
            
            Extract key insights, facts, and actionable information.
            Format as JSON with title, content, confidence, and type fields.
            Provide 1-3 knowledge items maximum.
            """
            
            response = await self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are an expert knowledge extraction AI."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.2,
                max_tokens=500
            )
            
            result_text = response.choices[0].message.content
            
            # Try to parse JSON response
            try:
                structured_knowledge = json.loads(result_text)
                if isinstance(structured_knowledge, dict):
                    structured_knowledge = [structured_knowledge]
                return structured_knowledge
            except json.JSONDecodeError:
                # Fallback if JSON parsing fails
                return [{
                    "title": "AI Extracted Knowledge",
                    "content": result_text,
                    "confidence": 0.8,
                    "type": "ai_generated"
                }]
                
        except Exception as e:
            logger.warning(f"LLM extraction failed: {e}")
            return [{
                "title": "Extracted Knowledge",
                "content": content[:500],
                "confidence": 0.6,
                "type": "fallback"
            }]
    
    async def _assess_quality(self, content: str, knowledge_items: List[Dict]) -> float:
        """Assess quality of extracted knowledge."""
        if not knowledge_items:
            return 0.0
        
        # Simple quality scoring based on various factors
        content_length_score = min(1.0, len(content) / 1000)
        knowledge_count_score = min(1.0, len(knowledge_items) / 5)
        confidence_score = sum(item.get("confidence", 0.5) for item in knowledge_items) / len(knowledge_items)
        
        return (content_length_score + knowledge_count_score + confidence_score) / 3
    
    async def _assess_completeness(self, knowledge_items: List[KnowledgeItem]) -> float:
        """Assess completeness of extracted knowledge."""
        if not knowledge_items:
            return 0.0
        
        # Simple completeness check
        complete_items = sum(
            1 for item in knowledge_items 
            if item.title and item.content and len(item.content) > 10
        )
        return complete_items / len(knowledge_items)
    
    async def _assess_accuracy(self, knowledge_items: List[KnowledgeItem]) -> float:
        """Assess accuracy of extracted knowledge."""
        # Simplified accuracy assessment based on confidence scores
        if not knowledge_items:
            return 0.0
        
        return sum(item.confidence_score for item in knowledge_items) / len(knowledge_items)
    
    async def _assess_relevance(self, knowledge_items: List[KnowledgeItem], context: Optional[str]) -> float:
        """Assess relevance of extracted knowledge to context."""
        # Simplified relevance assessment
        return 0.8  # Default relevance score
    
    async def _assess_consistency(self, knowledge_items: List[KnowledgeItem]) -> float:
        """Assess consistency across extracted knowledge items."""
        # Simplified consistency check
        return 0.85  # Default consistency score
    
    async def _generate_improvement_recommendations(
        self, knowledge_items: List[KnowledgeItem], quality_score: float
    ) -> List[str]:
        """Generate recommendations for improving knowledge extraction."""
        recommendations = []
        
        if quality_score < 0.7:
            recommendations.append("Consider providing more context for better extraction")
        
        if not knowledge_items:
            recommendations.append("No knowledge extracted - check content quality and format")
        
        avg_entities = sum(len(item.entities) for item in knowledge_items) / len(knowledge_items) if knowledge_items else 0
        if avg_entities < 2:
            recommendations.append("Content may benefit from more detailed entity information")
        
        return recommendations
    
    async def health_check(self) -> Dict[str, Any]:
        """Check engine health."""
        return {
            "healthy": self.is_initialized,
            "models_loaded": {
                "openai": self.openai_client is not None,
                "embeddings": self.embedding_model is not None,
                "nlp": self.nlp_model is not None,
                "classifier": self.classifier is not None
            },
            "extractions_completed": self.extractions_completed,
            "average_processing_time_ms": (
                self.total_processing_time / self.extractions_completed
                if self.extractions_completed > 0 else 0
            )
        }
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get engine metrics."""
        return {
            "extractions_completed": self.extractions_completed,
            "total_processing_time_ms": self.total_processing_time,
            "average_processing_time_ms": (
                self.total_processing_time / self.extractions_completed
                if self.extractions_completed > 0 else 0
            ),
            "models_initialized": self.is_initialized,
            "engine_status": "operational" if self.is_initialized else "not_initialized"
        }