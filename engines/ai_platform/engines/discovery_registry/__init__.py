"""
Discovery Registry AI Engine

This package provides AI-powered capabilities for service discovery and registry operations,
including capability matching, service optimization, and health prediction.
"""

from .engine import DiscoveryRegistryEngine
from .capability_matcher import (
    CapabilityMatchingAI,
    ServiceInfo,
    ServiceRequirements,
    ServiceMatch,
    DetailedMatchAnalysis
)
from .service_optimizer import (
    ServiceOptimizationAI,
    OptimizationRecommendation
)
from .health_predictor import (
    HealthPredictionAI,
    ServiceMetrics,
    HealthPrediction,
    ServiceStatus
)

__all__ = [
    # Main engine
    "DiscoveryRegistryEngine",
    
    # Capability matching
    "CapabilityMatchingAI",
    "ServiceInfo",
    "ServiceRequirements", 
    "ServiceMatch",
    "DetailedMatchAnalysis",
    
    # Service optimization
    "ServiceOptimizationAI",
    "OptimizationRecommendation",
    
    # Health prediction
    "HealthPredictionAI",
    "ServiceMetrics",
    "HealthPrediction",
    "ServiceStatus",
]