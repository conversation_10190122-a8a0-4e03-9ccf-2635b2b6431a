"""
Discovery Registry AI Engine

This module provides the main AI engine for service discovery and registry operations,
integrating capability matching, service optimization, and health prediction capabilities.
"""

import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime
import logging
logger = logging.getLogger(__name__)

from ai_platform.core.base_engine import BaseAIEngine
from .capability_matcher import CapabilityMatchingAI, ServiceInfo, ServiceRequirements, ServiceMatch
from .service_optimizer import ServiceOptimizationAI, OptimizationRecommendation
from .health_predictor import HealthPredictionAI, ServiceMetrics, HealthPrediction, ServiceStatus


class DiscoveryRegistryEngine(BaseAIEngine):
    """
    AI Engine for service discovery and registry operations.
    
    Provides intelligent capabilities for:
    - Service capability matching and discovery
    - Service mesh optimization recommendations
    - Health prediction and monitoring
    """

    def __init__(self):
        super().__init__("DiscoveryRegistry")
        
        # AI subsystems
        self.capability_matcher: Optional[CapabilityMatchingAI] = None
        self.service_optimizer: Optional[ServiceOptimizationAI] = None
        self.health_predictor: Optional[HealthPredictionAI] = None
        
        # Engine state
        self.subsystem_health: Dict[str, bool] = {}

    async def initialize(self) -> bool:
        """
        Initialize the discovery registry AI engine and all subsystems.
        
        Returns:
            bool: True if initialization successful, False otherwise
        """
        try:
            logger.info("Initializing Discovery Registry AI Engine...")
            
            # Initialize all AI subsystems
            await self._initialize_subsystems()
            
            # Verify subsystem health
            health_status = await self._check_subsystem_health()
            
            if not any(health_status.values()):
                logger.error("All AI subsystems failed to initialize")
                return False
                
            # Log subsystem status
            for subsystem, healthy in health_status.items():
                status = "✅ healthy" if healthy else "❌ failed"
                logger.info(f"  {subsystem}: {status}")
            
            logger.info("Discovery Registry AI Engine initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Discovery Registry AI Engine: {e}")
            return False

    async def process(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process an AI request for discovery registry operations.
        
        Args:
            request: Request containing operation type and parameters
            
        Returns:
            Dict containing the processing results
        """
        try:
            operation = request.get("operation")
            if not operation:
                return {"error": "No operation specified"}
            
            # Route to appropriate handler
            if operation == "match_services":
                return await self._handle_service_matching(request)
            elif operation == "optimize_services":
                return await self._handle_service_optimization(request)
            elif operation == "predict_health":
                return await self._handle_health_prediction(request)
            elif operation == "update_metrics":
                return await self._handle_metrics_update(request)
            elif operation == "comprehensive_analysis":
                return await self._handle_comprehensive_analysis(request)
            else:
                return {"error": f"Unknown operation: {operation}"}
                
        except Exception as e:
            logger.error(f"Processing error in Discovery Registry Engine: {e}")
            return {"error": str(e)}

    async def cleanup(self) -> None:
        """Clean up resources when shutting down."""
        try:
            logger.info("Cleaning up Discovery Registry AI Engine...")
            
            # Cleanup subsystems if needed
            # (Currently no explicit cleanup required for our subsystems)
            
            logger.info("Discovery Registry AI Engine cleanup complete")
            
        except Exception as e:
            logger.error(f"Cleanup error: {e}")

    async def health_check(self) -> Dict[str, Any]:
        """
        Enhanced health check including subsystem status.
        
        Returns:
            Dict containing detailed health status
        """
        base_health = await super().health_check()
        
        # Add subsystem health information
        subsystem_health = await self._check_subsystem_health()
        
        base_health.update({
            "subsystems": subsystem_health,
            "capabilities": {
                "service_matching": subsystem_health.get("capability_matcher", False),
                "service_optimization": subsystem_health.get("service_optimizer", False),
                "health_prediction": subsystem_health.get("health_predictor", False),
            },
            "overall_ai_health": any(subsystem_health.values())
        })
        
        return base_health

    # Private methods for subsystem management

    async def _initialize_subsystems(self) -> None:
        """Initialize all AI subsystems."""
        try:
            # Initialize capability matcher
            self.capability_matcher = CapabilityMatchingAI()
            await self.capability_matcher.initialize()
            
            # Initialize service optimizer
            self.service_optimizer = ServiceOptimizationAI()
            await self.service_optimizer.initialize()
            
            # Initialize health predictor
            self.health_predictor = HealthPredictionAI()
            await self.health_predictor.initialize()
            
        except Exception as e:
            logger.error(f"Subsystem initialization failed: {e}")
            raise

    async def _check_subsystem_health(self) -> Dict[str, bool]:
        """Check health of all subsystems."""
        health_status = {}
        
        # Check capability matcher
        try:
            health_status["capability_matcher"] = (
                self.capability_matcher is not None and 
                await self.capability_matcher.is_healthy()
            )
        except Exception:
            health_status["capability_matcher"] = False
        
        # Check service optimizer
        try:
            health_status["service_optimizer"] = (
                self.service_optimizer is not None and 
                await self.service_optimizer.is_healthy()
            )
        except Exception:
            health_status["service_optimizer"] = False
        
        # Check health predictor
        try:
            health_status["health_predictor"] = (
                self.health_predictor is not None and 
                await self.health_predictor.is_healthy()
            )
        except Exception:
            health_status["health_predictor"] = False
        
        self.subsystem_health = health_status
        return health_status

    # Request handlers

    async def _handle_service_matching(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle service capability matching requests."""
        try:
            if not self.capability_matcher:
                return {"error": "Capability matcher not initialized"}
            
            # Extract requirements and available services
            requirements_data = request.get("requirements", {})
            services_data = request.get("services", [])
            
            # Convert to proper objects
            requirements = ServiceRequirements(
                description=requirements_data.get("description", ""),
                capabilities=requirements_data.get("capabilities", []),
                protocols=requirements_data.get("protocols", []),
                tags=requirements_data.get("tags", []),
                min_availability=requirements_data.get("min_availability", 0.99),
                max_response_time=requirements_data.get("max_response_time", 1000.0),
                max_error_rate=requirements_data.get("max_error_rate", 0.01),
                namespace=requirements_data.get("namespace", ""),
            )
            
            services = []
            for service_data in services_data:
                service = ServiceInfo(
                    id=service_data.get("id", ""),
                    name=service_data.get("name", ""),
                    description=service_data.get("description", ""),
                    capabilities=service_data.get("capabilities", []),
                    tags=service_data.get("tags", []),
                    endpoints=service_data.get("endpoints", []),
                    metrics=service_data.get("metrics"),
                    namespace=service_data.get("namespace", ""),
                )
                services.append(service)
            
            # Perform matching
            matches = await self.capability_matcher.match_services_to_requirements(
                requirements, services
            )
            
            # Convert results to serializable format
            result_matches = []
            for match in matches:
                result_matches.append({
                    "service": match.service.to_dict(),
                    "similarity_score": match.similarity_score,
                    "confidence": match.confidence,
                    "reasoning": match.reasoning,
                    "match_details": match.match_details,
                })
            
            return {
                "matches": result_matches,
                "total_matches": len(result_matches),
                "timestamp": datetime.utcnow().isoformat(),
            }
            
        except Exception as e:
            logger.error(f"Service matching failed: {e}")
            return {"error": f"Service matching failed: {str(e)}"}

    async def _handle_service_optimization(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle service optimization requests."""
        try:
            if not self.service_optimizer:
                return {"error": "Service optimizer not initialized"}
            
            # Extract service mesh context
            context = request.get("context", {})
            
            # Generate optimization recommendations
            recommendations = await self.service_optimizer.generate_recommendations(context)
            
            # Convert to serializable format
            result_recommendations = []
            for rec in recommendations:
                result_recommendations.append({
                    "category": rec.category,
                    "title": rec.title,
                    "description": rec.description,
                    "impact_score": rec.impact_score,
                    "implementation_effort": rec.implementation_effort,
                    "priority": rec.priority,
                    "configuration_changes": rec.configuration_changes,
                    "expected_benefits": rec.expected_benefits,
                })
            
            return {
                "recommendations": result_recommendations,
                "total_recommendations": len(result_recommendations),
                "timestamp": datetime.utcnow().isoformat(),
            }
            
        except Exception as e:
            logger.error(f"Service optimization failed: {e}")
            return {"error": f"Service optimization failed: {str(e)}"}

    async def _handle_health_prediction(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle health prediction requests."""
        try:
            if not self.health_predictor:
                return {"error": "Health predictor not initialized"}
            
            service_id = request.get("service_id")
            if not service_id:
                return {"error": "Service ID required"}
            
            # Get health prediction
            prediction = await self.health_predictor.predict_service_health(service_id)
            
            if not prediction:
                return {"error": "Unable to generate health prediction"}
            
            # Convert to serializable format
            predictions_dict = {}
            for horizon, status in prediction.predictions.items():
                predictions_dict[horizon] = status.value if isinstance(status, ServiceStatus) else str(status)
            
            return {
                "service_id": prediction.service_id,
                "predictions": predictions_dict,
                "confidence_scores": prediction.confidence_scores,
                "risk_factors": prediction.risk_factors,
                "recommended_actions": prediction.recommended_actions,
                "prediction_timestamp": prediction.prediction_timestamp.isoformat(),
            }
            
        except Exception as e:
            logger.error(f"Health prediction failed: {e}")
            return {"error": f"Health prediction failed: {str(e)}"}

    async def _handle_metrics_update(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle service metrics updates for health prediction."""
        try:
            if not self.health_predictor:
                return {"error": "Health predictor not initialized"}
            
            service_id = request.get("service_id")
            metrics_data = request.get("metrics", {})
            
            if not service_id or not metrics_data:
                return {"error": "Service ID and metrics required"}
            
            # Convert metrics data to ServiceMetrics object
            metrics = ServiceMetrics(
                timestamp=datetime.fromisoformat(metrics_data.get("timestamp", datetime.utcnow().isoformat())),
                cpu_usage=float(metrics_data.get("cpu_usage", 0.0)),
                memory_usage=float(metrics_data.get("memory_usage", 0.0)),
                request_rate=float(metrics_data.get("request_rate", 0.0)),
                error_rate=float(metrics_data.get("error_rate", 0.0)),
                response_time_p50=float(metrics_data.get("response_time_p50", 0.0)),
                response_time_p95=float(metrics_data.get("response_time_p95", 0.0)),
                response_time_p99=float(metrics_data.get("response_time_p99", 0.0)),
                availability=float(metrics_data.get("availability", 1.0)),
            )
            
            # Update metrics in health predictor
            await self.health_predictor.update_service_metrics(service_id, metrics)
            
            return {
                "status": "success",
                "service_id": service_id,
                "timestamp": datetime.utcnow().isoformat(),
            }
            
        except Exception as e:
            logger.error(f"Metrics update failed: {e}")
            return {"error": f"Metrics update failed: {str(e)}"}

    async def _handle_comprehensive_analysis(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle comprehensive analysis combining all AI capabilities."""
        try:
            results = {}
            
            # Perform service matching if requested
            if request.get("include_matching", False):
                matching_result = await self._handle_service_matching(request)
                results["service_matching"] = matching_result
            
            # Perform optimization analysis if requested
            if request.get("include_optimization", False):
                optimization_result = await self._handle_service_optimization(request)
                results["optimization"] = optimization_result
            
            # Perform health predictions if requested
            if request.get("include_health", False):
                service_ids = request.get("service_ids", [])
                health_results = {}
                
                for service_id in service_ids:
                    health_request = {"service_id": service_id}
                    health_result = await self._handle_health_prediction(health_request)
                    health_results[service_id] = health_result
                
                results["health_predictions"] = health_results
            
            # Add summary
            results["analysis_summary"] = {
                "timestamp": datetime.utcnow().isoformat(),
                "subsystem_health": self.subsystem_health,
                "analysis_components": {
                    "service_matching": "include_matching" in request,
                    "optimization": "include_optimization" in request,
                    "health_prediction": "include_health" in request,
                },
            }
            
            return results
            
        except Exception as e:
            logger.error(f"Comprehensive analysis failed: {e}")
            return {"error": f"Comprehensive analysis failed: {str(e)}"}

    # Utility methods for external access

    async def get_capability_matcher(self) -> Optional[CapabilityMatchingAI]:
        """Get the capability matcher instance."""
        return self.capability_matcher

    async def get_service_optimizer(self) -> Optional[ServiceOptimizationAI]:
        """Get the service optimizer instance."""
        return self.service_optimizer

    async def get_health_predictor(self) -> Optional[HealthPredictionAI]:
        """Get the health predictor instance."""
        return self.health_predictor