#!/usr/bin/env python3
"""
Test script for Discovery Registry AI Engine

This script demonstrates the basic functionality of the Discovery Registry Engine
and its AI capabilities.
"""

import asyncio
import json
from datetime import datetime
from loguru import logger

from engine import DiscoveryRegistryEngine


async def test_discovery_registry_engine():
    """Test the Discovery Registry AI Engine functionality."""
    
    logger.info("Testing Discovery Registry AI Engine...")
    
    # Initialize the engine
    engine = DiscoveryRegistryEngine()
    
    # Test initialization
    logger.info("1. Testing engine initialization...")
    init_success = await engine._safe_initialize()
    if not init_success:
        logger.error("Engine initialization failed")
        return
    
    logger.info("✅ Engine initialized successfully")
    
    # Test health check
    logger.info("2. Testing health check...")
    health = await engine.health_check()
    logger.info(f"Engine health: {json.dumps(health, indent=2)}")
    
    # Test service matching
    logger.info("3. Testing service matching...")
    matching_request = {
        "operation": "match_services",
        "requirements": {
            "description": "Need a user authentication service",
            "capabilities": ["authentication", "oauth", "jwt"],
            "protocols": ["http", "https"],
            "tags": ["security", "user-management"],
            "min_availability": 0.99,
            "max_response_time": 500.0,
            "max_error_rate": 0.01,
            "namespace": "production"
        },
        "services": [
            {
                "id": "auth-service-1",
                "name": "AuthService",
                "description": "OAuth2 and JWT authentication service",
                "capabilities": ["authentication", "oauth", "jwt", "user-management"],
                "tags": ["security", "user-management", "production"],
                "endpoints": [
                    {"protocol": "https", "port": 443},
                    {"protocol": "http", "port": 80}
                ],
                "metrics": {
                    "availability": 0.995,
                    "response_time_p95": 250.0,
                    "error_rate": 0.005
                },
                "namespace": "production"
            },
            {
                "id": "legacy-auth",
                "name": "LegacyAuth", 
                "description": "Legacy authentication system",
                "capabilities": ["authentication", "basic-auth"],
                "tags": ["legacy"],
                "endpoints": [{"protocol": "http", "port": 8080}],
                "metrics": {
                    "availability": 0.98,
                    "response_time_p95": 800.0,
                    "error_rate": 0.02
                },
                "namespace": "staging"
            }
        ]
    }
    
    matching_result = await engine._safe_process(matching_request)
    logger.info(f"Service matching result: {json.dumps(matching_result, indent=2)}")
    
    # Test service optimization
    logger.info("4. Testing service optimization...")
    optimization_request = {
        "operation": "optimize_services",
        "context": {
            "routing_config": {
                "algorithm": "round_robin",
                "health_check_interval": "10s"
            },
            "performance_metrics": {
                "auth-service-1": {
                    "avg_latency": 150,
                    "p99_latency": 500,
                    "error_rate": 0.005,
                    "cpu_usage": 0.7
                }
            },
            "load_balancing_config": {
                "algorithm": "round_robin"
            },
            "service_metrics": {
                "instance-1": {"request_rate": 100},
                "instance-2": {"request_rate": 120},
                "instance-3": {"request_rate": 80}
            }
        }
    }
    
    optimization_result = await engine._safe_process(optimization_request)
    logger.info(f"Service optimization result: {json.dumps(optimization_result, indent=2)}")
    
    # Test metrics update
    logger.info("5. Testing metrics update...")
    metrics_request = {
        "operation": "update_metrics",
        "service_id": "auth-service-1",
        "metrics": {
            "timestamp": datetime.utcnow().isoformat(),
            "cpu_usage": 0.65,
            "memory_usage": 0.8,
            "request_rate": 150.0,
            "error_rate": 0.005,
            "response_time_p50": 100.0,
            "response_time_p95": 250.0,
            "response_time_p99": 500.0,
            "availability": 0.995
        }
    }
    
    metrics_result = await engine._safe_process(metrics_request)
    logger.info(f"Metrics update result: {json.dumps(metrics_result, indent=2)}")
    
    # Test health prediction
    logger.info("6. Testing health prediction...")
    # First add some more metrics to have enough data
    for i in range(10):
        await asyncio.sleep(0.1)  # Small delay
        temp_metrics_request = {
            "operation": "update_metrics",
            "service_id": "auth-service-1",
            "metrics": {
                "timestamp": datetime.utcnow().isoformat(),
                "cpu_usage": 0.6 + (i * 0.05),  # Gradually increasing
                "memory_usage": 0.7 + (i * 0.02),
                "request_rate": 140.0 + (i * 5),
                "error_rate": 0.003 + (i * 0.001),
                "response_time_p50": 95.0 + (i * 3),
                "response_time_p95": 240.0 + (i * 8),
                "response_time_p99": 480.0 + (i * 15),
                "availability": 0.995 - (i * 0.001)
            }
        }
        await engine._safe_process(temp_metrics_request)
    
    health_request = {
        "operation": "predict_health",
        "service_id": "auth-service-1"
    }
    
    health_result = await engine._safe_process(health_request)
    logger.info(f"Health prediction result: {json.dumps(health_result, indent=2)}")
    
    # Test comprehensive analysis
    logger.info("7. Testing comprehensive analysis...")
    comprehensive_request = {
        "operation": "comprehensive_analysis",
        "include_matching": True,
        "include_optimization": True,
        "include_health": True,
        "service_ids": ["auth-service-1"],
        # Include data from previous requests
        "requirements": matching_request["requirements"],
        "services": matching_request["services"],
        "context": optimization_request["context"]
    }
    
    comprehensive_result = await engine._safe_process(comprehensive_request)
    logger.info(f"Comprehensive analysis result keys: {list(comprehensive_result.get('result', {}).keys())}")
    
    # Test cleanup
    logger.info("8. Testing cleanup...")
    await engine._safe_cleanup()
    
    logger.info("✅ All tests completed successfully!")


async def main():
    """Main test function."""
    try:
        await test_discovery_registry_engine()
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        raise


if __name__ == "__main__":
    # Configure logging
    logger.remove()
    logger.add(
        lambda msg: print(msg, end=""),
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | {message}",
        level="INFO"
    )
    
    # Run tests
    asyncio.run(main())