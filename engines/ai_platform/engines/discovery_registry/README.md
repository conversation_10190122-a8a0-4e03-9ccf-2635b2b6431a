# Discovery Registry AI Engine

This directory contains the complete AI engine for service discovery and registry operations, providing intelligent capabilities for service mesh management.

## Components

### 1. Core Engine (`engine.py`)
- **DiscoveryRegistryEngine**: Main AI engine that extends BaseAIEngine
- Integrates all AI subsystems and provides unified interface
- Supports multiple operation types and comprehensive analysis

### 2. Capability Matching (`capability_matcher.py`)
- **CapabilityMatchingAI**: AI-powered service matching based on requirements
- Uses semantic similarity with sentence transformers
- Provides detailed analysis and confidence scoring
- **Data Classes**: ServiceInfo, ServiceRequirements, ServiceMatch, DetailedMatchAnalysis

### 3. Service Optimization (`service_optimizer.py`)
- **ServiceOptimizationAI**: Intelligent service mesh optimization recommendations
- Analyzes routing, load balancing, circuit breakers, timeouts, performance, and security
- Provides actionable recommendations with impact scoring
- **Data Classes**: OptimizationRecommendation

### 4. Health Prediction (`health_predictor.py`)
- **HealthPredictionAI**: ML and AI-powered health prediction for services
- Uses anomaly detection (IsolationForest) when scikit-learn is available
- Combines ML predictions with LLM reasoning
- **Data Classes**: ServiceMetrics, HealthPrediction, ServiceStatus

## Supported Operations

### Service Matching
```python
request = {
    "operation": "match_services",
    "requirements": {
        "description": "Need a user authentication service",
        "capabilities": ["authentication", "oauth", "jwt"],
        "protocols": ["http", "https"],
        "min_availability": 0.99,
        "max_response_time": 500.0
    },
    "services": [...]  # List of available services
}
```

### Service Optimization
```python
request = {
    "operation": "optimize_services",
    "context": {
        "routing_config": {...},
        "performance_metrics": {...},
        "load_balancing_config": {...}
    }
}
```

### Health Prediction
```python
# First update metrics
request = {
    "operation": "update_metrics",
    "service_id": "service-1",
    "metrics": {
        "cpu_usage": 0.65,
        "memory_usage": 0.8,
        "request_rate": 150.0,
        "error_rate": 0.005,
        # ... other metrics
    }
}

# Then predict health
request = {
    "operation": "predict_health",
    "service_id": "service-1"
}
```

### Comprehensive Analysis
```python
request = {
    "operation": "comprehensive_analysis",
    "include_matching": True,
    "include_optimization": True,
    "include_health": True,
    "service_ids": ["service-1"],
    # ... include other required data
}
```

## AI Features

- **Mock AI Clients**: All modules include mock AI clients for testing without external dependencies
- **Fallback Mechanisms**: Graceful degradation when AI services are unavailable
- **Caching**: Intelligent caching of recommendations and predictions
- **Modular Design**: Each AI capability can be used independently
- **Health Monitoring**: Built-in health checks for all subsystems

## Dependencies

### Required
- Python 3.7+
- numpy

### Optional
- scikit-learn (for ML-based anomaly detection)
- sentence-transformers (for semantic similarity)

### Development/Testing
- anthropic (for actual AI client implementation)

## Usage Example

```python
from ai_platform.engines.discovery_registry import DiscoveryRegistryEngine

# Initialize engine
engine = DiscoveryRegistryEngine()
await engine._safe_initialize()

# Process requests
result = await engine._safe_process(request)

# Check health
health = await engine.health_check()

# Cleanup
await engine._safe_cleanup()
```

## Architecture

The engine follows a layered architecture:

1. **BaseAIEngine** - Common interface for all AI engines
2. **DiscoveryRegistryEngine** - Main engine orchestrating AI subsystems
3. **AI Subsystems** - Specialized AI modules for different capabilities
4. **Mock AI Clients** - Testing implementations for development

Each subsystem can be used independently and includes comprehensive error handling and fallback mechanisms.

## Files Copied and Adapted

- `service_optimizer.py` - Adapted from DRA agent with platform-specific types
- `health_predictor.py` - Adapted from DRA agent with enhanced ML integration
- `capability_matcher.py` - Already existed in the platform
- `engine.py` - New main engine integrating all capabilities

All files have been adapted to use standard Python logging instead of loguru for better compatibility.