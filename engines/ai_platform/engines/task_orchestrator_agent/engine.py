"""Task Orchestrator Agent Engine - Main interface for workflow optimization AI capabilities."""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from ...core.base_engine import BaseAIEngine
from .workflow_optimization_engine import WorkflowOptimizationEngine

logger = logging.getLogger(__name__)


class TaskOrchestratorEngine(BaseAIEngine):
    """Main engine for task orchestration AI capabilities."""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__("task_orchestrator_agent", config)
        self.workflow_optimizer = WorkflowOptimizationEngine()
        
    async def initialize(self) -> bool:
        """Initialize the task orchestrator components."""
        try:
            logger.info("Initializing Task Orchestrator Engine...")
            
            await self.workflow_optimizer.initialize()
            
            self.is_initialized = True
            logger.info("✅ Task Orchestrator Engine initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Task Orchestrator Engine: {e}")
            self.is_initialized = False
            return False
    
    async def shutdown(self):
        """Shutdown all components."""
        try:
            await self.workflow_optimizer.shutdown()
            self.is_initialized = False
            logger.info("Task Orchestrator Engine shutdown completed")
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
    
    async def is_healthy(self) -> bool:
        """Check if all components are healthy."""
        if not self.is_initialized:
            return False
        
        try:
            return await self.workflow_optimizer.is_healthy()
        except Exception:
            return False
    
    async def get_capabilities(self) -> List[str]:
        """Get list of supported capabilities."""
        return [
            "workflow_optimization",
            "task_scheduling",
            "resource_allocation",
            "dependency_analysis",
            "performance_optimization",
            "bottleneck_detection"
        ]
    
    async def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Process a task orchestration request."""
        if not self.is_initialized:
            raise RuntimeError("Task Orchestrator Engine not initialized")
        
        request_type = request.get("type")
        
        try:
            if request_type == "optimize_workflow":
                return await self._handle_workflow_optimization(request)
            elif request_type == "schedule_tasks":
                return await self._handle_task_scheduling(request)
            elif request_type == "analyze_dependencies":
                return await self._handle_dependency_analysis(request)
            elif request_type == "detect_bottlenecks":
                return await self._handle_bottleneck_detection(request)
            else:
                raise ValueError(f"Unsupported request type: {request_type}")
                
        except Exception as e:
            logger.error(f"Request processing failed: {e}")
            return {
                "status": "error",
                "error": str(e),
                "request_type": request_type
            }
    
    async def _handle_workflow_optimization(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle workflow optimization requests."""
        workflow_id = request.get("workflow_id", "unknown")
        workflow_definition = request.get("workflow_definition", {})
        historical_data = request.get("historical_data", [])
        optimization_goals = request.get("optimization_goals", ["performance", "cost"])
        constraints = request.get("constraints", {})
        
        result = await self.workflow_optimizer.optimize_workflow(
            workflow_id=workflow_id,
            workflow_definition=workflow_definition,
            historical_data=historical_data,
            optimization_goals=optimization_goals,
            constraints=constraints
        )
        
        return {
            "status": "success",
            "type": "workflow_optimization",
            "result": result
        }
    
    async def _handle_task_scheduling(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle task scheduling requests."""
        tasks = request.get("tasks", [])
        resources = request.get("resources", {})
        constraints = request.get("constraints", {})
        priorities = request.get("priorities", {})
        
        result = await self.workflow_optimizer.schedule_tasks(
            tasks=tasks,
            resources=resources,
            constraints=constraints,
            priorities=priorities
        )
        
        return {
            "status": "success",
            "type": "task_scheduling",
            "result": result
        }
    
    async def _handle_dependency_analysis(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle dependency analysis requests."""
        workflow_definition = request.get("workflow_definition", {})
        analysis_depth = request.get("analysis_depth", "full")
        
        result = await self.workflow_optimizer.analyze_dependencies(
            workflow_definition=workflow_definition,
            analysis_depth=analysis_depth
        )
        
        return {
            "status": "success",
            "type": "dependency_analysis",
            "result": result
        }
    
    async def _handle_bottleneck_detection(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle bottleneck detection requests."""
        workflow_id = request.get("workflow_id", "unknown")
        execution_data = request.get("execution_data", [])
        threshold_config = request.get("threshold_config", {})
        
        result = await self.workflow_optimizer.detect_bottlenecks(
            workflow_id=workflow_id,
            execution_data=execution_data,
            threshold_config=threshold_config
        )
        
        return {
            "status": "success",
            "type": "bottleneck_detection",
            "result": result
        }
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get metrics from workflow optimizer."""
        try:
            optimizer_metrics = await self.workflow_optimizer.get_metrics()
            
            return {
                "engine_status": {
                    "is_initialized": self.is_initialized,
                    "is_healthy": await self.is_healthy()
                },
                "workflow_optimization": optimizer_metrics
            }
        except Exception as e:
            logger.error(f"Failed to get metrics: {e}")
            return {
                "engine_status": {
                    "is_initialized": self.is_initialized,
                    "is_healthy": False,
                    "error": str(e)
                }
            }