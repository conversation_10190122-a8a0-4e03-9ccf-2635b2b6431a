"""Data Processing Agent Engine - Main interface for data processing AI capabilities."""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from ...core.base_engine import BaseAIEngine
from .anomaly_detector import AnomalyDetector
from .transformation_optimizer import TransformationOptimizer
from .data_quality_engine import DataQualityEngine

logger = logging.getLogger(__name__)


class DataProcessingEngine(BaseAIEngine):
    """Main engine for data processing AI capabilities."""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__("data_processing_agent", config)
        self.anomaly_detector = AnomalyDetector()
        self.transformation_optimizer = TransformationOptimizer()
        self.data_quality_engine = DataQualityEngine()
        
    async def initialize(self) -> bool:
        """Initialize all data processing components."""
        try:
            logger.info("Initializing Data Processing Engine...")
            
            # Initialize all components
            await asyncio.gather(
                self.anomaly_detector.initialize(),
                self.transformation_optimizer.initialize(),
                self.data_quality_engine.initialize()
            )
            
            self.is_initialized = True
            logger.info("✅ Data Processing Engine initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Data Processing Engine: {e}")
            self.is_initialized = False
            return False
    
    async def shutdown(self):
        """Shutdown all components."""
        try:
            await asyncio.gather(
                self.anomaly_detector.shutdown(),
                self.transformation_optimizer.shutdown(),
                self.data_quality_engine.shutdown(),
                return_exceptions=True
            )
            self.is_initialized = False
            logger.info("Data Processing Engine shutdown completed")
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
    
    async def is_healthy(self) -> bool:
        """Check if all components are healthy."""
        if not self.is_initialized:
            return False
        
        try:
            health_checks = await asyncio.gather(
                self.anomaly_detector.is_healthy(),
                self.transformation_optimizer.is_healthy(),
                self.data_quality_engine.is_healthy(),
                return_exceptions=True
            )
            return all(isinstance(result, bool) and result for result in health_checks)
        except Exception:
            return False
    
    async def get_capabilities(self) -> List[str]:
        """Get list of supported capabilities."""
        return [
            "anomaly_detection",
            "transformation_optimization", 
            "data_quality_analysis",
            "pipeline_optimization",
            "data_profiling"
        ]
    
    async def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Process a data processing request."""
        if not self.is_initialized:
            raise RuntimeError("Data Processing Engine not initialized")
        
        request_type = request.get("type")
        
        try:
            if request_type == "detect_anomalies":
                return await self._handle_anomaly_detection(request)
            elif request_type == "optimize_transformation":
                return await self._handle_transformation_optimization(request)
            elif request_type == "analyze_quality":
                return await self._handle_quality_analysis(request)
            elif request_type == "profile_data":
                return await self._handle_data_profiling(request)
            else:
                raise ValueError(f"Unsupported request type: {request_type}")
                
        except Exception as e:
            logger.error(f"Request processing failed: {e}")
            return {
                "status": "error",
                "error": str(e),
                "request_type": request_type
            }
    
    async def _handle_anomaly_detection(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle anomaly detection requests."""
        dataset_id = request.get("dataset_id", "unknown")
        data = request.get("data", [])
        config = request.get("config", {})
        sensitivity = config.get("sensitivity", 0.95)
        
        result = await self.anomaly_detector.detect_anomalies(
            dataset_id=dataset_id,
            data=data,
            detection_config=config,
            sensitivity=sensitivity
        )
        
        return {
            "status": "success",
            "type": "anomaly_detection",
            "result": result
        }
    
    async def _handle_transformation_optimization(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle transformation optimization requests."""
        transformation_id = request.get("transformation_id", "unknown")
        current_pipeline = request.get("current_pipeline", [])
        sample_data = request.get("sample_data", [])
        performance_targets = request.get("performance_targets", {})
        constraints = request.get("constraints", {})
        
        result = await self.transformation_optimizer.optimize_pipeline(
            transformation_id=transformation_id,
            current_pipeline=current_pipeline,
            sample_data=sample_data,
            performance_targets=performance_targets,
            constraints=constraints
        )
        
        return {
            "status": "success",
            "type": "transformation_optimization",
            "result": result
        }
    
    async def _handle_quality_analysis(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle data quality analysis requests."""
        dataset_id = request.get("dataset_id", "unknown")
        schema_info = request.get("schema_info", {})
        sample_data = request.get("sample_data", [])
        quality_checks = request.get("quality_checks")
        metadata = request.get("metadata", {})
        
        result = await self.data_quality_engine.analyze_quality(
            dataset_id=dataset_id,
            schema_info=schema_info,
            sample_data=sample_data,
            quality_checks=quality_checks,
            metadata=metadata
        )
        
        return {
            "status": "success",
            "type": "quality_analysis",
            "result": result
        }
    
    async def _handle_data_profiling(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle comprehensive data profiling requests."""
        dataset_id = request.get("dataset_id", "unknown")
        data = request.get("data", [])
        schema_info = request.get("schema_info", {})
        
        # Run all analyses in parallel
        anomaly_task = self.anomaly_detector.detect_anomalies(dataset_id, data)
        quality_task = self.data_quality_engine.analyze_quality(dataset_id, schema_info, data)
        
        anomaly_result, quality_result = await asyncio.gather(
            anomaly_task, quality_task, return_exceptions=True
        )
        
        # Combine results
        profile = {
            "dataset_id": dataset_id,
            "data_size": len(data),
            "anomalies": anomaly_result if not isinstance(anomaly_result, Exception) else None,
            "quality": quality_result if not isinstance(quality_result, Exception) else None,
            "timestamp": asyncio.get_event_loop().time()
        }
        
        return {
            "status": "success",
            "type": "data_profiling",
            "result": profile
        }
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get combined metrics from all components."""
        try:
            anomaly_metrics = await self.anomaly_detector.get_metrics()
            optimization_metrics = await self.transformation_optimizer.get_metrics()
            quality_metrics = await self.data_quality_engine.get_metrics()
            
            return {
                "engine_status": {
                    "is_initialized": self.is_initialized,
                    "is_healthy": await self.is_healthy()
                },
                "anomaly_detection": anomaly_metrics,
                "transformation_optimization": optimization_metrics,
                "data_quality": quality_metrics
            }
        except Exception as e:
            logger.error(f"Failed to get metrics: {e}")
            return {
                "engine_status": {
                    "is_initialized": self.is_initialized,
                    "is_healthy": False,
                    "error": str(e)
                }
            }