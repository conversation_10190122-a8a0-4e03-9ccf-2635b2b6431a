"""
Data Quality Engine
AI-powered data quality analysis and scoring
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime
import json
from sklearn.ensemble import RandomForestClassifier, IsolationForest
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import uuid

logger = logging.getLogger(__name__)

class DataQualityEngine:
    """AI-powered data quality analysis engine"""
    
    def __init__(self):
        self.is_initialized = False
        self.quality_model = None
        self.scaler = StandardScaler()
        self.analysis_count = 0
        self.total_quality_score = 0.0
        
        # Quality check configurations
        self.quality_checks = {
            "completeness": self._check_completeness,
            "accuracy": self._check_accuracy,
            "consistency": self._check_consistency,
            "validity": self._check_validity,
            "uniqueness": self._check_uniqueness,
            "timeliness": self._check_timeliness
        }
    
    async def initialize(self):
        """Initialize the data quality engine"""
        try:
            logger.info("Initializing Data Quality Engine...")
            
            # Initialize ML models
            self.quality_model = RandomForestClassifier(
                n_estimators=100,
                random_state=42,
                max_depth=10
            )
            
            # Train with synthetic data (in production, use real training data)
            await self._train_quality_model()
            
            self.is_initialized = True
            logger.info("✅ Data Quality Engine initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Data Quality Engine: {e}")
            raise
    
    async def shutdown(self):
        """Shutdown the engine"""
        self.is_initialized = False
        logger.info("Data Quality Engine shutdown completed")
    
    async def is_healthy(self) -> bool:
        """Check engine health"""
        return self.is_initialized and self.quality_model is not None
    
    async def analyze_quality(self, dataset_id: str, schema_info: Dict[str, Any], 
                            sample_data: List[Dict[str, Any]], 
                            quality_checks: List[str] = None,
                            metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """Analyze data quality for a dataset"""
        
        if not self.is_initialized:
            raise RuntimeError("Data Quality Engine not initialized")
        
        logger.info(f"Analyzing data quality for dataset: {dataset_id}")
        
        try:
            # Convert to DataFrame for analysis
            df = pd.DataFrame(sample_data)
            
            # Perform quality checks
            quality_metrics = {}
            quality_issues = []
            
            # Run specified quality checks or all if none specified
            checks_to_run = quality_checks if quality_checks else list(self.quality_checks.keys())
            
            for check_name in checks_to_run:
                if check_name in self.quality_checks:
                    metric, issues = await self.quality_checks[check_name](df, schema_info)
                    quality_metrics[check_name] = metric
                    quality_issues.extend(issues)
            
            # Calculate overall quality score
            overall_score = self._calculate_overall_score(quality_metrics)
            
            # Generate recommendations
            recommendations = self._generate_recommendations(quality_metrics, quality_issues)
            
            # Update metrics
            self.analysis_count += 1
            self.total_quality_score += overall_score
            
            # Calculate confidence based on data sample size and completeness
            confidence_score = self._calculate_confidence(df, quality_metrics)
            
            result = {
                "overall_quality_score": overall_score,
                "quality_metrics": quality_metrics,
                "quality_issues": quality_issues,
                "recommendations": recommendations,
                "confidence_score": confidence_score
            }
            
            logger.info(f"Quality analysis completed for {dataset_id}: score={overall_score:.2f}")
            return result
            
        except Exception as e:
            logger.error(f"Quality analysis failed for {dataset_id}: {e}")
            raise
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get engine metrics"""
        avg_quality = self.total_quality_score / self.analysis_count if self.analysis_count > 0 else 0.0
        
        return {
            "analysis_count": self.analysis_count,
            "average_quality_score": avg_quality,
            "is_initialized": self.is_initialized,
            "model_available": self.quality_model is not None,
            "supported_checks": list(self.quality_checks.keys())
        }
    
    # Quality check implementations
    
    async def _check_completeness(self, df: pd.DataFrame, schema_info: Dict[str, Any]) -> tuple:
        """Check data completeness"""
        completeness_scores = []
        issues = []
        
        for column in df.columns:
            non_null_ratio = df[column].notna().sum() / len(df)
            completeness_scores.append(non_null_ratio)
            
            if non_null_ratio < 0.9:  # Less than 90% complete
                issues.append({
                    "type": "completeness",
                    "field": column,
                    "severity": "medium" if non_null_ratio > 0.7 else "high",
                    "description": f"Column {column} is {non_null_ratio:.1%} complete",
                    "null_percentage": (1 - non_null_ratio) * 100
                })
        
        overall_completeness = np.mean(completeness_scores) * 100
        return overall_completeness, issues
    
    async def _check_accuracy(self, df: pd.DataFrame, schema_info: Dict[str, Any]) -> tuple:
        """Check data accuracy using pattern matching and range validation"""
        accuracy_scores = []
        issues = []
        
        for column in df.columns:
            if df[column].dtype == 'object':  # String columns
                # Check for common data quality issues
                invalid_count = 0
                total_count = df[column].notna().sum()
                
                if total_count > 0:
                    # Check for obviously invalid values
                    invalid_patterns = ['null', 'none', 'n/a', '', 'invalid']
                    for pattern in invalid_patterns:
                        invalid_count += df[column].str.lower().str.contains(pattern, na=False).sum()
                    
                    accuracy = 1 - (invalid_count / total_count)
                    accuracy_scores.append(accuracy)
                    
                    if accuracy < 0.95:
                        issues.append({
                            "type": "accuracy",
                            "field": column,
                            "severity": "medium",
                            "description": f"Column {column} has {invalid_count} potentially invalid values",
                            "invalid_count": invalid_count
                        })
            else:  # Numeric columns
                # Check for outliers using IQR method
                Q1 = df[column].quantile(0.25)
                Q3 = df[column].quantile(0.75)
                IQR = Q3 - Q1
                outliers = df[(df[column] < (Q1 - 1.5 * IQR)) | (df[column] > (Q3 + 1.5 * IQR))]
                
                outlier_ratio = len(outliers) / len(df)
                accuracy = 1 - outlier_ratio
                accuracy_scores.append(accuracy)
                
                if outlier_ratio > 0.05:  # More than 5% outliers
                    issues.append({
                        "type": "accuracy",
                        "field": column,
                        "severity": "low",
                        "description": f"Column {column} has {len(outliers)} potential outliers",
                        "outlier_count": len(outliers)
                    })
        
        overall_accuracy = np.mean(accuracy_scores) * 100 if accuracy_scores else 100.0
        return overall_accuracy, issues
    
    async def _check_consistency(self, df: pd.DataFrame, schema_info: Dict[str, Any]) -> tuple:
        """Check data consistency across fields"""
        consistency_score = 95.0  # Default high score
        issues = []
        
        # Check for inconsistent data types within columns
        for column in df.columns:
            if df[column].dtype == 'object':
                # Check if numeric-looking strings are mixed with text
                numeric_pattern = df[column].str.match(r'^-?\d+\.?\d*$', na=False)
                if numeric_pattern.any() and not numeric_pattern.all():
                    numeric_count = numeric_pattern.sum()
                    total_count = df[column].notna().sum()
                    
                    if 0.1 < (numeric_count / total_count) < 0.9:  # Mixed types
                        consistency_score -= 10
                        issues.append({
                            "type": "consistency",
                            "field": column,
                            "severity": "medium",
                            "description": f"Column {column} has mixed data types",
                            "numeric_count": numeric_count,
                            "total_count": total_count
                        })
        
        return consistency_score, issues
    
    async def _check_validity(self, df: pd.DataFrame, schema_info: Dict[str, Any]) -> tuple:
        """Check data validity against schema or business rules"""
        validity_scores = []
        issues = []
        
        for column in df.columns:
            valid_ratio = 1.0  # Default to valid
            
            # Email validation for email-like columns
            if 'email' in column.lower():
                email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
                valid_emails = df[column].str.match(email_pattern, na=False)
                valid_ratio = valid_emails.sum() / df[column].notna().sum()
                
                if valid_ratio < 0.9:
                    issues.append({
                        "type": "validity",
                        "field": column,
                        "severity": "high",
                        "description": f"Column {column} has invalid email formats",
                        "invalid_count": df[column].notna().sum() - valid_emails.sum()
                    })
            
            # Date validation for date-like columns
            elif 'date' in column.lower() or 'time' in column.lower():
                try:
                    pd.to_datetime(df[column], errors='coerce')
                    valid_dates = pd.to_datetime(df[column], errors='coerce').notna()
                    valid_ratio = valid_dates.sum() / df[column].notna().sum()
                    
                    if valid_ratio < 0.9:
                        issues.append({
                            "type": "validity",
                            "field": column,
                            "severity": "high",
                            "description": f"Column {column} has invalid date formats",
                            "invalid_count": df[column].notna().sum() - valid_dates.sum()
                        })
                except:
                    valid_ratio = 0.5  # Assume partially valid if parsing fails
            
            validity_scores.append(valid_ratio)
        
        overall_validity = np.mean(validity_scores) * 100 if validity_scores else 100.0
        return overall_validity, issues
    
    async def _check_uniqueness(self, df: pd.DataFrame, schema_info: Dict[str, Any]) -> tuple:
        """Check data uniqueness"""
        uniqueness_scores = []
        issues = []
        
        for column in df.columns:
            unique_ratio = df[column].nunique() / df[column].notna().sum()
            uniqueness_scores.append(unique_ratio)
            
            # Check for expected unique fields (like IDs)
            if 'id' in column.lower() and unique_ratio < 0.95:
                issues.append({
                    "type": "uniqueness",
                    "field": column,
                    "severity": "high",
                    "description": f"ID column {column} has duplicates",
                    "duplicate_count": df[column].notna().sum() - df[column].nunique()
                })
            elif unique_ratio < 0.1:  # Very low uniqueness might be an issue
                issues.append({
                    "type": "uniqueness",
                    "field": column,
                    "severity": "low",
                    "description": f"Column {column} has very low uniqueness ({unique_ratio:.1%})",
                    "unique_ratio": unique_ratio
                })
        
        overall_uniqueness = np.mean(uniqueness_scores) * 100 if uniqueness_scores else 100.0
        return overall_uniqueness, issues
    
    async def _check_timeliness(self, df: pd.DataFrame, schema_info: Dict[str, Any]) -> tuple:
        """Check data timeliness"""
        timeliness_score = 90.0  # Default score
        issues = []
        
        # Look for timestamp columns
        timestamp_columns = [col for col in df.columns if 'time' in col.lower() or 'date' in col.lower()]
        
        for column in timestamp_columns:
            try:
                dates = pd.to_datetime(df[column], errors='coerce')
                valid_dates = dates.dropna()
                
                if not valid_dates.empty:
                    # Check for future dates (might be data entry errors)
                    future_dates = valid_dates > pd.Timestamp.now()
                    if future_dates.any():
                        issues.append({
                            "type": "timeliness",
                            "field": column,
                            "severity": "medium",
                            "description": f"Column {column} has future dates",
                            "future_count": future_dates.sum()
                        })
                        timeliness_score -= 5
                    
                    # Check for very old dates (might be outdated data)
                    very_old = valid_dates < pd.Timestamp.now() - pd.Timedelta(days=3650)  # 10 years
                    if very_old.any():
                        issues.append({
                            "type": "timeliness",
                            "field": column,
                            "severity": "low",
                            "description": f"Column {column} has very old dates",
                            "old_count": very_old.sum()
                        })
                        timeliness_score -= 2
            except:
                pass  # Skip if date parsing fails
        
        return timeliness_score, issues
    
    def _calculate_overall_score(self, quality_metrics: Dict[str, float]) -> float:
        """Calculate overall quality score with weights"""
        weights = {
            "completeness": 0.25,
            "accuracy": 0.25,
            "consistency": 0.15,
            "validity": 0.20,
            "uniqueness": 0.10,
            "timeliness": 0.05
        }
        
        weighted_score = 0.0
        total_weight = 0.0
        
        for metric, score in quality_metrics.items():
            if metric in weights:
                weighted_score += score * weights[metric]
                total_weight += weights[metric]
        
        return weighted_score / total_weight if total_weight > 0 else 0.0
    
    def _generate_recommendations(self, quality_metrics: Dict[str, float], 
                                quality_issues: List[Dict[str, Any]]) -> List[str]:
        """Generate quality improvement recommendations"""
        recommendations = []
        
        # Low completeness
        if quality_metrics.get("completeness", 100) < 80:
            recommendations.append("Improve data collection processes to reduce missing values")
        
        # Low accuracy
        if quality_metrics.get("accuracy", 100) < 90:
            recommendations.append("Implement data validation rules at the source")
        
        # Consistency issues
        if quality_metrics.get("consistency", 100) < 85:
            recommendations.append("Standardize data formats and types across sources")
        
        # Validity issues
        if quality_metrics.get("validity", 100) < 90:
            recommendations.append("Add schema validation and format checking")
        
        # Uniqueness issues
        if quality_metrics.get("uniqueness", 100) < 70:
            recommendations.append("Review data deduplication processes")
        
        # Issue-specific recommendations
        high_severity_issues = [issue for issue in quality_issues if issue.get("severity") == "high"]
        if high_severity_issues:
            recommendations.append("Address high-severity data quality issues immediately")
        
        if not recommendations:
            recommendations.append("Data quality is good - maintain current processes")
        
        return recommendations
    
    def _calculate_confidence(self, df: pd.DataFrame, quality_metrics: Dict[str, float]) -> float:
        """Calculate confidence in the quality analysis"""
        # Base confidence on sample size
        sample_size_factor = min(1.0, len(df) / 1000)  # Full confidence at 1000+ records
        
        # Adjust based on completeness
        completeness_factor = quality_metrics.get("completeness", 100) / 100
        
        # Combine factors
        confidence = (sample_size_factor * 0.6 + completeness_factor * 0.4)
        
        return max(0.3, min(1.0, confidence))  # Clamp between 0.3 and 1.0
    
    async def _train_quality_model(self):
        """Train the quality scoring model with synthetic data"""
        try:
            # Generate synthetic training data
            # In production, this would use real historical data
            X_train, y_train = self._generate_synthetic_training_data()
            
            # Train the model
            self.quality_model.fit(X_train, y_train)
            
            logger.info("Quality model trained successfully")
            
        except Exception as e:
            logger.error(f"Failed to train quality model: {e}")
            raise
    
    def _generate_synthetic_training_data(self):
        """Generate synthetic training data for the quality model"""
        n_samples = 1000
        n_features = 6  # completeness, accuracy, consistency, validity, uniqueness, timeliness
        
        # Generate feature data
        X = np.random.uniform(0, 100, (n_samples, n_features))
        
        # Generate labels based on weighted average (good/bad quality)
        weights = np.array([0.25, 0.25, 0.15, 0.20, 0.10, 0.05])
        quality_scores = np.dot(X, weights)
        y = (quality_scores > 75).astype(int)  # 1 for good quality, 0 for poor quality
        
        return X, y