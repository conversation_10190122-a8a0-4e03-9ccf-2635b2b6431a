"""
Anomaly Detector
AI-powered anomaly detection using multiple algorithms
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import DBSCAN
import uuid

logger = logging.getLogger(__name__)

class AnomalyDetector:
    """AI-powered anomaly detection engine"""
    
    def __init__(self):
        self.is_initialized = False
        self.isolation_forest = None
        self.scaler = StandardScaler()
        self.detection_count = 0
        self.total_anomalies = 0
        
    async def initialize(self):
        """Initialize the anomaly detector"""
        try:
            logger.info("Initializing Anomaly Detector...")
            
            # Initialize Isolation Forest model
            self.isolation_forest = IsolationForest(
                contamination=0.1,  # Expect 10% anomalies
                random_state=42,
                n_estimators=100
            )
            
            self.is_initialized = True
            logger.info("✅ Anomaly Detector initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Anomaly Detector: {e}")
            raise
    
    async def shutdown(self):
        """Shutdown the detector"""
        self.is_initialized = False
        logger.info("Anomaly Detector shutdown completed")
    
    async def is_healthy(self) -> bool:
        """Check detector health"""
        return self.is_initialized and self.isolation_forest is not None
    
    async def detect_anomalies(self, dataset_id: str, data: List[Dict[str, Any]], 
                              detection_config: Dict[str, Any] = None,
                              sensitivity: float = 0.95) -> Dict[str, Any]:
        """Detect anomalies in the provided data"""
        
        if not self.is_initialized:
            raise RuntimeError("Anomaly Detector not initialized")
        
        logger.info(f"Detecting anomalies for dataset: {dataset_id}")
        
        try:
            # Convert to DataFrame
            df = pd.DataFrame(data)
            
            if df.empty:
                return {
                    "anomalies_detected": [],
                    "anomaly_score": 0.0,
                    "detection_summary": {"total_records": 0, "anomalies_found": 0},
                    "model_confidence": 1.0
                }
            
            # Prepare data for anomaly detection
            numeric_df = self._prepare_numeric_data(df)
            
            if numeric_df.empty:
                # Fallback for non-numeric data
                anomalies = self._detect_text_anomalies(df)
                anomaly_score = len(anomalies) / len(df) if len(df) > 0 else 0.0
            else:
                # Use ML-based detection for numeric data
                anomalies = self._detect_ml_anomalies(numeric_df, sensitivity)
                anomaly_score = len(anomalies) / len(numeric_df) if len(numeric_df) > 0 else 0.0
            
            # Calculate model confidence
            confidence = self._calculate_confidence(df, len(anomalies))
            
            # Create detailed anomaly information
            detailed_anomalies = self._create_anomaly_details(df, anomalies)
            
            # Update metrics
            self.detection_count += 1
            self.total_anomalies += len(anomalies)
            
            result = {
                "anomalies_detected": detailed_anomalies,
                "anomaly_score": anomaly_score,
                "detection_summary": {
                    "total_records": len(df),
                    "anomalies_found": len(anomalies),
                    "anomaly_percentage": anomaly_score * 100
                },
                "model_confidence": confidence
            }
            
            logger.info(f"Anomaly detection completed for {dataset_id}: {len(anomalies)} anomalies found")
            return result
            
        except Exception as e:
            logger.error(f"Anomaly detection failed for {dataset_id}: {e}")
            raise
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get detector metrics"""
        avg_anomalies = self.total_anomalies / self.detection_count if self.detection_count > 0 else 0.0
        
        return {
            "detection_count": self.detection_count,
            "total_anomalies_detected": self.total_anomalies,
            "average_anomalies_per_detection": avg_anomalies,
            "is_initialized": self.is_initialized,
            "model_available": self.isolation_forest is not None
        }
    
    def _prepare_numeric_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Prepare numeric data for ML-based anomaly detection"""
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        numeric_df = df[numeric_columns].copy()
        
        # Fill missing values with median
        numeric_df = numeric_df.fillna(numeric_df.median())
        
        return numeric_df
    
    def _detect_ml_anomalies(self, numeric_df: pd.DataFrame, sensitivity: float) -> List[int]:
        """Detect anomalies using Isolation Forest"""
        if numeric_df.empty or len(numeric_df) < 2:
            return []
        
        try:
            # Scale the data
            scaled_data = self.scaler.fit_transform(numeric_df)
            
            # Adjust contamination based on sensitivity
            contamination = max(0.01, min(0.5, 1.0 - sensitivity))
            self.isolation_forest.set_params(contamination=contamination)
            
            # Fit and predict
            anomaly_labels = self.isolation_forest.fit_predict(scaled_data)
            
            # Get anomaly indices (where label is -1)
            anomaly_indices = np.where(anomaly_labels == -1)[0].tolist()
            
            return anomaly_indices
            
        except Exception as e:
            logger.warning(f"ML anomaly detection failed: {e}")
            return []
    
    def _detect_text_anomalies(self, df: pd.DataFrame) -> List[int]:
        """Detect anomalies in text data using simple heuristics"""
        anomaly_indices = []
        
        for column in df.columns:
            if df[column].dtype == 'object':  # Text columns
                # Check for unusual length patterns
                if df[column].notna().any():
                    lengths = df[column].str.len()
                    mean_length = lengths.mean()
                    std_length = lengths.std()
                    
                    # Flag records with very long or very short text
                    if std_length > 0:
                        z_scores = np.abs((lengths - mean_length) / std_length)
                        text_anomalies = np.where(z_scores > 3)[0].tolist()
                        anomaly_indices.extend(text_anomalies)
        
        return list(set(anomaly_indices))  # Remove duplicates
    
    def _create_anomaly_details(self, df: pd.DataFrame, anomaly_indices: List[int]) -> List[Dict[str, Any]]:
        """Create detailed information about detected anomalies"""
        detailed_anomalies = []
        
        for idx in anomaly_indices:
            if idx < len(df):
                record = df.iloc[idx].to_dict()
                
                anomaly_detail = {
                    "anomaly_id": str(uuid.uuid4()),
                    "record_index": idx,
                    "anomaly_type": self._classify_anomaly_type(df, idx),
                    "severity": self._calculate_anomaly_severity(df, idx),
                    "description": self._generate_anomaly_description(df, idx),
                    "affected_fields": self._identify_affected_fields(df, idx),
                    "anomaly_score": self._calculate_record_anomaly_score(df, idx),
                    "record_data": record
                }
                
                detailed_anomalies.append(anomaly_detail)
        
        return detailed_anomalies
    
    def _classify_anomaly_type(self, df: pd.DataFrame, idx: int) -> str:
        """Classify the type of anomaly"""
        record = df.iloc[idx]
        
        # Check for null values
        if record.isna().any():
            return "missing_data"
        
        # Check for numeric outliers
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            if col in record and pd.notna(record[col]):
                q1 = df[col].quantile(0.25)
                q3 = df[col].quantile(0.75)
                iqr = q3 - q1
                if record[col] < (q1 - 3 * iqr) or record[col] > (q3 + 3 * iqr):
                    return "statistical_outlier"
        
        # Check for text anomalies
        text_cols = df.select_dtypes(include=['object']).columns
        for col in text_cols:
            if col in record and pd.notna(record[col]):
                avg_length = df[col].str.len().mean()
                if len(str(record[col])) > avg_length * 3:
                    return "text_length_anomaly"
        
        return "pattern_deviation"
    
    def _calculate_anomaly_severity(self, df: pd.DataFrame, idx: int) -> str:
        """Calculate severity of the anomaly"""
        # Simplified severity calculation based on distance from normal
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        
        if not numeric_cols.empty:
            record = df.iloc[idx]
            severity_scores = []
            
            for col in numeric_cols:
                if col in record and pd.notna(record[col]):
                    mean_val = df[col].mean()
                    std_val = df[col].std()
                    if std_val > 0:
                        z_score = abs((record[col] - mean_val) / std_val)
                        severity_scores.append(z_score)
            
            if severity_scores:
                max_z_score = max(severity_scores)
                if max_z_score > 4:
                    return "critical"
                elif max_z_score > 3:
                    return "high"
                elif max_z_score > 2:
                    return "medium"
                else:
                    return "low"
        
        return "medium"  # Default
    
    def _generate_anomaly_description(self, df: pd.DataFrame, idx: int) -> str:
        """Generate a human-readable description of the anomaly"""
        anomaly_type = self._classify_anomaly_type(df, idx)
        
        descriptions = {
            "missing_data": f"Record at index {idx} has missing values in key fields",
            "statistical_outlier": f"Record at index {idx} contains statistical outliers",
            "text_length_anomaly": f"Record at index {idx} has unusually long text values",
            "pattern_deviation": f"Record at index {idx} deviates from normal data patterns"
        }
        
        return descriptions.get(anomaly_type, f"Anomaly detected at record index {idx}")
    
    def _identify_affected_fields(self, df: pd.DataFrame, idx: int) -> List[str]:
        """Identify which fields contribute to the anomaly"""
        affected_fields = []
        record = df.iloc[idx]
        
        # Check for missing values
        null_fields = record[record.isna()].index.tolist()
        affected_fields.extend(null_fields)
        
        # Check for numeric outliers
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            if col in record and pd.notna(record[col]):
                q1 = df[col].quantile(0.25)
                q3 = df[col].quantile(0.75)
                iqr = q3 - q1
                if record[col] < (q1 - 3 * iqr) or record[col] > (q3 + 3 * iqr):
                    affected_fields.append(col)
        
        return list(set(affected_fields))
    
    def _calculate_record_anomaly_score(self, df: pd.DataFrame, idx: int) -> float:
        """Calculate anomaly score for a specific record"""
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        
        if numeric_cols.empty:
            return 0.5  # Default score for non-numeric data
        
        try:
            record_data = df.iloc[idx:idx+1][numeric_cols]
            scaled_data = self.scaler.transform(record_data.fillna(record_data.median()))
            
            # Get anomaly score from isolation forest
            anomaly_score = self.isolation_forest.score_samples(scaled_data)[0]
            
            # Convert to 0-1 scale (lower isolation forest scores = higher anomaly)
            normalized_score = max(0.0, min(1.0, 1.0 - (anomaly_score + 0.5)))
            
            return normalized_score
            
        except Exception:
            return 0.5  # Fallback score
    
    def _calculate_confidence(self, df: pd.DataFrame, anomaly_count: int) -> float:
        """Calculate confidence in the anomaly detection"""
        # Base confidence on data size and characteristics
        size_factor = min(1.0, len(df) / 100)  # Full confidence at 100+ records
        
        # Adjust based on data completeness
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if not numeric_cols.empty:
            completeness = df[numeric_cols].notna().sum().sum() / (len(df) * len(numeric_cols))
        else:
            completeness = 0.5  # Lower confidence for non-numeric data
        
        # Adjust based on anomaly ratio (very high or very low ratios are less reliable)
        anomaly_ratio = anomaly_count / len(df) if len(df) > 0 else 0
        ratio_factor = 1.0 - abs(anomaly_ratio - 0.1)  # Optimal around 10%
        ratio_factor = max(0.3, ratio_factor)
        
        confidence = (size_factor * 0.4 + completeness * 0.4 + ratio_factor * 0.2)
        return max(0.3, min(1.0, confidence))