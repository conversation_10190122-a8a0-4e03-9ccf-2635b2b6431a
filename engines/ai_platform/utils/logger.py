"""
Logging configuration for Meta-Agent AI Platform
"""

import sys
from loguru import logger
from ai_platform.config.settings import get_settings


def setup_logging():
    """Setup logging configuration"""
    settings = get_settings()
    
    # Remove default handler
    logger.remove()
    
    # Configure format based on environment
    if settings.log_format == "json":
        log_format = (
            "{{\"time\": \"{time:YYYY-MM-DD HH:mm:ss.SSS}\", "
            "\"level\": \"{level}\", "
            "\"module\": \"{module}\", "
            "\"function\": \"{function}\", "
            "\"line\": {line}, "
            "\"message\": \"{message}\"}}"
        )
    else:
        log_format = (
            "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{module}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )
    
    # Add console handler
    logger.add(
        sys.stdout,
        format=log_format,
        level=settings.log_level,
        colorize=settings.log_format != "json"
    )
    
    # Add file handler for errors
    logger.add(
        "logs/ai_platform_errors.log",
        format=log_format,
        level="ERROR",
        rotation="10 MB",
        retention="30 days",
        compression="gz"
    )
    
    logger.info("Logging configured successfully")