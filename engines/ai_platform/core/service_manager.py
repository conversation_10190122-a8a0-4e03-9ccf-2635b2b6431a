"""
AI Service Manager
Manages lifecycle of all AI engines in the platform
"""

import asyncio
import time
from typing import Dict, Any, List
from loguru import logger

from .base_engine import Base<PERSON>IEngine, MockAIEngine
from ai_platform.engines import (
    DiscoveryRegistryEngine,
    SecurityMonitorEngine,
    DataProcessingEngine,
    KnowledgeBaseEngine,
    TaskOrchestratorEngine,
    ResourceManagerEngine,
    AgentFactoryEngine,
    SupremePlatformIntelligenceEngine
)


class AIServiceManager:
    """Manages all AI engines in the platform"""
    
    def __init__(self):
        self.engines: Dict[str, BaseAIEngine] = {}
        self.initialized = False
        self.start_time: float = 0
    
    async def initialize_all_engines(self) -> bool:
        """Initialize all AI engines"""
        try:
            self.start_time = time.time()
            logger.info("🔄 Initializing all AI engines...")
            
            # Initialize actual AI engines
            engine_instances = {
                "discovery_registry": DiscoveryRegistryEngine(),
                "security_monitor": SecurityMonitorEngine(),
                "data_processing": DataProcessingEngine(),
                "knowledge_base": KnowledgeBaseEngine(),
                "task_orchestrator": TaskOrchestratorEngine(),
                "resource_manager": ResourceManagerEngine(),
                "agent_factory": AgentFactoryEngine(),
                "supreme_intelligence": SupremePlatformIntelligenceEngine()
            }
            
            # Initialize all engines concurrently
            initialization_tasks = []
            for engine_name, engine in engine_instances.items():
                self.engines[engine_name] = engine
                initialization_tasks.append(engine._safe_initialize())
            
            # Initialize all engines concurrently
            results = await asyncio.gather(*initialization_tasks, return_exceptions=True)
            
            # Check results
            success_count = sum(1 for result in results if result is True)
            total_count = len(engine_instances)
            
            self.initialized = success_count == total_count
            
            if self.initialized:
                logger.info(f"✅ All {total_count} AI engines initialized successfully")
            else:
                logger.warning(f"⚠️  Only {success_count}/{total_count} AI engines initialized")
            
            return self.initialized
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize AI engines: {e}")
            return False
    
    async def shutdown_all_engines(self) -> None:
        """Shutdown all AI engines"""
        try:
            logger.info("🔄 Shutting down all AI engines...")
            
            cleanup_tasks = []
            for engine in self.engines.values():
                cleanup_tasks.append(engine._safe_cleanup())
            
            await asyncio.gather(*cleanup_tasks, return_exceptions=True)
            
            self.engines.clear()
            self.initialized = False
            
            logger.info("✅ All AI engines shut down successfully")
            
        except Exception as e:
            logger.error(f"❌ Error during engine shutdown: {e}")
    
    async def get_engine(self, engine_name: str) -> BaseAIEngine:
        """Get a specific AI engine"""
        if engine_name not in self.engines:
            raise ValueError(f"Engine '{engine_name}' not found")
        
        engine = self.engines[engine_name]
        if not engine.initialized:
            raise RuntimeError(f"Engine '{engine_name}' not initialized")
        
        return engine
    
    async def process_request(self, engine_name: str, request: Dict[str, Any]) -> Dict[str, Any]:
        """Process a request with the specified engine"""
        engine = await self.get_engine(engine_name)
        return await engine._safe_process(request)
    
    async def get_all_engine_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all engines"""
        status = {}
        
        for engine_name, engine in self.engines.items():
            status[engine_name] = await engine.health_check()
        
        return status
    
    async def get_platform_metrics(self) -> Dict[str, Any]:
        """Get overall platform metrics"""
        engine_status = await self.get_all_engine_status()
        
        total_requests = sum(status["request_count"] for status in engine_status.values())
        total_errors = sum(status["error_count"] for status in engine_status.values())
        healthy_engines = sum(1 for status in engine_status.values() if status["healthy"])
        
        uptime = time.time() - self.start_time if self.start_time else 0
        
        return {
            "platform_status": "healthy" if self.initialized else "degraded",
            "uptime_seconds": uptime,
            "total_engines": len(self.engines),
            "healthy_engines": healthy_engines,
            "total_requests": total_requests,
            "total_errors": total_errors,
            "overall_error_rate": total_errors / max(total_requests, 1),
            "engines": engine_status
        }
    
    def get_timestamp(self) -> float:
        """Get current timestamp"""
        return time.time()
    
    def is_healthy(self) -> bool:
        """Check if the service manager is healthy"""
        return self.initialized and len(self.engines) > 0