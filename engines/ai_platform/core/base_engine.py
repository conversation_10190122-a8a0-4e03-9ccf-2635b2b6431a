"""
Base AI Engine Interface
Standard interface for all AI engines in the platform
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import time
import logging
logger = logging.getLogger(__name__)


class BaseAIEngine(ABC):
    """Base class for all AI engines in the platform"""
    
    def __init__(self, engine_name: str):
        self.engine_name = engine_name
        self.initialized = False
        self.start_time: Optional[float] = None
        self.request_count = 0
        self.error_count = 0
    
    @abstractmethod
    async def initialize(self) -> bool:
        """
        Initialize the AI engine
        
        Returns:
            bool: True if initialization successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def process(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process an AI request
        
        Args:
            request: Input data for processing
            
        Returns:
            Dict containing the processing results
        """
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """Clean up resources when shutting down"""
        pass
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check for this engine
        
        Returns:
            Dict containing health status information
        """
        uptime = time.time() - self.start_time if self.start_time else 0
        
        return {
            "engine_name": self.engine_name,
            "healthy": self.initialized,
            "uptime_seconds": uptime,
            "request_count": self.request_count,
            "error_count": self.error_count,
            "error_rate": self.error_count / max(self.request_count, 1),
            "status": "healthy" if self.initialized else "not_initialized"
        }
    
    async def _safe_initialize(self) -> bool:
        """Safe initialization with error handling"""
        try:
            logger.info(f"Initializing {self.engine_name} engine...")
            result = await self.initialize()
            if result:
                self.initialized = True
                self.start_time = time.time()
                logger.info(f"✅ {self.engine_name} engine initialized successfully")
            else:
                logger.error(f"❌ {self.engine_name} engine initialization failed")
            return result
        except Exception as e:
            logger.error(f"❌ {self.engine_name} engine initialization error: {e}")
            return False
    
    async def _safe_process(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Safe processing with error handling and metrics"""
        try:
            if not self.initialized:
                raise RuntimeError(f"{self.engine_name} engine not initialized")
            
            self.request_count += 1
            start_time = time.time()
            
            result = await self.process(request)
            
            processing_time = time.time() - start_time
            logger.debug(f"{self.engine_name} processed request in {processing_time:.3f}s")
            
            return {
                "success": True,
                "result": result,
                "processing_time": processing_time,
                "engine": self.engine_name
            }
            
        except Exception as e:
            self.error_count += 1
            logger.error(f"❌ {self.engine_name} processing error: {e}")
            return {
                "success": False,
                "error": str(e),
                "engine": self.engine_name
            }
    
    async def _safe_cleanup(self) -> None:
        """Safe cleanup with error handling"""
        try:
            logger.info(f"Cleaning up {self.engine_name} engine...")
            await self.cleanup()
            self.initialized = False
            logger.info(f"✅ {self.engine_name} engine cleanup complete")
        except Exception as e:
            logger.error(f"❌ {self.engine_name} cleanup error: {e}")


class MockAIEngine(BaseAIEngine):
    """Mock AI engine for testing and fallback"""
    
    def __init__(self, engine_name: str):
        super().__init__(engine_name)
    
    async def initialize(self) -> bool:
        """Mock initialization"""
        return True
    
    async def process(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Mock processing"""
        return {
            "message": f"Mock response from {self.engine_name}",
            "request_id": request.get("request_id", "unknown"),
            "mock": True
        }
    
    async def cleanup(self) -> None:
        """Mock cleanup"""
        pass