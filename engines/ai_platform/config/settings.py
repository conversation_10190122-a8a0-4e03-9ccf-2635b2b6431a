"""
Configuration settings for Meta-Agent AI Platform
"""

import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """Application settings"""
    
    # Server configuration
    host: str = Field("0.0.0.0", env="AI_HOST")
    port: int = Field(8081, env="AI_PORT")
    environment: str = Field("development", env="ENVIRONMENT")
    
    # Logging
    log_level: str = Field("INFO", env="LOG_LEVEL")
    log_format: str = Field("text", env="LOG_FORMAT")  # json or text
    
    # Java Platform integration
    java_platform_url: str = Field("http://localhost:8080", env="JAVA_PLATFORM_URL")
    java_platform_timeout: int = Field(30, env="JAVA_PLATFORM_TIMEOUT")
    
    # AI Engine configuration
    ai_model_cache_size: int = Field(100, env="AI_MODEL_CACHE_SIZE")
    ai_request_timeout: int = Field(60, env="AI_REQUEST_TIMEOUT")
    
    # External AI services (optional)
    openai_api_key: Optional[str] = Field(None, env="OPENAI_API_KEY")
    anthropic_api_key: Optional[str] = Field(None, env="ANTHROPIC_API_KEY")
    
    # Database (if needed for AI models)
    redis_url: Optional[str] = Field(None, env="REDIS_URL")
    postgres_url: Optional[str] = Field(None, env="POSTGRES_URL")
    
    # Performance tuning
    max_workers: int = Field(4, env="MAX_WORKERS")
    batch_size: int = Field(32, env="BATCH_SIZE")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# Global settings instance
_settings: Optional[Settings] = None


def get_settings() -> Settings:
    """Get application settings (singleton)"""
    global _settings
    if _settings is None:
        _settings = Settings()
    return _settings