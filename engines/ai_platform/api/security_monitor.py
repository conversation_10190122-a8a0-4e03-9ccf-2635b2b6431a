"""
Security Monitor API Router
Provides REST endpoints for AI-powered threat detection and security analysis.
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, Any, List, Optional
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

router = APIRouter()

# Pydantic models for request/response
class SecurityEventRequest(BaseModel):
    event: Dict[str, Any]

class SecurityEventAnalysisRequest(BaseModel):
    events: List[Dict[str, Any]]

class RiskAssessmentRequest(BaseModel):
    type: str  # event, system, network
    event: Optional[Dict[str, Any]] = None
    system: Optional[Dict[str, Any]] = None
    network: Optional[Dict[str, Any]] = None

# Global engine instance
security_engine = None

def set_engine(engine):
    """Set the security monitor engine instance"""
    global security_engine
    security_engine = engine

@router.get("/health")
async def health_check():
    """Health check endpoint for security monitor AI"""
    try:
        if not security_engine:
            raise HTTPException(status_code=503, detail="Security monitor engine not available")
        
        health_status = await security_engine.health_check()
        return {
            "status": "healthy" if health_status["healthy"] else "unhealthy",
            "engine": health_status["engine_name"],
            "uptime_seconds": health_status["uptime_seconds"],
            "request_count": health_status["request_count"],
            "error_count": health_status["error_count"]
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/detect-threat")
async def detect_threat(request: SecurityEventRequest):
    """Detect threats in security events using hybrid AI analysis"""
    try:
        if not security_engine:
            raise HTTPException(status_code=503, detail="Security monitor engine not available")
        
        # Ensure timestamp is present
        if "timestamp" not in request.event:
            request.event["timestamp"] = datetime.now().isoformat()
        
        # Process threat detection request
        ai_request = {
            "operation": "threat_detection",
            "event": request.event
        }
        
        result = await security_engine._safe_process(ai_request)
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result.get("error", "Threat detection failed"))
        
        return {
            "threat_detected": result["result"]["threat_detected"],
            "threat_type": result["result"]["threat_type"],
            "confidence_score": result["result"]["confidence_score"],
            "risk_level": result["result"]["risk_level"],
            "analysis_method": result["result"]["analysis_method"],
            "details": result["result"]["details"],
            "timestamp": result["result"]["timestamp"],
            "processing_time_ms": result["result"]["processing_time_ms"]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Threat detection failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/analyze-events")
async def analyze_events(request: SecurityEventAnalysisRequest):
    """Perform comprehensive analysis of multiple security events"""
    try:
        if not security_engine:
            raise HTTPException(status_code=503, detail="Security monitor engine not available")
        
        # Add timestamps to events if missing
        for event in request.events:
            if "timestamp" not in event:
                event["timestamp"] = datetime.now().isoformat()
        
        # Process event analysis request
        ai_request = {
            "operation": "event_analysis",
            "events": request.events
        }
        
        result = await security_engine._safe_process(ai_request)
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result.get("error", "Event analysis failed"))
        
        return {
            "analysis_summary": result["result"]["analysis_summary"],
            "event_analyses": result["result"]["event_analyses"],
            "processing_time_ms": result["processing_time"]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Event analysis failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/assess-risk")
async def assess_risk(request: RiskAssessmentRequest):
    """Assess risk levels for events, systems, or networks"""
    try:
        if not security_engine:
            raise HTTPException(status_code=503, detail="Security monitor engine not available")
        
        # Process risk assessment request
        ai_request = {
            "operation": "risk_assessment",
            "type": request.type
        }
        
        if request.event:
            if "timestamp" not in request.event:
                request.event["timestamp"] = datetime.now().isoformat()
            ai_request["event"] = request.event
        
        if request.system:
            ai_request["system"] = request.system
            
        if request.network:
            ai_request["network"] = request.network
        
        result = await security_engine._safe_process(ai_request)
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result.get("error", "Risk assessment failed"))
        
        return result["result"]
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Risk assessment failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/threat-intelligence")
async def get_threat_intelligence():
    """Get current threat intelligence summary"""
    try:
        if not security_engine:
            raise HTTPException(status_code=503, detail="Security monitor engine not available")
        
        # Mock threat intelligence data
        return {
            "threat_landscape": {
                "current_threat_level": "MODERATE",
                "active_campaigns": ["phishing", "ransomware", "apt_groups"],
                "trending_vulnerabilities": ["CVE-2024-001", "CVE-2024-002"],
                "recommended_actions": [
                    "Update security signatures",
                    "Monitor phishing indicators",
                    "Review access controls"
                ]
            },
            "ai_model_status": {
                "isolation_forest": "trained",
                "nlp_analyzer": "active",
                "ai_reasoner": "active"
            },
            "last_updated": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Threat intelligence failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/metrics")
async def get_security_metrics():
    """Get security monitoring metrics"""
    try:
        if not security_engine:
            raise HTTPException(status_code=503, detail="Security monitor engine not available")
        
        health_status = await security_engine.health_check()
        
        return {
            "engine_metrics": {
                "total_requests": health_status["request_count"],
                "total_errors": health_status["error_count"],
                "error_rate": health_status["error_rate"],
                "uptime_seconds": health_status["uptime_seconds"]
            },
            "threat_metrics": {
                "threats_detected_today": 0,  # Mock data
                "high_risk_events": 0,
                "false_positive_rate": 0.05,
                "detection_accuracy": 0.95
            },
            "model_performance": {
                "isolation_forest_accuracy": 0.92,
                "nlp_analyzer_precision": 0.88,
                "ensemble_confidence": 0.90
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Security metrics failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/capabilities")
async def get_capabilities():
    """Get AI capabilities of the security monitor engine"""
    return {
        "engine_name": "SecurityMonitor",
        "capabilities": [
            "threat_detection",
            "event_analysis",
            "risk_assessment",
            "behavioral_analysis",
            "anomaly_detection"
        ],
        "ai_technologies": [
            "isolation_forest",
            "lstm_neural_networks",
            "nlp_analysis",
            "ensemble_methods",
            "hybrid_ai_reasoning"
        ],
        "supported_operations": [
            "detect-threat",
            "analyze-events",
            "assess-risk",
            "threat-intelligence",
            "metrics"
        ],
        "threat_types": [
            "anomalous_behavior",
            "suspicious_content", 
            "ai_detected_threat",
            "multiple_threats"
        ],
        "risk_levels": ["LOW", "MEDIUM", "HIGH", "CRITICAL"]
    }