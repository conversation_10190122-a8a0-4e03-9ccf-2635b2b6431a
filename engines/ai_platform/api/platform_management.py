"""
Platform Management API Router
Provides endpoints for managing the overall AI platform and engine coordination.
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, Any, List, Optional
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

router = APIRouter()

# Global service manager instance
service_manager = None

def set_service_manager(manager):
    """Set the AI service manager instance"""
    global service_manager
    service_manager = manager

@router.get("/health")
async def platform_health():
    """Overall platform health check"""
    try:
        if not service_manager:
            raise HTTPException(status_code=503, detail="Service manager not available")
        
        if not service_manager.is_healthy():
            raise HTTPException(status_code=503, detail="Platform not healthy")
        
        metrics = await service_manager.get_platform_metrics()
        
        return {
            "status": "healthy",
            "platform_metrics": metrics,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Platform health check failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/engines")
async def list_engines():
    """List all available AI engines and their status"""
    try:
        if not service_manager:
            raise HTTPException(status_code=503, detail="Service manager not available")
        
        engine_status = await service_manager.get_all_engine_status()
        
        return {
            "engines": engine_status,
            "total_engines": len(engine_status),
            "healthy_engines": sum(1 for status in engine_status.values() if status["healthy"]),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Engine listing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/engines/{engine_name}")
async def get_engine_status(engine_name: str):
    """Get detailed status of a specific engine"""
    try:
        if not service_manager:
            raise HTTPException(status_code=503, detail="Service manager not available")
        
        engine = await service_manager.get_engine(engine_name)
        health_status = await engine.health_check()
        
        return {
            "engine_name": engine_name,
            "status": health_status,
            "timestamp": datetime.now().isoformat()
        }
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Engine status check failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/engines/{engine_name}/process")
async def process_request(engine_name: str, request: Dict[str, Any]):
    """Process a request using a specific AI engine"""
    try:
        if not service_manager:
            raise HTTPException(status_code=503, detail="Service manager not available")
        
        result = await service_manager.process_request(engine_name, request)
        
        return {
            "engine_name": engine_name,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except RuntimeError as e:
        raise HTTPException(status_code=503, detail=str(e))
    except Exception as e:
        logger.error(f"Request processing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/metrics")
async def get_platform_metrics():
    """Get comprehensive platform metrics"""
    try:
        if not service_manager:
            raise HTTPException(status_code=503, detail="Service manager not available")
        
        metrics = await service_manager.get_platform_metrics()
        
        return {
            "platform_metrics": metrics,
            "collection_timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Metrics collection failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/capabilities")
async def get_platform_capabilities():
    """Get all AI capabilities available on the platform"""
    try:
        if not service_manager:
            raise HTTPException(status_code=503, detail="Service manager not available")
        
        # Mock comprehensive capabilities
        return {
            "platform_name": "Meta-Agent AI Platform",
            "version": "1.0.0",
            "engines": {
                "discovery_registry": {
                    "capabilities": ["service_matching", "service_optimization", "health_prediction"],
                    "ai_technologies": ["semantic_similarity", "machine_learning", "ensemble_methods"]
                },
                "security_monitor": {
                    "capabilities": ["threat_detection", "event_analysis", "risk_assessment"],
                    "ai_technologies": ["isolation_forest", "lstm_networks", "nlp_analysis", "hybrid_reasoning"]
                },
                "data_processing": {
                    "capabilities": ["anomaly_detection", "transformation_optimization", "quality_analysis"],
                    "ai_technologies": ["machine_learning", "statistical_analysis", "pattern_recognition"]
                },
                "knowledge_base": {
                    "capabilities": ["knowledge_extraction", "pattern_recognition", "semantic_processing", "recommendations"],
                    "ai_technologies": ["transformers", "embeddings", "nlp", "knowledge_graphs"]
                },
                "task_orchestrator": {
                    "capabilities": ["workflow_optimization", "task_scheduling", "dependency_analysis"],
                    "ai_technologies": ["graph_neural_networks", "reinforcement_learning", "optimization_algorithms"]
                },
                "resource_manager": {
                    "capabilities": ["capacity_prediction", "resource_optimization", "load_forecasting"],
                    "ai_technologies": ["time_series_analysis", "predictive_models", "optimization"]
                },
                "agent_factory": {
                    "capabilities": ["agent_creation", "template_management", "capability_assessment"],
                    "ai_technologies": ["rule_based_systems", "template_matching"]
                },
                "supreme_intelligence": {
                    "capabilities": ["strategic_planning", "crisis_management", "coordination", "consciousness_monitoring"],
                    "ai_technologies": ["advanced_reasoning", "multi_agent_coordination", "emergent_intelligence"]
                }
            },
            "total_engines": 8,
            "supported_protocols": ["HTTP", "REST", "JSON"],
            "ai_frameworks": ["scikit-learn", "transformers", "torch", "sentence-transformers"],
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Capabilities listing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/")
async def platform_info():
    """Get basic platform information"""
    return {
        "platform": "Meta-Agent AI Platform",
        "version": "1.0.0",
        "description": "Unified AI platform for multi-agent systems",
        "total_engines": 8,
        "status": "operational",
        "api_version": "v1",
        "documentation": "/docs",
        "health_check": "/ai/platform/health",
        "engines_list": "/ai/platform/engines",
        "capabilities": "/ai/platform/capabilities",
        "timestamp": datetime.now().isoformat()
    }