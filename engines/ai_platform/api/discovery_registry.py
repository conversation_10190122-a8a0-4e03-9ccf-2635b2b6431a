"""
Discovery Registry API Router
Provides REST endpoints for service discovery and registry AI capabilities.
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

# Pydantic models for request/response
class ServiceMatchRequest(BaseModel):
    requirements: Dict[str, Any]
    available_services: List[Dict[str, Any]]

class ServiceOptimizationRequest(BaseModel):
    service_name: str
    current_config: Dict[str, Any]
    metrics: Optional[Dict[str, Any]] = None

class HealthPredictionRequest(BaseModel):
    service_name: str
    metrics: Dict[str, Any]
    time_horizon: str = "1h"

class MetricsUpdateRequest(BaseModel):
    service_name: str
    metrics: Dict[str, Any]

# Global engine instance (will be injected by main app)
discovery_engine = None

def set_engine(engine):
    """Set the discovery registry engine instance"""
    global discovery_engine
    discovery_engine = engine

@router.get("/health")
async def health_check():
    """Health check endpoint for discovery registry AI"""
    try:
        if not discovery_engine:
            raise HTTPException(status_code=503, detail="Discovery registry engine not available")
        
        health_status = await discovery_engine.health_check()
        return {
            "status": "healthy" if health_status["healthy"] else "unhealthy",
            "engine": health_status["engine_name"],
            "uptime_seconds": health_status["uptime_seconds"],
            "request_count": health_status["request_count"],
            "error_count": health_status["error_count"]
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/match-services")
async def match_services(request: ServiceMatchRequest):
    """Match services to requirements using AI-powered analysis"""
    try:
        if not discovery_engine:
            raise HTTPException(status_code=503, detail="Discovery registry engine not available")
        
        # Process service matching request
        ai_request = {
            "operation": "service_matching",
            "requirements": request.requirements,
            "available_services": request.available_services
        }
        
        result = await discovery_engine._safe_process(ai_request)
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result.get("error", "Service matching failed"))
        
        return {
            "matches": result["result"]["matches"],
            "processing_time_ms": result["processing_time"],
            "engine": result["engine"]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Service matching failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/optimize-service")
async def optimize_service(request: ServiceOptimizationRequest):
    """Get AI-powered optimization recommendations for a service"""
    try:
        if not discovery_engine:
            raise HTTPException(status_code=503, detail="Discovery registry engine not available")
        
        # Process service optimization request
        ai_request = {
            "operation": "service_optimization",
            "service_name": request.service_name,
            "current_config": request.current_config,
            "metrics": request.metrics
        }
        
        result = await discovery_engine._safe_process(ai_request)
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result.get("error", "Service optimization failed"))
        
        return {
            "recommendations": result["result"]["recommendations"],
            "optimization_score": result["result"]["optimization_score"],
            "processing_time_ms": result["processing_time"],
            "engine": result["engine"]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Service optimization failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/predict-health")
async def predict_health(request: HealthPredictionRequest):
    """Predict service health using AI models"""
    try:
        if not discovery_engine:
            raise HTTPException(status_code=503, detail="Discovery registry engine not available")
        
        # Process health prediction request
        ai_request = {
            "operation": "health_prediction",
            "service_name": request.service_name,
            "metrics": request.metrics,
            "time_horizon": request.time_horizon
        }
        
        result = await discovery_engine._safe_process(ai_request)
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result.get("error", "Health prediction failed"))
        
        return {
            "prediction": result["result"]["prediction"],
            "confidence": result["result"]["confidence"],
            "risk_factors": result["result"]["risk_factors"],
            "processing_time_ms": result["processing_time"],
            "engine": result["engine"]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Health prediction failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/update-metrics")
async def update_metrics(request: MetricsUpdateRequest):
    """Update metrics for health prediction models"""
    try:
        if not discovery_engine:
            raise HTTPException(status_code=503, detail="Discovery registry engine not available")
        
        # Process metrics update request
        ai_request = {
            "operation": "metrics_update",
            "service_name": request.service_name,
            "metrics": request.metrics
        }
        
        result = await discovery_engine._safe_process(ai_request)
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result.get("error", "Metrics update failed"))
        
        return {
            "updated": True,
            "service_name": request.service_name,
            "processing_time_ms": result["processing_time"]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Metrics update failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/comprehensive-analysis")
async def comprehensive_analysis(request: ServiceMatchRequest):
    """Perform comprehensive analysis combining all AI capabilities"""
    try:
        if not discovery_engine:
            raise HTTPException(status_code=503, detail="Discovery registry engine not available")
        
        # Process comprehensive analysis request
        ai_request = {
            "operation": "comprehensive_analysis",
            "requirements": request.requirements,
            "available_services": request.available_services
        }
        
        result = await discovery_engine._safe_process(ai_request)
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result.get("error", "Comprehensive analysis failed"))
        
        return {
            "analysis": result["result"],
            "processing_time_ms": result["processing_time"],
            "engine": result["engine"]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Comprehensive analysis failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/capabilities")
async def get_capabilities():
    """Get AI capabilities of the discovery registry engine"""
    return {
        "engine_name": "DiscoveryRegistry",
        "capabilities": [
            "service_matching",
            "service_optimization", 
            "health_prediction",
            "metrics_update",
            "comprehensive_analysis"
        ],
        "ai_technologies": [
            "semantic_similarity",
            "machine_learning",
            "ensemble_methods",
            "performance_optimization"
        ],
        "supported_operations": [
            "match-services",
            "optimize-service",
            "predict-health", 
            "update-metrics",
            "comprehensive-analysis"
        ]
    }