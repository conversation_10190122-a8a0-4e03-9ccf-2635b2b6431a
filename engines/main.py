"""
Meta-Agent Platform - Unified AI Service
FastAPI application providing AI capabilities for all agents
"""

import asyncio
import os
import sys
from contextlib import asynccontextmanager
from typing import Dict, Any

import uvicorn
from fastapi import FastAPI, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from loguru import logger

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ai_platform.core.service_manager import AIServiceManager
from ai_platform.api import discovery_registry, security_monitor, platform_management
from ai_platform.config.settings import get_settings
from ai_platform.utils.logger import setup_logging

# Global service manager
service_manager: AIServiceManager = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management"""
    global service_manager
    
    logger.info("🚀 Starting Meta-Agent AI Platform...")
    
    # Initialize service manager and all AI engines
    service_manager = AIServiceManager()
    await service_manager.initialize_all_engines()
    
    # Set engines in API routers
    discovery_registry.set_engine(await service_manager.get_engine("discovery_registry"))
    security_monitor.set_engine(await service_manager.get_engine("security_monitor"))
    platform_management.set_service_manager(service_manager)
    
    logger.info("✅ All AI engines initialized successfully")
    
    yield
    
    logger.info("🛑 Shutting down Meta-Agent AI Platform...")
    if service_manager:
        await service_manager.shutdown_all_engines()
    logger.info("✅ AI Platform shutdown complete")


def create_app() -> FastAPI:
    """Create and configure FastAPI application"""
    settings = get_settings()
    
    app = FastAPI(
        title="Meta-Agent AI Platform",
        description="Unified AI Service for Multi-Agent Platform",
        version="1.0.0",
        lifespan=lifespan
    )
    
    # Configure CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["http://localhost:8080", "http://127.0.0.1:8080"],  # Java platform
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Health check endpoint
    @app.get("/health", tags=["Health"])
    async def health_check():
        """Health check endpoint"""
        global service_manager
        if not service_manager:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Service manager not initialized"
            )
        
        engine_status = await service_manager.get_all_engine_status()
        healthy = all(status["healthy"] for status in engine_status.values())
        
        return {
            "status": "healthy" if healthy else "degraded",
            "service": "Meta-Agent AI Platform",
            "version": "1.0.0",
            "engines": engine_status,
            "timestamp": service_manager.get_timestamp()
        }
    
    # Include all AI engine routers
    app.include_router(discovery_registry.router, prefix="/ai/discovery", tags=["Discovery Registry"])
    app.include_router(security_monitor.router, prefix="/ai/security", tags=["Security Monitor"])
    app.include_router(platform_management.router, prefix="/ai/platform", tags=["Platform Management"])
    
    @app.exception_handler(Exception)
    async def global_exception_handler(request, exc):
        """Global exception handler"""
        logger.error(f"Unhandled exception: {exc}")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"detail": "Internal server error", "error": str(exc)}
        )
    
    return app


def main():
    """Main entry point"""
    # Setup logging
    setup_logging()
    
    settings = get_settings()
    
    logger.info("========================================")
    logger.info("🤖 Meta-Agent AI Platform Starting")
    logger.info("========================================")
    logger.info(f"Environment: {settings.environment}")
    logger.info(f"Port: {settings.port}")
    logger.info(f"Java Platform URL: {settings.java_platform_url}")
    logger.info("========================================")
    
    app = create_app()
    
    # Run the application
    uvicorn.run(
        app,
        host=settings.host,
        port=settings.port,
        log_level=settings.log_level.lower(),
        reload=settings.environment == "development"
    )


# Create app at module level for uvicorn
app = create_app()

if __name__ == "__main__":
    main()