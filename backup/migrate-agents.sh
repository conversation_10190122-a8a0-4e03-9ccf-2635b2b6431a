#!/bin/bash

echo "Starting agent migration to unified structure..."

# Function to migrate Java agent
migrate_java_agent() {
    local agent_num=$1
    local agent_name=$2
    local target_dir=$3
    
    echo "Migrating $agent_name (${agent_num})..."
    
    if [ -d "${agent_num}/src" ]; then
        # Create target directory
        mkdir -p "src/agents/${target_dir}"
        
        # Copy Java source files
        if [ -d "${agent_num}/src/main/java" ]; then
            cp -r "${agent_num}/src/main/java/"* "src/agents/${target_dir}/" 2>/dev/null || true
        fi
        
        # Copy resources
        if [ -d "${agent_num}/src/main/resources" ]; then
            mkdir -p "src/agents/${target_dir}/resources"
            cp -r "${agent_num}/src/main/resources/"* "src/agents/${target_dir}/resources/" 2>/dev/null || true
        fi
        
        echo "  ✓ Migrated $agent_name"
    else
        echo "  ⚠ Source directory not found for $agent_name"
    fi
}

# Migrate Python agent (Discovery Registry)
echo "Migrating Discovery Registry Agent (Python)..."
if [ -d "02-discovery-registry-agent/src" ]; then
    cp -r "02-discovery-registry-agent/src/"* "src/agents/discovery-registry/python/" 2>/dev/null || true
    echo "  ✓ Migrated Discovery Registry Agent (Python)"
else
    echo "  ⚠ Source directory not found for Discovery Registry Agent"
fi

# Migrate Java agents
migrate_java_agent "03-security-monitor-agent" "Security Monitor Agent" "security-monitor"
migrate_java_agent "04-resource-manager-agent" "Resource Manager Agent" "resource-manager"
migrate_java_agent "05-data-processing-agent" "Data Processing Agent" "data-processing"
migrate_java_agent "06-knowledge-base-agent" "Knowledge Base Agent" "knowledge-base"
migrate_java_agent "07-task-orchestrator-agent" "Task Orchestrator Agent" "task-orchestrator"
migrate_java_agent "08-agent-factory-agent" "Agent Factory Agent" "agent-factory"
migrate_java_agent "08-supreme-platform-intelligence-agent" "Supreme Intelligence Agent" "supreme-intelligence"

echo ""
echo "Migration completed!"
echo ""
echo "Next steps:"
echo "1. Review the migrated files in src/agents/"
echo "2. Update package declarations to match new structure"
echo "3. Extract common components to src/core/"
echo "4. Implement facade interfaces in src/facade/"
echo "5. Update build configurations"

# Create a summary of what needs to be refactored
echo ""
echo "Creating refactoring checklist..."
cat > refactoring-checklist.md << 'EOF'
# Refactoring Checklist

## Phase 1: Core Extraction
- [ ] Extract BaseAgent from all agent implementations
- [ ] Move HealthCheckController to core.controller
- [ ] Extract common AI service patterns to core.ai
- [ ] Move A2A messaging models to core.integration.a2a.models
- [ ] Create common configuration classes in core.config

## Phase 2: Package Updates
- [ ] Update all package declarations in migrated files
- [ ] Fix import statements to use new core packages
- [ ] Update Spring component scanning configuration

## Phase 3: Facade Implementation
- [ ] Implement AgentRegistryFacade for service discovery
- [ ] Implement CommunicationFacade for inter-agent communication
- [ ] Implement OrchestrationFacade for task coordination
- [ ] Implement MonitoringFacade for health and metrics

## Phase 4: Build Configuration
- [ ] Create parent pom.xml with common dependencies
- [ ] Update individual agent pom.xml files
- [ ] Configure multi-module Maven build
- [ ] Update CI/CD pipelines

## Phase 5: Testing
- [ ] Create unit tests for core components
- [ ] Test facade implementations
- [ ] Verify inter-agent communication
- [ ] End-to-end integration tests
EOF

echo "  ✓ Created refactoring-checklist.md"