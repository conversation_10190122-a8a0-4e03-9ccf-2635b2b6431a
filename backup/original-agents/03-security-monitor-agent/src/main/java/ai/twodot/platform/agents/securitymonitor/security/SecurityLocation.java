package ai.twodot.platform.agents.securitymonitor.security;

public record SecurityLocation(String country, String city, double latitude, double longitude) {
    public static SecurityLocation fromIP(String ipAddress) {
        // In a real implementation, this would use a GeoIP service.
        // For testing, we return a dummy location.
        if (ipAddress == null || ipAddress.isBlank()) {
            return null;
        }
        return new SecurityLocation("USA", "Mountain View", 37.422, -122.084);
    }
}