package ai.twodot.platform.agents.securitymonitor.agent;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.Duration;
import java.time.Instant;
import java.util.Map;

/**
 * Agent Health Status - Health information for the Security Monitor Agent
 */
public record AgentHealthStatus(
    @JsonProperty("agentId") String agentId,
    @JsonProperty("agentName") String agentName,
    @JsonProperty("version") String version,
    @JsonProperty("status") String status,
    @JsonProperty("uptime") Duration uptime,
    @JsonProperty("eventsProcessed") long eventsProcessed,
    @JsonProperty("threatsDetected") long threatsDetected,
    @JsonProperty("incidentsHandled") long incidentsHandled,
    @JsonProperty("componentStatus") Map<String, String> componentStatus,
    @JsonProperty("timestamp") Instant timestamp
) {
    
    public static Builder builder() {
        return new Builder();
    }
    
    public static class Builder {
        private String agentId;
        private String agentName;
        private String version;
        private String status;
        private Duration uptime;
        private long eventsProcessed;
        private long threatsDetected;
        private long incidentsHandled;
        private Map<String, String> componentStatus;
        private Instant timestamp;
        
        public Builder agentId(String agentId) {
            this.agentId = agentId;
            return this;
        }
        
        public Builder agentName(String agentName) {
            this.agentName = agentName;
            return this;
        }
        
        public Builder version(String version) {
            this.version = version;
            return this;
        }
        
        public Builder status(String status) {
            this.status = status;
            return this;
        }
        
        public Builder uptime(Duration uptime) {
            this.uptime = uptime;
            return this;
        }
        
        public Builder eventsProcessed(long eventsProcessed) {
            this.eventsProcessed = eventsProcessed;
            return this;
        }
        
        public Builder threatsDetected(long threatsDetected) {
            this.threatsDetected = threatsDetected;
            return this;
        }
        
        public Builder incidentsHandled(long incidentsHandled) {
            this.incidentsHandled = incidentsHandled;
            return this;
        }
        
        public Builder componentStatus(Map<String, String> componentStatus) {
            this.componentStatus = componentStatus;
            return this;
        }
        
        public Builder timestamp(Instant timestamp) {
            this.timestamp = timestamp;
            return this;
        }
        
        public AgentHealthStatus build() {
            return new AgentHealthStatus(
                agentId, agentName, version, status, uptime,
                eventsProcessed, threatsDetected, incidentsHandled,
                componentStatus, timestamp
            );
        }
    }
}