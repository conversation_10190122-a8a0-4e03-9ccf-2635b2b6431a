package ai.twodot.platform.agents.securitymonitor.security;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import java.util.concurrent.CompletableFuture;

@Service
public class SecurityEventProcessorImpl implements SecurityEventProcessor {
    private static final Logger logger = LoggerFactory.getLogger(SecurityEventProcessorImpl.class);

    @Override
    public String getStatus() { return "STREAMING"; }

    @Override
    public void startEventStreaming() { logger.info("Event streaming started (stub)."); }

    @Override
    public void stopEventStreaming() { logger.info("Event streaming stopped (stub)."); }

    @Override
    public CompletableFuture<SecurityEventResult> processEvent(SecurityEvent event) {
        logger.warn("processEvent on the stub SecurityEventProcessorImpl should not be called directly in the main flow.");
        return CompletableFuture.completedFuture(SecurityEventResult.success(event.eventId(), "Processed by stub processor."));
    }
}