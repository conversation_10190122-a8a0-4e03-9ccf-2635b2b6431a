package ai.twodot.platform.agents.securitymonitor.ai;

import ai.twodot.platform.agents.securitymonitor.security.SecurityEvent;
import ai.twodot.platform.agents.securitymonitor.security.ThreatAnalysisResult;

import java.util.concurrent.CompletableFuture;

public interface ThreatDetectionService {
    String getStatus();

    void initializeModels();

    CompletableFuture<ThreatAnalysisResult> analyzeThreat(SecurityEvent event);
}