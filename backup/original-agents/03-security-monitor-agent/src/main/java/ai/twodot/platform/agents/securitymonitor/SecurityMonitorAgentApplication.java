package ai.twodot.platform.agents.securitymonitor;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Security Monitor Agent (SMA-003) - Main Application
 * 
 * AI-powered cybersecurity monitoring and threat detection agent that provides:
 * - Real-time threat detection and analysis
 * - Behavioral anomaly detection
 * - Automated incident response
 * - Dynamic security policy enforcement
 * - AI-powered risk assessment
 * 
 * Architecture: Java 21 (Core) + Python (ML Models)
 */
@SpringBootApplication
@EnableAsync
@EnableScheduling
public class SecurityMonitorAgentApplication {

    /**
     * Main entry point for the Security Monitor Agent
     * 
     * @param args command line arguments
     */
    public static void main(String[] args) {
        // Set system properties for optimal security monitoring performance
        System.setProperty("java.util.concurrent.ForkJoinPool.common.parallelism", 
                          String.valueOf(Runtime.getRuntime().availableProcessors() * 2));
        
        // Enable virtual threads for high-concurrency security event processing
        System.setProperty("spring.threads.virtual.enabled", "true");
        
        // Configure security-optimized JVM settings
        System.setProperty("java.security.egd", "file:/dev/./urandom");
        
        SpringApplication app = new SpringApplication(SecurityMonitorAgentApplication.class);
        
        // Set application properties for security monitoring
        app.setAdditionalProfiles("security-monitor");
        
        app.run(args);
    }
}