package ai.twodot.platform.agents.securitymonitor.security;

import java.time.Instant;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

public interface IncidentResponseService {
    String getStatus();

    void loadResponsePlaybooks();

    CompletableFuture<IncidentResponseResult> respondToThreat(SecurityEvent event, ThreatAnalysisResult threatAnalysis,
            SecurityResponse securityResponse);

    record IncidentResponseResult(String eventId, String responseType, boolean success, Map<String, Object> details,
            Instant timestamp, long processingTimeMs) {
    }
}