package ai.twodot.platform.agents.securitymonitor.security;

import java.time.Instant;
import java.util.List;
import java.util.Map;

/**
 * Behavioral Analysis Result - Result of behavioral analysis
 */
public record BehavioralAnalysisResult(
    String analysisId,
    String entityId,
    SecurityEntityType entityType,
    boolean anomalyDetected,
    double anomalyScore,
    List<String> anomalousPatterns,
    Map<String, Double> behaviorMetrics,
    String baselineProfile,
    Instant analysisTime,
    long analysisWindow
) {
    public static BehavioralAnalysisResult normal(String entityId, SecurityEntityType entityType) {
        return new BehavioralAnalysisResult(
            java.util.UUID.randomUUID().toString(),
            entityId,
            entityType,
            false,
            0.1,
            List.of(),
            Map.of(),
            "normal",
            Instant.now(),
            3600000 // 1 hour
        );
    }
}