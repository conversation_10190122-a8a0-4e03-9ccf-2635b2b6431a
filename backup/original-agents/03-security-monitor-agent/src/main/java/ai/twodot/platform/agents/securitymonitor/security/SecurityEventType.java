package ai.twodot.platform.agents.securitymonitor.security;

/**
 * Security Event Types
 */
public enum SecurityEventType {
    // Authentication Events
    LOGIN_SUCCESS(false),
    LO<PERSON>N_FAILURE(true),
    LOGOUT(false),
    PASSWORD_CHANGE(false),
    <PERSON><PERSON>_CHALLENGE(false),
    BRUTE_FORCE_ATTACK(true),

    // Authorization Events
    ACCESS_GRANTED(false),
    ACCESS_DENIED(true),
    PR<PERSON><PERSON>LEGE_ESCALATION(true),
    PERMISSION_CHANGE(true),

    // Network Security Events
    NETWORK_INTRUSION(true),
    SUSPICIOUS_TRAFFIC(true),
    DDoS_ATTACK(true),
    PORT_SCAN(true),

    // Application Security Events
    SQL_INJECTION_ATTEMPT(true),
    XSS_ATTEMPT(true),
    CSRF_ATTEMPT(true),
    FILE_UPLOAD_THREAT(true),

    // Data Security Events
    DATA_BREACH(true),
    DATA_EXFILTRATION(true),
    UNAUTHORIZED_DATA_ACCESS(true),
    D<PERSON><PERSON>_MODIFICATION(true),

    // System Security Events
    MALWARE_DETECTED(true),
    VIRUS_DETECTED(true),
    RANSOMWARE_DETECTED(true),
    ROOTKIT_DETECTED(true),

    // Behavioral Events
    ANOMALOUS_BEHAVIOR(true),
    UNUSUAL_LOGIN_PATTERN(true),
    SUSPICIOUS_FILE_ACCESS(true),
    ABNORMAL_DATA_USAGE(true),

    // Compliance Events
    POLICY_VIOLATION(true),
    COMPLIANCE_BREACH(true),
    AUDIT_FAILURE(true),

    // Infrastructure Events
    SYSTEM_COMPROMISE(true),
    SERVICE_DISRUPTION(true),
    CONFIGURATION_CHANGE(false),

    // Advanced Threats
    APT_ACTIVITY(true),
    ACTIVE_ATTACK(true),
    LATERAL_MOVEMENT(true),
    COMMAND_AND_CONTROL(true),
    
    UNKNOWN(true);
    
    private final boolean highRisk;
    
    SecurityEventType(boolean highRisk) {
        this.highRisk = highRisk;
    }
    
    public boolean isHighRisk() {
        return highRisk;
    }
}