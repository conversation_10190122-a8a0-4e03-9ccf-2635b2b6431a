package ai.twodot.platform.agents.securitymonitor.monitoring;

import ai.twodot.platform.agents.securitymonitor.security.SecurityEvent;
import ai.twodot.platform.agents.securitymonitor.security.SecurityResponse;
import ai.twodot.platform.agents.securitymonitor.security.ThreatAnalysisResult;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

@Service
public class SecurityMetricsCollectorImpl implements SecurityMetricsCollector {

    private final AtomicLong totalEvents = new AtomicLong(0);
    private final AtomicLong totalThreats = new AtomicLong(0);
    private final AtomicBoolean collecting = new AtomicBoolean(false);
    private volatile Instant lastCollected = null;

    @Override
    public void startCollection() {
        collecting.set(true);
    }

    @Override
    public void stopCollection() {
        collecting.set(false);
    }

    @Override
    public void recordEvent(SecurityEvent event) {
        if (collecting.get()) {
            totalEvents.incrementAndGet();
            lastCollected = Instant.now();
        }
    }

    @Override
    public void recordThreatAnalysis(ThreatAnalysisResult threatAnalysis) {
        if (collecting.get() && threatAnalysis.isThreatDetected()) {
            totalThreats.incrementAndGet();
        }
    }

    @Override
    public void recordSecurityResponse(SecurityResponse securityResponse) {
        // This is a stub. A real implementation could record response metrics.
    }

    @Override
    public SecurityMetrics getCurrentMetrics() {
        return new SecurityMetrics(totalEvents.get(), totalThreats.get(), collecting.get(), lastCollected);
    }
}