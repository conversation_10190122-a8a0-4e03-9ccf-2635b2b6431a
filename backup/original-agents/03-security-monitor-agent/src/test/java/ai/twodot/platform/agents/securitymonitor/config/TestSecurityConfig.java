package ai.twodot.platform.agents.securitymonitor.config;

import ai.twodot.platform.agents.securitymonitor.integration.CommunicationBrokerClient;
import ai.twodot.platform.agents.securitymonitor.security.SecurityPolicyService;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.web.SecurityFilterChain;

import static org.mockito.Mockito.mock;

@TestConfiguration
public class TestSecurityConfig {

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.authorizeRequests(authorize -> authorize
                .requestMatchers("/actuator/health").permitAll()
                .anyRequest().authenticated()
        );
        return http.build();
    }

    @Bean
    @Primary
    public CommunicationBrokerClient communicationBrokerClient() {
        return mock(CommunicationBrokerClient.class);
    }

    @Bean
    @Primary
    public SecurityPolicyService securityPolicyService() {
        return mock(SecurityPolicyService.class);
    }
}
