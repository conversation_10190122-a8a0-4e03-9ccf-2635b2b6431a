package ai.twodot.platform.agents.securitymonitor.ai;

import ai.twodot.platform.agents.securitymonitor.security.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ThreatDetectionServiceTest {

    @Mock private WebClient webClient;
    @Mock private WebClient.RequestHeadersUriSpec requestHeadersUriSpec;
    @Mock private WebClient.RequestBodyUriSpec requestBodyUriSpec;
    @Mock private WebClient.RequestBodySpec requestBodySpec;
    @Mock private WebClient.ResponseSpec responseSpec;

    private ThreatDetectionServiceImpl threatDetectionService;

    @BeforeEach
    void setUp() {
        threatDetectionService = new ThreatDetectionServiceImpl();
        
        // Use reflection to set the mocked WebClient
        try {
            var webClientField = ThreatDetectionServiceImpl.class.getDeclaredField(
                "webClient"
            );
            webClientField.setAccessible(true);
            webClientField.set(threatDetectionService, webClient);
        } catch (Exception e) {
            fail("Could not set WebClient field: " + e.getMessage());
        }
    }

    @Test
    void testInitializeModels_Success() {
        // Arrange
        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri("/health")).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToMono(String.class)).thenReturn(Mono.just("{\"status\":\"healthy\"}"));

        // Act
        threatDetectionService.initializeModels();

        // Assert
        assertEquals("HEALTHY", threatDetectionService.getStatus());
    }

    @Test
    void testInitializeModels_Failure() {
        // Arrange
        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri("/health")).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToMono(String.class)).thenReturn(Mono.error(new RuntimeException("Connection failed")));

        // Act
        threatDetectionService.initializeModels();

        // Assert
        assertEquals("DEGRADED", threatDetectionService.getStatus());
    }

    @Test
    void testAnalyzeThreat_WithModelsNotInitialized() {
        // Arrange
        SecurityEvent event = createTestSecurityEvent();

        // Act
        ThreatAnalysisResult result = threatDetectionService.analyzeThreat(event).join();

        // Assert
        assertNotNull(result);
        assertEquals(event.eventId(), result.eventId());
        assertEquals("FALLBACK_RULES", result.analysisMethod());
        assertTrue(result.details().containsKey("reason"));
        assertEquals("AI models not initialized", result.details().get("reason"));
    }

    @Test
    void testAnalyzeThreat_WithMLServiceSuccess() {
        // Arrange
        SecurityEvent event = createTestSecurityEvent();
        ThreatDetectionServiceImpl.MLThreatDetectionResponse mlResponse = createMLResponse(
            true,
            0.95
        );
        // Initialize models first
        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri("/health")).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToMono(String.class)).thenReturn(Mono.just("{\"status\":\"healthy\"}"));
        threatDetectionService.initializeModels();

        // Mock ML service call
        when(webClient.post()).thenReturn(requestBodyUriSpec);
        when(requestBodyUriSpec.uri("/detect-threat")).thenReturn(requestBodySpec);
        when(requestBodySpec.bodyValue(any())).thenReturn(requestBodySpec);
        when(requestBodySpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToMono(ThreatDetectionServiceImpl.MLThreatDetectionResponse.class))
                .thenReturn(Mono.just(mlResponse));

        // Act
        ThreatAnalysisResult result = threatDetectionService.analyzeThreat(event).join();

        // Assert
        assertNotNull(result);
        assertEquals(event.eventId(), result.eventId());
        assertTrue(result.threatDetected());
        assertEquals(0.95, result.confidenceScore());
        assertEquals("BRUTE_FORCE_ATTACK", result.threatType());
        assertEquals("TEST_ML_METHOD", result.analysisMethod());
        assertTrue(result.requiresBehavioralAnalysis());
    }

    @Test
    void testAnalyzeThreat_WithMLServiceFailure() {
        // Arrange
        SecurityEvent event = createTestSecurityEvent();
        
        // Initialize models first
        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri("/health")).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToMono(String.class)).thenReturn(Mono.just("{\"status\":\"healthy\"}"));
        threatDetectionService.initializeModels();

        // Mock ML service failure
        when(webClient.post()).thenReturn(requestBodyUriSpec);
        when(requestBodyUriSpec.uri("/detect-threat")).thenReturn(requestBodySpec);
        when(requestBodySpec.bodyValue(any())).thenReturn(requestBodySpec);
        when(requestBodySpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToMono(ThreatDetectionServiceImpl.MLThreatDetectionResponse.class))
                .thenReturn(Mono.error(new WebClientResponseException(500, "Internal Server Error", null, null, null)));

        // Act
        ThreatAnalysisResult result = threatDetectionService.analyzeThreat(event).join();

        // Assert
        assertNotNull(result);
        assertEquals(event.eventId(), result.eventId());
        assertEquals("FALLBACK_RULES", result.analysisMethod());
        assertTrue(result.threatDetected()); // High severity event should be detected by fallback
    }

    @Test
    void testAnalyzeThreatAsync() {
        // Arrange
        SecurityEvent event = createTestSecurityEvent();

        // Act
        CompletableFuture<ThreatAnalysisResult> futureResult = threatDetectionService.analyzeThreat(
            event
        );
        ThreatAnalysisResult result = futureResult.join();

        // Assert
        assertNotNull(result);
        assertEquals(event.eventId(), result.eventId());
    }

    @Test
    void testStartStopThreatHunting() {
        // Act & Assert - These are currently placeholder methods
        assertDoesNotThrow(() -> threatDetectionService.startThreatHunting());
        assertDoesNotThrow(() -> threatDetectionService.stopThreatHunting());
    }

    @Test
    void testGetMetrics() {
        // Arrange
        SecurityEvent event = createTestSecurityEvent();
        
        // Process some events to generate metrics
        threatDetectionService.analyzeThreat(event).join();
        threatDetectionService.analyzeThreat(event).join();

        // Act
        ThreatDetectionServiceImpl.ThreatDetectionMetrics metrics = threatDetectionService.getMetrics();

        // Assert
        assertNotNull(metrics);
        assertEquals(0, metrics.successfulRequests());
        assertEquals(2L, metrics.totalRequests());
        assertFalse(metrics.modelsInitialized()); // Models not initialized in this test
    }

    @Test
    void testGetStatus_NotInitialized() {
        // Act
        String status = threatDetectionService.getStatus();

        // Assert
        assertEquals("DEGRADED", status);
    }

    @Test
    void testGetStatus_Initialized() {
        // Arrange - Initialize models
        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri("/health")).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToMono(String.class)).thenReturn(Mono.just("{\"status\":\"healthy\"}"));
        threatDetectionService.initializeModels();

        // Act
        String status = threatDetectionService.getStatus();

        // Assert
        assertEquals("HEALTHY", status);
    }

    // Helper methods

    private SecurityEvent createTestSecurityEvent() {
        return SecurityEvent.createDetailed(
            SecurityEventType.LOGIN_FAILURE,
            SecuritySeverity.HIGH,
            SecurityEventSource.WEB_APPLICATION,
            "user123",
            SecurityEntityType.USER,
            "Failed login attempt",
            "*************",
            "********",
            "Mozilla/5.0",
            "session123",
            SecurityLocation.fromIP("*************"),
            Map.of("attempts", 3),
            "raw login data"
        );
    }

    private ThreatDetectionServiceImpl.MLThreatDetectionResponse createMLResponse(
        boolean threatDetected,
        double confidence
    ) {
        return new ThreatDetectionServiceImpl.MLThreatDetectionResponse(
            "test-event-123",
            threatDetected,
            threatDetected ? "BRUTE_FORCE_ATTACK" : "NORMAL",
            confidence,
            confidence > 0.7 ? "HIGH" : "LOW",
            "TEST_ML_METHOD",
            Map.of("ml_analysis", true),
            Instant.now(),
            50.0
        );
    }
}