package ai.twodot.platform.agents.securitymonitor.integration;

import ai.twodot.platform.agents.securitymonitor.SecurityMonitorAgentApplication;
import ai.twodot.platform.agents.securitymonitor.agent.SecurityMonitorAgent;
import ai.twodot.platform.agents.securitymonitor.config.TestSecurityConfig;
import ai.twodot.platform.agents.securitymonitor.security.SecurityEventProcessor.SecurityEventResult;
import ai.twodot.platform.agents.securitymonitor.security.*;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import java.time.Instant;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration tests for Security Monitor Agent
 * Tests the complete flow from event ingestion to threat response
 */
@SpringBootTest(classes = SecurityMonitorAgentApplication.class)
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "sma.python.service.url=http://localhost:8081",
    "sma.python.service.timeout=5000",
    "spring.threads.virtual.enabled=true"
})
@Import(TestSecurityConfig.class)
class SecurityMonitorAgentIntegrationTest {

    @Autowired
    private SecurityMonitorAgent securityMonitorAgent;

    @BeforeEach
    void setUp() {
        // Initialize the agent for each test
        if (!securityMonitorAgent.isRunning()) {
            securityMonitorAgent.initialize();
        }
    }

    @Test
    void testAgentInitialization() {
        // Test that the agent initializes correctly
        assertTrue(securityMonitorAgent.isRunning());
        assertEquals("SMA-003", securityMonitorAgent.getHealthStatus().agentId());
        assertEquals("Security Monitor Agent", securityMonitorAgent.getHealthStatus().agentName());
        assertEquals("1.0.0", securityMonitorAgent.getHealthStatus().version());
    }

    @Test
    void testHealthStatusCheck() {
        // Test health status reporting
        var healthStatus = securityMonitorAgent.getHealthStatus();
        
        assertNotNull(healthStatus);
        assertEquals("SMA-003", healthStatus.agentId());
        assertNotNull(healthStatus.uptime());
        assertNotNull(healthStatus.componentStatus());
        assertTrue(healthStatus.componentStatus().size() > 0);
    }

    @Test
    void testHighSeverityThreatProcessing() {
        // Create a high-severity security event
        SecurityEvent highSeverityEvent = SecurityEvent.createDetailed(
            SecurityEventType.MALWARE_DETECTED,
            SecuritySeverity.CRITICAL,
            SecurityEventSource.ENDPOINT_PROTECTION,
            "workstation-001",
            SecurityEntityType.DEVICE,
            "Critical malware detected on workstation",
            "************",
            "**********",
            "malicious-process",
            "session-critical-001",
            SecurityLocation.fromIP("************"),
            Map.of("malware_type", "ransomware", "severity_score", 95),
            "malware signature: 0xDEADBEEF"
        );

        // Process the event
        CompletableFuture<SecurityEventResult> resultFuture =
            securityMonitorAgent.processSecurityEvent(highSeverityEvent);
        
        SecurityEventResult result = resultFuture.join();
        
        // Verify processing
        assertNotNull(result);
        assertTrue(result.success());
        assertEquals(highSeverityEvent.eventId(), result.eventId());
        assertNotNull(result.message());
        
        // Verify metrics updated
        assertTrue(securityMonitorAgent.getEventsProcessed() > 0);
        assertTrue(securityMonitorAgent.getThreatsDetected() > 0);
    }

    @Test
    void testNormalEventProcessing() {
        // Create a normal security event
        SecurityEvent normalEvent = SecurityEvent.create(
            SecurityEventType.LOGIN_SUCCESS,
            SecuritySeverity.INFO,
            SecurityEventSource.WEB_APPLICATION,
            "user-normal",
            SecurityEntityType.USER,
            "Successful user login"
        );

        // Process the event
        CompletableFuture<SecurityEventResult> resultFuture = 
            securityMonitorAgent.processSecurityEvent(normalEvent);
        
        SecurityEventResult result = resultFuture.join();
        
        // Verify processing
        assertNotNull(result);
        assertTrue(result.success());
        assertEquals(normalEvent.eventId(), result.eventId());
        assertNotNull(result.message());
        
        // Verify metrics updated
        assertTrue(securityMonitorAgent.getEventsProcessed() > 0);
    }

    @Test
    void testMultipleConcurrentEvents() throws InterruptedException {
        // Create multiple security events
        SecurityEvent[] events = {
            SecurityEvent.create(
                SecurityEventType.LOGIN_FAILURE, SecuritySeverity.MEDIUM,
                SecurityEventSource.WEB_APPLICATION, "user1", SecurityEntityType.USER,
                "Failed login attempt 1"
            ),
            SecurityEvent.create(
                SecurityEventType.ACCESS_DENIED, SecuritySeverity.HIGH,
                SecurityEventSource.API_GATEWAY, "api-client", SecurityEntityType.API_CLIENT,
                "API access denied"
            ),
            SecurityEvent.create(
                SecurityEventType.NETWORK_INTRUSION, SecuritySeverity.CRITICAL,
                SecurityEventSource.INTRUSION_DETECTION_SYSTEM, "network", SecurityEntityType.NETWORK_SEGMENT,
                "Network intrusion detected"
            )
        };

        long initialEventsProcessed = securityMonitorAgent.getEventsProcessed();
        
        // Process events concurrently
        CompletableFuture<SecurityEventResult>[] futures = 
            new CompletableFuture[events.length];
        
        for (int i = 0; i < events.length; i++) {
            futures[i] = securityMonitorAgent.processSecurityEvent(events[i]);
        }
        
        // Wait for all to complete
        CompletableFuture.allOf(futures).join();
        
        // Verify all events were processed
        assertEquals(initialEventsProcessed + events.length, securityMonitorAgent.getEventsProcessed());
        
        // Verify all results are successful
        for (CompletableFuture<SecurityEventResult> future : futures) {
            SecurityEventResult result = future.join();
            assertNotNull(result);
            assertTrue(result.success());
        }
    }

    @Test
    void testBruteForceAttackScenario() {
        // Simulate a brute force attack scenario
        String attackerIP = "*************";
        String targetUser = "admin";
        
        // Create multiple failed login events from same IP
        for (int i = 0; i < 5; i++) {
            SecurityEvent bruteForceEvent = SecurityEvent.createDetailed(
                SecurityEventType.LOGIN_FAILURE,
                SecuritySeverity.HIGH,
                SecurityEventSource.WEB_APPLICATION,
                targetUser,
                SecurityEntityType.USER,
                "Brute force login attempt " + (i + 1),
                attackerIP,
                "********",
                "attack-bot/1.0",
                "session-" + i,
                SecurityLocation.fromIP(attackerIP),
                Map.of("attempt_number", i + 1, "attack_pattern", "brute_force"),
                "failed login data"
            );
            
            CompletableFuture<SecurityEventResult> result = 
                securityMonitorAgent.processSecurityEvent(bruteForceEvent);
            
            SecurityEventResult eventResult = result.join();
            assertNotNull(eventResult);
            assertTrue(eventResult.success());
        }
        
        // Verify that multiple threats were detected
        assertTrue(securityMonitorAgent.getThreatsDetected() >= 3); // At least 3 of 5 should be detected
        assertTrue(securityMonitorAgent.getIncidentsHandled() >= 1); // At least one incident response
    }

    @Test
    void testSecurityMetricsCollection() {
        // Process some events to generate metrics
        SecurityEvent event1 = SecurityEvent.create(
            SecurityEventType.VIRUS_DETECTED, SecuritySeverity.HIGH,
            SecurityEventSource.ENDPOINT_PROTECTION, "endpoint1", SecurityEntityType.DEVICE,
            "Virus detected"
        );
        
        SecurityEvent event2 = SecurityEvent.create(
            SecurityEventType.DATA_BREACH, SecuritySeverity.CRITICAL,
            SecurityEventSource.DATABASE, "database1", SecurityEntityType.DATABASE,
            "Data breach detected"
        );
        
        long initialEvents = securityMonitorAgent.getEventsProcessed();
        long initialThreats = securityMonitorAgent.getThreatsDetected();
        
        // Process events
        securityMonitorAgent.processSecurityEvent(event1).join();
        securityMonitorAgent.processSecurityEvent(event2).join();
        
        // Check metrics
        var metrics = securityMonitorAgent.getSecurityMetrics();
        assertNotNull(metrics);
        
        // Verify counters increased
        assertEquals(initialEvents + 2, securityMonitorAgent.getEventsProcessed());
        assertTrue(securityMonitorAgent.getThreatsDetected() > initialThreats);
    }

    @Test
    void testAgentShutdownAndRestart() {
        // Test graceful shutdown
        assertTrue(securityMonitorAgent.isRunning());
        
        securityMonitorAgent.shutdown();
        assertFalse(securityMonitorAgent.isRunning());
        
        // Test restart
        securityMonitorAgent.initialize();
        assertTrue(securityMonitorAgent.isRunning());
    }

    @Test
    void testErrorHandling() {
        // Create an event that might cause processing errors
        SecurityEvent malformedEvent = new SecurityEvent(
            "malformed-event",
            SecurityEventType.UNKNOWN,
            SecuritySeverity.HIGH,
            SecurityEventSource.MANUAL_REPORT,
            "", // Empty entity ID
            SecurityEntityType.UNKNOWN,
            Instant.now(),
            "", // Empty description
            null, null, null, null, null,
            Map.of(),
            null, null
        );
        
        // Process the malformed event
        CompletableFuture<SecurityEventResult> resultFuture = 
            securityMonitorAgent.processSecurityEvent(malformedEvent);
        
        // Should handle gracefully without throwing exceptions
        assertDoesNotThrow(() -> {
            SecurityEventResult result = resultFuture.join();
            assertNotNull(result);
            // Result might be success or failure, but should not throw
            assertNotNull(result.eventId());
        });
    }

    @Test
    void testPerformanceUnderLoad() {
        // Test performance with multiple events
        int numberOfEvents = 10;
        long startTime = System.currentTimeMillis();
        
        CompletableFuture<SecurityEventResult>[] futures = 
            new CompletableFuture[numberOfEvents];
        
        // Create and process multiple events
        for (int i = 0; i < numberOfEvents; i++) {
            SecurityEvent event = SecurityEvent.create(
                SecurityEventType.ACCESS_DENIED, SecuritySeverity.MEDIUM,
                SecurityEventSource.API_GATEWAY, "load-test-" + i, SecurityEntityType.API_CLIENT,
                "Load test event " + i
            );
            
            futures[i] = securityMonitorAgent.processSecurityEvent(event);
        }
        
        // Wait for all to complete
        CompletableFuture.allOf(futures).join();
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        
        // Verify all completed successfully
        for (CompletableFuture<SecurityEventResult> future : futures) {
            SecurityEventResult result = future.join();
            assertNotNull(result);
            assertTrue(result.success());
        }
        
        // Performance assertion - should process 10 events in reasonable time
        assertTrue(totalTime < 5000, "Processing 10 events took too long: " + totalTime + "ms");
        
        // Verify metrics
        assertTrue(securityMonitorAgent.getEventsProcessed() >= numberOfEvents);
    }
}
