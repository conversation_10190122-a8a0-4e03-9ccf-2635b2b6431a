package ai.twodot.platform.agents.securitymonitor.integration;

import ai.twodot.platform.agents.securitymonitor.SecurityMonitorAgentApplication;
import ai.twodot.platform.agents.securitymonitor.agent.SecurityMonitorAgent;
import ai.twodot.platform.agents.securitymonitor.config.TestSecurityConfig;
import ai.twodot.platform.agents.securitymonitor.security.SecurityEventProcessor.SecurityEventResult;
import ai.twodot.platform.agents.securitymonitor.security.SecurityEvent;
import ai.twodot.platform.agents.securitymonitor.security.SecurityEventType;
import ai.twodot.platform.agents.securitymonitor.security.SecuritySeverity;
import ai.twodot.platform.agents.securitymonitor.security.SecurityEventSource;
import ai.twodot.platform.agents.securitymonitor.security.SecurityEntityType;
import ai.twodot.platform.agents.securitymonitor.security.SecurityLocation;
import ai.twodot.platform.agents.securitymonitor.integration.AgentIntegrationService;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Performance tests for the integrated Security Monitor Agent system.
 * Tests throughput, latency, and scalability under various load conditions.
 */
@SpringBootTest(classes = SecurityMonitorAgentApplication.class)
@ActiveProfiles("test")
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@SpringJUnitConfig
@Import(TestSecurityConfig.class)
class AgentPerformanceTest {

    @Autowired
    private SecurityMonitorAgent securityAgent;

    @Autowired
    private AgentIntegrationService integrationService;

    @Autowired
    private CommunicationBrokerClient cbaClient;

    @MockBean
    private DiscoveryRegistryClient draClient;

    @MockBean
    private SecurityEventStreamer eventStreamer;

    private ExecutorService testExecutor;

    @BeforeEach
    void setUp() {
        testExecutor = Executors.newFixedThreadPool(20);

        // Mock responses for performance testing
        when(cbaClient.isConnected()).thenReturn(true);
        when(cbaClient.sendMessage(anyString(), anyString(), any())).thenReturn(
            CompletableFuture.completedFuture(createMockResponse(50))
        );
        when(cbaClient.sendBroadcastMessage(anyString(), any())).thenReturn(
            CompletableFuture.completedFuture(createMockResponse(75))
        );

        when(draClient.isRegistered()).thenReturn(true);
        when(eventStreamer.streamSecurityEvent(any(), any())).thenReturn(
            CompletableFuture.completedFuture(createMockStreamingResponse())
        );
    }

    @Test
    void testSingleEventProcessingPerformance() throws Exception {
        // Arrange
        SecurityEvent testEvent = createTestSecurityEvent();
        List<Long> processingTimes = new ArrayList<>();

        // Act
        for (int i = 0; i < 100; i++) {
            Instant start = Instant.now();
            CompletableFuture<SecurityEventResult> result =
                securityAgent.processSecurityEvent(testEvent);
            result.get(5, TimeUnit.SECONDS);
            Instant end = Instant.now();

            processingTimes.add(Duration.between(start, end).toMillis());
        }

        // Assert
        double avgProcessingTime = processingTimes.stream().mapToLong(Long::longValue).average().orElse(0);
        long maxProcessingTime = processingTimes.stream().mapToLong(Long::longValue).max().orElse(0);
        long minProcessingTime = processingTimes.stream().mapToLong(Long::longValue).min().orElse(0);

        System.out.printf("Single Event Processing Performance:%n");
        System.out.printf("  Average: %.2f ms%n", avgProcessingTime);
        System.out.printf("  Min: %d ms%n", minProcessingTime);
        System.out.printf("  Max: %d ms%n", maxProcessingTime);

        // Performance assertions (targets from requirements)
        assertTrue(avgProcessingTime < 100, "Average processing time should be < 100ms");
        assertTrue(maxProcessingTime < 500, "Max processing time should be < 500ms");
    }

    @Test
    void testConcurrentEventProcessingThroughput() throws Exception {
        // Arrange
        int numberOfEvents = 500;
        int concurrentThreads = 10;
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch completionLatch = new CountDownLatch(numberOfEvents);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);
        AtomicLong totalProcessingTime = new AtomicLong(0);

        // Act
        Instant testStart = Instant.now();
        for (int i = 0; i < numberOfEvents; i++) {
            testExecutor.submit(() -> {
                try {
                    startLatch.await();

                    Instant eventStart = Instant.now();
                    SecurityEvent event = createRandomSecurityEvent();
                    CompletableFuture<SecurityEventResult> result =
                        securityAgent.processSecurityEvent(event);

                    result.get(10, TimeUnit.SECONDS);
                    Instant eventEnd = Instant.now();

                    totalProcessingTime.addAndGet(Duration.between(eventStart, eventEnd).toMillis());
                    successCount.incrementAndGet();

                } catch (Exception e) {
                    errorCount.incrementAndGet();
                } finally {
                    completionLatch.countDown();
                }
            });
        }

        startLatch.countDown(); // Start all threads
        boolean completed = completionLatch.await(60, TimeUnit.SECONDS);
        Instant testEnd = Instant.now();

        // Assert
        assertTrue(completed, "All events should complete within timeout");

        long totalTestTime = Duration.between(testStart, testEnd).toMillis();
        double throughput = (double) successCount.get() / (totalTestTime / 1000.0);
        double avgProcessingTime = (double) totalProcessingTime.get() / successCount.get();
        double errorRate = (double) errorCount.get() / numberOfEvents;

        System.out.printf("Concurrent Processing Performance:%n");
        System.out.printf("  Events processed: %d/%d%n", successCount.get(), numberOfEvents);
        System.out.printf("  Throughput: %.2f events/second%n", throughput);
        System.out.printf("  Average processing time: %.2f ms%n", avgProcessingTime);
        System.out.printf("  Error rate: %.2f%%%n", errorRate * 100);
        System.out.printf("  Total test time: %d ms%n", totalTestTime);

        // Performance assertions
        assertTrue(throughput >= 100, "Throughput should be >= 100 events/second");
        assertTrue(errorRate < 0.01, "Error rate should be < 1%");
        assertTrue(avgProcessingTime < 200, "Average processing time should be < 200ms under load");
    }

    @Test
    void testA2AMessageProcessingPerformance() throws Exception {
        // Arrange
        int numberOfMessages = 200;
        List<CompletableFuture<AgentIntegrationService.A2AMessageResponse>> futures = new ArrayList<>();

        // Act
        Instant start = Instant.now();
        for (int i = 0; i < numberOfMessages; i++) {
            AgentIntegrationService.A2AMessage message = createRandomA2AMessage();
            CompletableFuture<AgentIntegrationService.A2AMessageResponse> future =
                integrationService.processIncomingThreatAlert(message);
            futures.add(future);
        }

        // Wait for all to complete
        CompletableFuture<Void> allOf = CompletableFuture.allOf(
            futures.toArray(new CompletableFuture[0])
        );
        allOf.get(30, TimeUnit.SECONDS);
        Instant end = Instant.now();

        // Assert
        long totalTime = Duration.between(start, end).toMillis();
        double throughput = (double) numberOfMessages / (totalTime / 1000.0);

        long successCount = futures.stream()
            .mapToLong(f -> {
                try {
                    return f.get().success() ? 1 : 0;
                } catch (Exception e) {
                    return 0;
                }
            })
            .sum();

        System.out.printf("A2A Message Processing Performance:%n");
        System.out.printf("  Messages processed: %d/%d%n", successCount, numberOfMessages);
        System.out.printf("  Throughput: %.2f messages/second%n", throughput);
        System.out.printf("  Total time: %d ms%n", totalTime);

        assertTrue(throughput >= 50, "A2A message throughput should be >= 50 messages/second");
        assertEquals(numberOfMessages, successCount, "All A2A messages should be processed successfully");
    }

    @Test
    void testMemoryUsageUnderLoad() throws Exception {
        // Arrange
        Runtime runtime = Runtime.getRuntime();
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();

        int numberOfEvents = 1000;
        List<CompletableFuture<SecurityEventResult>> futures = new ArrayList<>();

        // Act
        for (int i = 0; i < numberOfEvents; i++) {
            SecurityEvent event = createRandomSecurityEvent();
            CompletableFuture<SecurityEventResult> future =
                securityAgent.processSecurityEvent(event);
            futures.add(future);

            // Periodic memory check
            if (i % 100 == 0) {
                long currentMemory = runtime.totalMemory() - runtime.freeMemory();
                long memoryIncrease = currentMemory - initialMemory;
                System.out.printf("Memory after %d events: %d MB (increase: %d MB)%n",
                    i, currentMemory / 1024 / 1024, memoryIncrease / 1024 / 1024);
            }
        }

        // Wait for completion
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
            .get(60, TimeUnit.SECONDS);

        // Force garbage collection and measure final memory
        System.gc();
        Thread.sleep(1000);
        long finalMemory = runtime.totalMemory() - runtime.freeMemory();
        long memoryIncrease = finalMemory - initialMemory;

        // Assert
        System.out.printf("Memory Usage Analysis:%n");
        System.out.printf("  Initial memory: %d MB%n", initialMemory / 1024 / 1024);
        System.out.printf("  Final memory: %d MB%n", finalMemory / 1024 / 1024);
        System.out.printf("  Memory increase: %d MB%n", memoryIncrease / 1024 / 1024);
        System.out.printf("  Memory per event: %.2f KB%n", (double) memoryIncrease / numberOfEvents / 1024);

        // Memory should not increase by more than 512MB for 1000 events
        assertTrue(memoryIncrease < 512 * 1024 * 1024,
            "Memory increase should be < 512MB for 1000 events");
    }

    @Test
    void testSystemStabilityUnderSustainedLoad() throws Exception {
        // Arrange
        int testDurationSeconds = 30;
        AtomicInteger eventsProcessed = new AtomicInteger(0);
        AtomicInteger errors = new AtomicInteger(0);
        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(5);

        // Act
        Instant testStart = Instant.now();

        // Schedule continuous event generation
        ScheduledFuture<?> eventGenerator = scheduler.scheduleAtFixedRate(() -> {
            try {
                SecurityEvent event = createRandomSecurityEvent();
                securityAgent.processSecurityEvent(event)
                    .thenRun(() -> eventsProcessed.incrementAndGet())
                    .exceptionally(throwable -> {
                        errors.incrementAndGet();
                        return null;
                    });
            } catch (Exception e) {
                errors.incrementAndGet();
            }
        }, 0, 10, TimeUnit.MILLISECONDS); // 100 events/second

        // Schedule A2A message generation
        ScheduledFuture<?> messageGenerator = scheduler.scheduleAtFixedRate(() -> {
            try {
                AgentIntegrationService.A2AMessage message = createRandomA2AMessage();
                integrationService.processIncomingThreatAlert(message)
                    .exceptionally(throwable -> {
                        errors.incrementAndGet();
                        return null;
                    });
            } catch (Exception e) {
                errors.incrementAndGet();
            }
        }, 0, 50, TimeUnit.MILLISECONDS); // 20 messages/second

        // Run for specified duration
        Thread.sleep(testDurationSeconds * 1000);

        eventGenerator.cancel(false);
        messageGenerator.cancel(false);
        scheduler.shutdown();
        scheduler.awaitTermination(5, TimeUnit.SECONDS);

        Instant testEnd = Instant.now();

        // Assert
        long totalTestTime = Duration.between(testStart, testEnd).toMillis();
        double eventThroughput = (double) eventsProcessed.get() / (totalTestTime / 1000.0);
        double errorRate = (double) errors.get() / (eventsProcessed.get() + errors.get());

        System.out.printf("Sustained Load Test Results:%n");
        System.out.printf("  Test duration: %d seconds%n", testDurationSeconds);
        System.out.printf("  Events processed: %d%n", eventsProcessed.get());
        System.out.printf("  Errors: %d%n", errors.get());
        System.out.printf("  Event throughput: %.2f events/second%n", eventThroughput);
        System.out.printf("  Error rate: %.2f%%%n", errorRate * 100);

        assertTrue(eventThroughput >= 90, "Should maintain >= 90 events/second under sustained load");
        assertTrue(errorRate < 0.05, "Error rate should be < 5% under sustained load");
        assertTrue(eventsProcessed.get() > 0, "Should process events successfully");
    }

    // Helper methods

    private SecurityEvent createTestSecurityEvent() {
        return SecurityEvent.createDetailed(
            SecurityEventType.MALWARE_DETECTED,
            SecuritySeverity.HIGH,
            SecurityEventSource.ENDPOINT_PROTECTION,
            "test-endpoint-001",
            SecurityEntityType.DEVICE,
            "Test malware detection",
            "192.168.1.100",
            null,
            null,
            null,
            null,
            Map.of("test_id", UUID.randomUUID().toString()),
            "Performance test event"
        );
    }

    private SecurityEvent createRandomSecurityEvent() {
        SecurityEventType[] eventTypes = SecurityEventType.values();
        SecuritySeverity[] severities = SecuritySeverity.values();
        SecurityEventSource[] sources = SecurityEventSource.values();

        return SecurityEvent.createDetailed(
            eventTypes[(int) (Math.random() * eventTypes.length)],
            severities[(int) (Math.random() * severities.length)],
            sources[(int) (Math.random() * sources.length)],
            "random-entity-" + UUID.randomUUID().toString().substring(0, 8),
            SecurityEntityType.HOST,
            "Random security event " + UUID.randomUUID().toString().substring(0, 8),
            "192.168.1." + (int) (Math.random() * 255),
            null,
            null,
            null,
            null,
            Map.of("random_id", UUID.randomUUID().toString()),
            "Random security event for performance testing"
        );
    }

    private AgentIntegrationService.A2AMessage createRandomA2AMessage() {
        String[] threatTypes = {"malware", "intrusion", "data_breach", "phishing", "ddos"};
        String[] severities = {"LOW", "MEDIUM", "HIGH", "CRITICAL"};

        AgentIntegrationService.ThreatAlertMessage threat = new AgentIntegrationService.ThreatAlertMessage(
            UUID.randomUUID().toString(),
            threatTypes[(int) (Math.random() * threatTypes.length)],
            severities[(int) (Math.random() * severities.length)],
            Math.random(),
            "Random threat alert for performance testing",
            List.of("indicator1", "indicator2"),
            Instant.now()
        );

        return new AgentIntegrationService.A2AMessage(
            UUID.randomUUID().toString(),
            "PERF-TEST-AGENT-" + (int) (Math.random() * 100),
            "SMA-003",
            "threat_alert",
            "notification",
            "HIGH",
            threat,
            Instant.now(),
            Map.of("protocol", "a2a", "test", "performance")
        );
    }

    private CommunicationBrokerClient.MessageResponse createMockResponse(long processingTimeMs) {
        return new CommunicationBrokerClient.MessageResponse(
            UUID.randomUUID().toString(),
            "success",
            true,
            Map.of("status", "processed"),
            null,
            processingTimeMs + "ms",
            Instant.now()
        );
    }

    private SecurityEventStreamer.StreamingResponse createMockStreamingResponse() {
        return SecurityEventStreamer.StreamingResponse.queued();
    }
}
