package ai.twodot.platform.agents.securitymonitor.security;

import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class SecurityEventTest {

    @Test
    void testCreateBasicSecurityEvent() {
        // Act
        SecurityEvent event = SecurityEvent.create(
            SecurityEventType.LOGIN_FAILURE,
            SecuritySeverity.HIGH,
            SecurityEventSource.WEB_APPLICATION,
            "user123",
            SecurityEntityType.USER,
            "Failed login attempt"
        );

        // Assert
        assertNotNull(event);
        assertNotNull(event.eventId());
        assertEquals(SecurityEventType.LOGIN_FAILURE, event.eventType());
        assertEquals(SecuritySeverity.HIGH, event.severity());
        assertEquals(SecurityEventSource.WEB_APPLICATION, event.source());
        assertEquals("user123", event.entityId());
        assertEquals(SecurityEntityType.USER, event.entityType());
        assertEquals("Failed login attempt", event.description());
        assertNotNull(event.timestamp());
        assertNotNull(event.metadata());
        assertTrue(event.metadata().isEmpty());
    }

    @Test
    void testCreateDetailedSecurityEvent() {
        // Arrange
        Map<String, Object> metadata = Map.of("attempts", 3, "location", "office");
        SecurityLocation location = SecurityLocation.fromIP("*************");

        // Act
        SecurityEvent event = SecurityEvent.createDetailed(
            SecurityEventType.BRUTE_FORCE_ATTACK,
            SecuritySeverity.CRITICAL,
            SecurityEventSource.INTRUSION_DETECTION_SYSTEM,
            "user456",
            SecurityEntityType.USER,
            "Brute force attack detected",
            "*************",
            "********",
            "Mozilla/5.0",
            "session456",
            location,
            metadata,
            "raw attack data"
        );

        // Assert
        assertNotNull(event);
        assertEquals(SecurityEventType.BRUTE_FORCE_ATTACK, event.eventType());
        assertEquals(SecuritySeverity.CRITICAL, event.severity());
        assertEquals("*************", event.sourceIP());
        assertEquals("********", event.targetIP());
        assertEquals("Mozilla/5.0", event.userAgent());
        assertEquals("session456", event.sessionId());
        assertEquals(location, event.location());
        assertEquals(metadata, event.metadata());
        assertEquals("raw attack data", event.rawData());
        assertNotNull(event.correlationId());
    }

    @Test
    void testIsHighPriority() {
        // High priority events
        SecurityEvent criticalEvent = SecurityEvent.create(
            SecurityEventType.DATA_BREACH, SecuritySeverity.CRITICAL,
            SecurityEventSource.AUTOMATED_DETECTION, "system", SecurityEntityType.APPLICATION,
            "Data breach detected"
        );
        assertTrue(criticalEvent.isHighPriority());

        SecurityEvent highSeverityEvent = SecurityEvent.create(
            SecurityEventType.LOGIN_FAILURE, SecuritySeverity.HIGH,
            SecurityEventSource.WEB_APPLICATION, "user", SecurityEntityType.USER,
            "High severity login failure"
        );
        assertTrue(highSeverityEvent.isHighPriority());

        SecurityEvent highRiskTypeEvent = SecurityEvent.create(
            SecurityEventType.MALWARE_DETECTED, SecuritySeverity.MEDIUM,
            SecurityEventSource.ENDPOINT_PROTECTION, "endpoint", SecurityEntityType.DEVICE,
            "Malware detected"
        );
        assertTrue(highRiskTypeEvent.isHighPriority());

        // Low priority event
        SecurityEvent lowPriorityEvent = SecurityEvent.create(
            SecurityEventType.CONFIGURATION_CHANGE, SecuritySeverity.INFO,
            SecurityEventSource.SYSTEM_LOG, "admin", SecurityEntityType.USER,
            "Configuration updated"
        );
        assertFalse(lowPriorityEvent.isHighPriority());
    }

    @Test
    void testRequiresImmediateResponse() {
        // Events requiring immediate response
        SecurityEvent criticalEvent = SecurityEvent.create(
            SecurityEventType.ACTIVE_ATTACK, SecuritySeverity.CRITICAL,
            SecurityEventSource.INTRUSION_DETECTION_SYSTEM, "system", SecurityEntityType.APPLICATION,
            "Active attack in progress"
        );
        assertTrue(criticalEvent.requiresImmediateResponse());

        SecurityEvent dataBreachEvent = SecurityEvent.create(
            SecurityEventType.DATA_BREACH, SecuritySeverity.HIGH,
            SecurityEventSource.DATABASE, "db", SecurityEntityType.DATABASE,
            "Data breach detected"
        );
        assertTrue(dataBreachEvent.requiresImmediateResponse());

        // Event not requiring immediate response
        SecurityEvent normalEvent = SecurityEvent.create(
            SecurityEventType.LOGIN_SUCCESS, SecuritySeverity.INFO,
            SecurityEventSource.WEB_APPLICATION, "user", SecurityEntityType.USER,
            "Successful login"
        );
        assertFalse(normalEvent.requiresImmediateResponse());
    }

    @Test
    void testWithCorrelationId() {
        // Arrange
        SecurityEvent originalEvent = SecurityEvent.create(
            SecurityEventType.LOGIN_FAILURE, SecuritySeverity.MEDIUM,
            SecurityEventSource.WEB_APPLICATION, "user", SecurityEntityType.USER,
            "Login failed"
        );
        String correlationId = "correlation-123";

        // Act
        SecurityEvent eventWithCorrelation = originalEvent.withCorrelationId(correlationId);

        // Assert
        assertEquals(correlationId, eventWithCorrelation.correlationId());
        assertEquals(originalEvent.eventId(), eventWithCorrelation.eventId());
        assertEquals(originalEvent.eventType(), eventWithCorrelation.eventType());
    }

    @Test
    void testGetMetadata() {
        // Arrange
        Map<String, Object> metadata = Map.of(
            "attempts", 5,
            "location", "datacenter",
            "validated", true
        );
        SecurityEvent event = SecurityEvent.createDetailed(
            SecurityEventType.LOGIN_FAILURE, SecuritySeverity.HIGH,
            SecurityEventSource.WEB_APPLICATION, "user", SecurityEntityType.USER,
            "Failed login", null, null, null, null, null, metadata, null
        );

        // Act & Assert
        assertEquals(5, event.getMetadata("attempts", Integer.class));
        assertEquals("datacenter", event.getMetadata("location", String.class));
        assertEquals(true, event.getMetadata("validated", Boolean.class));
        assertNull(event.getMetadata("nonexistent", String.class));

        // Test with default value
        assertEquals("default", event.getMetadata("nonexistent", String.class, "default"));
        assertEquals(5, event.getMetadata("attempts", Integer.class, 0));
    }

    @Test
    void testHasMetadata() {
        // Arrange
        Map<String, Object> metadata = Map.of("key1", "value1", "key2", 42);
        SecurityEvent event = SecurityEvent.createDetailed(
            SecurityEventType.ACCESS_DENIED, SecuritySeverity.MEDIUM,
            SecurityEventSource.API_GATEWAY, "api", SecurityEntityType.SERVICE,
            "Access denied", null, null, null, null, null, metadata, null
        );

        // Act & Assert
        assertTrue(event.hasMetadata("key1"));
        assertTrue(event.hasMetadata("key2"));
        assertFalse(event.hasMetadata("nonexistent"));
    }

    @Test
    void testGetAgeMillis() throws InterruptedException {
        // Arrange
        SecurityEvent event = SecurityEvent.create(
            SecurityEventType.LOGIN_SUCCESS, SecuritySeverity.INFO,
            SecurityEventSource.WEB_APPLICATION, "user", SecurityEntityType.USER,
            "Login successful"
        );

        // Wait a bit to ensure age > 0
        Thread.sleep(10);

        // Act
        long age = event.getAgeMillis();

        // Assert
        assertTrue(age > 0);
        assertTrue(age < 1000); // Should be less than 1 second
    }

    @Test
    void testIsStale() {
        // Arrange
        SecurityEvent recentEvent = SecurityEvent.create(
            SecurityEventType.LOGIN_SUCCESS, SecuritySeverity.INFO,
            SecurityEventSource.WEB_APPLICATION, "user", SecurityEntityType.USER,
            "Recent login"
        );

        // Create an old event by creating one with past timestamp
        SecurityEvent oldEvent = new SecurityEvent(
            "old-event",
            SecurityEventType.LOGIN_FAILURE,
            SecuritySeverity.MEDIUM,
            SecurityEventSource.WEB_APPLICATION,
            "user",
            SecurityEntityType.USER,
            Instant.now().minusSeconds(3600), // 1 hour ago
            "Old login failure",
            null, null, null, null, null, Map.of(), null, null
        );

        // Act & Assert
        assertFalse(recentEvent.isStale(60000)); // 60 seconds threshold
        assertTrue(oldEvent.isStale(60000)); // Should be stale
    }

    @Test
    void testToSummary() {
        // Arrange
        SecurityEvent event = SecurityEvent.create(
            SecurityEventType.NETWORK_INTRUSION, SecuritySeverity.HIGH,
            SecurityEventSource.NETWORK_FIREWALL, "network", SecurityEntityType.NETWORK_SEGMENT,
            "Network intrusion detected"
        );

        // Act
        String summary = event.toSummary();

        // Assert
        assertNotNull(summary);
        assertTrue(summary.contains("SecurityEvent"));
        assertTrue(summary.contains(event.eventId()));
        assertTrue(summary.contains("NETWORK_INTRUSION"));
        assertTrue(summary.contains("HIGH"));
        assertTrue(summary.contains("network"));
        assertTrue(summary.contains("NETWORK_FIREWALL"));
    }

    @Test
    void testIsValid() {
        // Valid event
        SecurityEvent validEvent = SecurityEvent.create(
            SecurityEventType.LOGIN_SUCCESS, SecuritySeverity.INFO,
            SecurityEventSource.WEB_APPLICATION, "user123", SecurityEntityType.USER,
            "Valid login event"
        );
        assertTrue(validEvent.isValid());

        // Invalid event with null required fields
        SecurityEvent invalidEvent = new SecurityEvent(
            null, // null eventId
            SecurityEventType.LOGIN_FAILURE,
            SecuritySeverity.HIGH,
            SecurityEventSource.WEB_APPLICATION,
            "user",
            SecurityEntityType.USER,
            Instant.now(),
            "Invalid event",
            null, null, null, null, null, Map.of(), null, null
        );
        assertFalse(invalidEvent.isValid());

        // Invalid event with blank required fields
        SecurityEvent blankEvent = new SecurityEvent(
            "", // blank eventId
            SecurityEventType.LOGIN_FAILURE,
            SecuritySeverity.HIGH,
            SecurityEventSource.WEB_APPLICATION,
            "", // blank entityId
            SecurityEntityType.USER,
            Instant.now(),
            "", // blank description
            null, null, null, null, null, Map.of(), null, null
        );
        assertFalse(blankEvent.isValid());
    }
}