package ai.twodot.platform.agents.securitymonitor.integration;

import ai.twodot.platform.agents.securitymonitor.SecurityMonitorAgentApplication;
import ai.twodot.platform.agents.securitymonitor.agent.SecurityMonitorAgent;
import ai.twodot.platform.agents.securitymonitor.security.SecurityEvent;
import ai.twodot.platform.agents.securitymonitor.security.ThreatAnalysisResult;
import ai.twodot.platform.agents.securitymonitor.security.SecurityEventType;
import ai.twodot.platform.agents.securitymonitor.security.SecuritySeverity;
import ai.twodot.platform.agents.securitymonitor.security.SecurityEventSource;
import ai.twodot.platform.agents.securitymonitor.security.SecurityEntityType;
import ai.twodot.platform.agents.securitymonitor.security.SecurityLocation;
import ai.twodot.platform.agents.securitymonitor.security.SecurityEventProcessor.SecurityEventResult;
import ai.twodot.platform.agents.securitymonitor.integration.AgentIntegrationService;
import ai.twodot.platform.agents.securitymonitor.integration.CommunicationBrokerClient;
import ai.twodot.platform.agents.securitymonitor.integration.DiscoveryRegistryClient;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Integration tests for Security Monitor Agent with CBA and DRA.
 * Tests the complete A2A communication flow across all three agents.
 */
@ExtendWith(MockitoExtension.class)
@SpringBootTest(classes = SecurityMonitorAgentApplication.class)
@ActiveProfiles("test")
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@SpringJUnitConfig
class AgentIntegrationTest {

    @Autowired
    private SecurityMonitorAgent securityAgent;

    @Autowired
    private AgentIntegrationService integrationService;

    @MockBean
    private CommunicationBrokerClient cbaClient;

    @MockBean
    private DiscoveryRegistryClient draClient;

    @MockBean
    private SecurityEventStreamer eventStreamer;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    void setUp() {
        // Mock CBA client responses
        when(cbaClient.isConnected()).thenReturn(true);
        when(cbaClient.sendMessage(anyString(), anyString(), any())).thenReturn(
            CompletableFuture.completedFuture(createSuccessResponse())
        );
        when(cbaClient.sendBroadcastMessage(anyString(), any())).thenReturn(
            CompletableFuture.completedFuture(createSuccessResponse())
        );
        when(cbaClient.sendHeartbeat()).thenReturn(
            CompletableFuture.completedFuture(createSuccessResponse())
        );

        // Mock DRA client responses
        when(draClient.isRegistered()).thenReturn(true);
        when(draClient.registerSecurityMonitorServices()).thenReturn(
            CompletableFuture.completedFuture(createServiceRegistrationResponse())
        );

        // Mock event streamer responses
        when(eventStreamer.streamSecurityEvent(any(), any())).thenReturn(
            CompletableFuture.completedFuture(createStreamingResponse())
        );
        when(eventStreamer.getMetrics()).thenReturn(
            new SecurityEventStreamer.StreamingMetrics(100, 10, 0, 0, 5, 2, true)
        );
    }

    @Test
    void testFullSecurityEventProcessingFlow() throws Exception {
        // Arrange
        SecurityEvent testEvent = createTestSecurityEvent();

        // Act
        CompletableFuture<SecurityEventResult> result = 
            securityAgent.processSecurityEvent(testEvent);

        // Assert
        SecurityEventResult eventResult = result.get(5, TimeUnit.SECONDS);
        assertNotNull(eventResult);
        assertTrue(eventResult.success());
        assertEquals(testEvent.eventId(), eventResult.eventId());

        // Verify CBA communication
        verify(cbaClient, atLeastOnce()).sendBroadcastMessage(eq("security_alert"), any());
        
        // Verify event streaming
        verify(eventStreamer, atLeastOnce()).streamSecurityEvent(eq(testEvent), any());
    }

    @Test
    void testA2AMessageProcessing() throws Exception {
        // Arrange
        AgentIntegrationService.A2AMessage incomingThreatAlert = createIncomingThreatAlert();

        // Act
        CompletableFuture<AgentIntegrationService.A2AMessageResponse> response = 
            integrationService.processIncomingThreatAlert(incomingThreatAlert);

        // Assert
        AgentIntegrationService.A2AMessageResponse result = response.get(5, TimeUnit.SECONDS);
        assertNotNull(result);
        assertTrue(result.success());
        assertEquals("Threat alert processed successfully", result.message());

        // Verify event streaming was triggered
        verify(eventStreamer, times(1)).streamSecurityEvent(any(), isNull());
    }

    @Test
    void testServiceDiscoveryProtocol() throws Exception {
        // Arrange
        AgentIntegrationService.A2AMessage discoveryRequest = createServiceDiscoveryRequest();

        // Act
        CompletableFuture<AgentIntegrationService.A2AMessageResponse> response = 
            integrationService.processServiceDiscoveryRequest(discoveryRequest);

        // Assert
        AgentIntegrationService.A2AMessageResponse result = response.get(5, TimeUnit.SECONDS);
        assertNotNull(result);
        assertTrue(result.success());
        
        // Verify response contains our capabilities
        assertNotNull(result.data());
        assertTrue(result.data() instanceof AgentIntegrationService.ServiceDiscoveryResponse);
        
        AgentIntegrationService.ServiceDiscoveryResponse discoveryResponse = 
            (AgentIntegrationService.ServiceDiscoveryResponse) result.data();
        assertTrue(discoveryResponse.capabilities().contains("threat_detection"));
        assertTrue(discoveryResponse.capabilities().contains("behavioral_analysis"));
    }

    @Test
    void testIntelligenceSharing() throws Exception {
        // Arrange
        AgentIntegrationService.A2AMessage intelligenceMessage = createIntelligenceShareMessage();

        // Act
        CompletableFuture<AgentIntegrationService.A2AMessageResponse> response = 
            integrationService.processIntelligenceShare(intelligenceMessage);

        // Assert
        AgentIntegrationService.A2AMessageResponse result = response.get(5, TimeUnit.SECONDS);
        assertNotNull(result);
        assertTrue(result.success());
        assertEquals("Intelligence shared successfully", result.message());

        // Verify intelligence was streamed
        verify(eventStreamer, times(1)).streamThreatIntelligence(any());
    }

    @Test
    void testSecurityAssistanceRequest() throws Exception {
        // Arrange
        SecurityEvent criticalEvent = createCriticalSecurityEvent();

        // Act
        CompletableFuture<AgentIntegrationService.A2AMessageResponse> response = 
            integrationService.requestSecurityAssistance(criticalEvent, "threat_analysis");

        // Assert
        AgentIntegrationService.A2AMessageResponse result = response.get(5, TimeUnit.SECONDS);
        assertNotNull(result);
        assertTrue(result.success());
        assertEquals("Assistance request sent", result.message());

        // Verify CBA was called for broadcast
        verify(cbaClient, times(1)).sendBroadcastMessage(eq("security_assistance_request"), any());
    }

    @Test
    void testIncidentCoordination() throws Exception {
        // Arrange
        SecurityEvent incidentEvent = createIncidentSecurityEvent();
        ThreatAnalysisResult analysis = createThreatAnalysisResult();

        // Act
        CompletableFuture<AgentIntegrationService.A2AMessageResponse> response = 
            integrationService.coordinateIncidentResponse(incidentEvent, analysis);

        // Assert
        AgentIntegrationService.A2AMessageResponse result = response.get(5, TimeUnit.SECONDS);
        assertNotNull(result);
        assertTrue(result.success());
        assertEquals("Incident coordination initiated", result.message());

        // Verify incident coordination message was sent
        verify(cbaClient, times(1)).sendBroadcastMessage(eq("incident_coordination"), any());
    }

    @Test
    void testCBARegistrationAndHeartbeat() throws Exception {
        // Act
        integrationService.sendAgentHeartbeat().get(5, TimeUnit.SECONDS);

        // Assert
        verify(cbaClient, times(1)).sendHeartbeat();
    }

    @Test
    void testDRAServiceRegistration() throws Exception {
        // Act
        DiscoveryRegistryClient.ServiceRegistrationResponse response = 
            draClient.registerSecurityMonitorServices().get(5, TimeUnit.SECONDS);

        // Assert
        assertNotNull(response);
        assertTrue(response.success());
        verify(draClient, times(1)).registerSecurityMonitorServices();
    }

    @Test
    void testConcurrentA2AMessageProcessing() throws Exception {
        // Arrange
        List<CompletableFuture<AgentIntegrationService.A2AMessageResponse>> futures = List.of(
            integrationService.processIncomingThreatAlert(createIncomingThreatAlert()),
            integrationService.processServiceDiscoveryRequest(createServiceDiscoveryRequest()),
            integrationService.processIntelligenceShare(createIntelligenceShareMessage())
        );

        // Act & Assert
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
            .get(10, TimeUnit.SECONDS);

        for (CompletableFuture<AgentIntegrationService.A2AMessageResponse> future : futures) {
            AgentIntegrationService.A2AMessageResponse result = future.get();
            assertNotNull(result);
            assertTrue(result.success());
        }
    }

    @Test
    void testErrorHandlingInA2AProcessing() throws Exception {
        // Arrange
        AgentIntegrationService.A2AMessage malformedMessage = new AgentIntegrationService.A2AMessage(
            UUID.randomUUID().toString(),
            "TEST-AGENT",
            "SMA-003",
            "malformed_request",
            "request",
            "HIGH",
            "invalid_payload", // Invalid payload type
            Instant.now(),
            Map.of()
        );

        // Act
        CompletableFuture<AgentIntegrationService.A2AMessageResponse> response = 
            integrationService.processIncomingThreatAlert(malformedMessage);

        // Assert
        AgentIntegrationService.A2AMessageResponse result = response.get(5, TimeUnit.SECONDS);
        assertNotNull(result);
        assertFalse(result.success());
        assertNotNull(result.error());
        assertTrue(result.error().contains("Failed to process threat alert"));
    }

    @Test
    void testIntegrationMetrics() {
        // Act
        AgentIntegrationService.IntegrationMetrics metrics = integrationService.getMetrics();

        // Assert
        assertNotNull(metrics);
        assertTrue(metrics.cbaConnected());
        assertTrue(metrics.draRegistered());
        assertTrue(metrics.streamingActive());
        assertTrue(metrics.initialized());
    }

    // Helper methods for creating test data

    private SecurityEvent createTestSecurityEvent() {
        return SecurityEvent.createDetailed(
            SecurityEventType.MALWARE_DETECTED,
            SecuritySeverity.HIGH,
            SecurityEventSource.ENDPOINT_PROTECTION,
            "test-endpoint-001",
            SecurityEntityType.DEVICE,
            "Test malware detection",
            "*************",
            null,
            null,
            null,
            null,
            Map.of("endpoint_id", "test-endpoint-001"),
            "Test malware detected on endpoint"
        );
    }

    private SecurityEvent createCriticalSecurityEvent() {
        return SecurityEvent.createDetailed(
            SecurityEventType.NETWORK_INTRUSION,
            SecuritySeverity.CRITICAL,
            SecurityEventSource.NETWORK_FIREWALL,
            "network-segment-001",
            SecurityEntityType.NETWORK_SEGMENT,
            "Critical intrusion attempt",
            "*************",
            "********",
            null,
            null,
            null,
            Map.of("source_ip", "*************"),
            "Unauthorized access attempt detected"
        );
    }

    private SecurityEvent createIncidentSecurityEvent() {
        return SecurityEvent.createDetailed(
            SecurityEventType.DATA_BREACH,
            SecuritySeverity.CRITICAL,
            SecurityEventSource.DATABASE,
            "prod-db-001",
            SecurityEntityType.DATABASE,
            "Data breach incident",
            "*************",
            "********",
            null,
            null,
            null,
            Map.of("database_id", "prod-db-001"),
            "Unauthorized data access detected"
        );
    }

    private ThreatAnalysisResult createThreatAnalysisResult() {
        return new ThreatAnalysisResult(
            "advanced_persistent_threat",
            true,
            "CRITICAL",
            0.95,
            "AI_ML_ANALYSIS",
            "test-event-123",
            Map.of("indicators", List.of("lateral_movement", "data_exfiltration")),
            true,
            Instant.now(),
            150L
        );
    }

    private AgentIntegrationService.A2AMessage createIncomingThreatAlert() {
        AgentIntegrationService.ThreatAlertMessage threat = new AgentIntegrationService.ThreatAlertMessage(
            UUID.randomUUID().toString(),
            "malware",
            "HIGH",
            0.85,
            "Malware detected by external agent",
            List.of("suspicious_file", "network_communication"),
            Instant.now()
        );

        return new AgentIntegrationService.A2AMessage(
            UUID.randomUUID().toString(),
            "EXTERNAL-AGENT-001",
            "SMA-003",
            "threat_alert",
            "notification",
            "HIGH",
            threat,
            Instant.now(),
            Map.of("protocol", "a2a")
        );
    }

    private AgentIntegrationService.A2AMessage createServiceDiscoveryRequest() {
        AgentIntegrationService.ServiceDiscoveryRequest request = new AgentIntegrationService.ServiceDiscoveryRequest(
            List.of("threat_detection", "behavioral_analysis", "unknown_capability"),
            Map.of("min_confidence", 0.8, "max_response_time", 100),
            "security_analysis"
        );

        return new AgentIntegrationService.A2AMessage(
            UUID.randomUUID().toString(),
            "REQUESTING-AGENT-001",
            "SMA-003",
            "service_discovery",
            "request",
            "NORMAL",
            request,
            Instant.now(),
            Map.of("protocol", "a2a")
        );
    }

    private AgentIntegrationService.A2AMessage createIntelligenceShareMessage() {
        AgentIntegrationService.IntelligenceShareMessage intel = new AgentIntegrationService.IntelligenceShareMessage(
            UUID.randomUUID().toString(),
            "threat_signature",
            Map.of(
                "signature", "malware_family_x",
                "hash", "abc123def456",
                "behavior", "file_encryption"
            ),
            0.9,
            "INTEL-AGENT-001",
            Instant.now()
        );

        return new AgentIntegrationService.A2AMessage(
            UUID.randomUUID().toString(),
            "INTEL-AGENT-001",
            "SMA-003",
            "intelligence_share",
            "notification",
            "HIGH",
            intel,
            Instant.now(),
            Map.of("protocol", "a2a")
        );
    }

    private CommunicationBrokerClient.MessageResponse createSuccessResponse() {
        return new CommunicationBrokerClient.MessageResponse(
            UUID.randomUUID().toString(),
            "success",
            true,
            Map.of("status", "processed"),
            null,
            "50ms",
            Instant.now()
        );
    }

    private DiscoveryRegistryClient.ServiceRegistrationResponse createServiceRegistrationResponse() {
        return DiscoveryRegistryClient.ServiceRegistrationResponse.success("Services registered successfully");
    }

    private SecurityEventStreamer.StreamingResponse createStreamingResponse() {
        return SecurityEventStreamer.StreamingResponse.queued();
    }
}