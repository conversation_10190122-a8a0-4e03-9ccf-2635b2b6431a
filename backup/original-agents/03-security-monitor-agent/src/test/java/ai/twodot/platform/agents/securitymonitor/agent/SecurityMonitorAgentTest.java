package ai.twodot.platform.agents.securitymonitor.agent;

import ai.twodot.platform.agents.securitymonitor.security.ThreatAnalysisResult;
import ai.twodot.platform.agents.securitymonitor.security.BehavioralAnalysisResult;
import ai.twodot.platform.agents.securitymonitor.security.IncidentResponseService;
import ai.twodot.platform.agents.securitymonitor.security.IncidentResponseService.IncidentResponseResult;
import ai.twodot.platform.agents.securitymonitor.security.SecurityEventProcessor.SecurityEventResult;
import ai.twodot.platform.agents.securitymonitor.security.SecurityResponse;
import ai.twodot.platform.agents.securitymonitor.security.SecurityResponseStatus;
import ai.twodot.platform.agents.securitymonitor.security.SecurityEvent;
import ai.twodot.platform.agents.securitymonitor.security.SecurityEventType;
import ai.twodot.platform.agents.securitymonitor.security.SecuritySeverity;
import ai.twodot.platform.agents.securitymonitor.security.SecurityEventSource;
import ai.twodot.platform.agents.securitymonitor.security.SecurityEntityType;
import ai.twodot.platform.agents.securitymonitor.security.SecurityLocation;
import ai.twodot.platform.agents.securitymonitor.monitoring.SecurityMetricsCollector;
import ai.twodot.platform.agents.securitymonitor.intelligence.ThreatIntelligenceService;
import ai.twodot.platform.agents.securitymonitor.agent.SecurityMonitorAgent.AgentHealthStatus;
import ai.twodot.platform.agents.securitymonitor.security.BehavioralAnalysisService;
import ai.twodot.platform.agents.securitymonitor.security.SecurityPolicyService;
import ai.twodot.platform.agents.securitymonitor.security.SecurityEventProcessor;
import ai.twodot.platform.agents.securitymonitor.ai.ThreatDetectionService;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import ai.twodot.platform.agents.securitymonitor.security.SecurityResponseType;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SecurityMonitorAgentTest {

    @Mock private ThreatDetectionService threatDetectionService;
    @Mock private BehavioralAnalysisService behavioralAnalysisService;
    @Mock private IncidentResponseService incidentResponseService;
    @Mock private SecurityPolicyService securityPolicyService;
    @Mock private SecurityEventProcessor securityEventProcessor;
    @Mock private ThreatIntelligenceService threatIntelligenceService;
    @Mock private SecurityMetricsCollector metricsCollector;

    private SecurityMonitorAgent securityMonitorAgent;

    @BeforeEach
    void setUp() {
        securityMonitorAgent = new SecurityMonitorAgent(
            threatDetectionService,
            behavioralAnalysisService,
            incidentResponseService,
            securityPolicyService,
            securityEventProcessor,
            threatIntelligenceService,
            metricsCollector
        );
    }

    @Test
    void testInitialize() {
        // Mock the initialization of services
        when(threatDetectionService.getStatus()).thenReturn("HEALTHY");
        when(behavioralAnalysisService.getStatus()).thenReturn("HEALTHY");
        when(incidentResponseService.getStatus()).thenReturn("HEALTHY");
        when(securityPolicyService.getStatus()).thenReturn("HEALTHY");
        when(securityEventProcessor.getStatus()).thenReturn("STREAMING");
        when(threatIntelligenceService.getStatus()).thenReturn("HEALTHY");

        // Test initialization
        securityMonitorAgent.initialize();

        // Verify that all services were initialized
        verify(threatDetectionService).initializeModels();
        verify(behavioralAnalysisService).initializeProfiles();
        verify(incidentResponseService).loadResponsePlaybooks();
        verify(securityPolicyService).loadSecurityPolicies();
        verify(securityEventProcessor).startEventStreaming();
        verify(threatIntelligenceService).initializeThreatFeeds();
        verify(metricsCollector).startCollection();

        assertTrue(securityMonitorAgent.isRunning());
    }

    @Test
    void testProcessSecurityEvent_WithThreatDetected() {
        // Arrange
        SecurityEvent event = createTestSecurityEvent();
        String eventId = event.eventId();
        ThreatAnalysisResult threatAnalysis = createThreatAnalysisResult(eventId, true, 0.9);
        BehavioralAnalysisResult behavioralResult = createBehavioralResult(event.entityId());
        SecurityResponse securityResponse = createSecurityResponse(eventId, true, false);
        IncidentResponseResult incidentResult = createIncidentResult(eventId);

        // Mock service responses
        when(threatDetectionService.analyzeThreat(event)).thenReturn(CompletableFuture.completedFuture(threatAnalysis));
        when(behavioralAnalysisService.analyzeEntity(event.entityId(), event)).thenReturn(CompletableFuture.completedFuture(behavioralResult));
        when(securityPolicyService.determineResponse(event, threatAnalysis, behavioralResult)).thenReturn(CompletableFuture.completedFuture(securityResponse));
        when(incidentResponseService.respondToThreat(event, threatAnalysis, securityResponse)).thenReturn(CompletableFuture.completedFuture(incidentResult));

        // Act
        CompletableFuture<SecurityEventResult> result = securityMonitorAgent.processSecurityEvent(event);
        SecurityEventResult actualResult = result.join();

        // Assert
        assertNotNull(actualResult);
        assertTrue(actualResult.isSuccess());
        assertEquals(event.eventId(), actualResult.getEventId());
        
        // Verify service interactions
        verify(threatDetectionService).analyzeThreat(event);
        verify(behavioralAnalysisService).analyzeEntity(event.entityId(), event);
        verify(securityPolicyService).determineResponse(event, threatAnalysis, behavioralResult);
        verify(incidentResponseService).respondToThreat(event, threatAnalysis, securityResponse);
        verify(metricsCollector).recordEvent(event);
        verify(metricsCollector).recordThreatAnalysis(threatAnalysis);
        verify(metricsCollector).recordSecurityResponse(securityResponse);

        assertEquals(1, securityMonitorAgent.getEventsProcessed());
        assertEquals(1, securityMonitorAgent.getThreatsDetected());
        assertEquals(1, securityMonitorAgent.getIncidentsHandled());
    }

    @Test
    void testProcessSecurityEvent_WithoutThreatDetected() {
        // Arrange
        SecurityEvent event = createTestSecurityEvent();
        ThreatAnalysisResult threatAnalysis = createThreatAnalysisResult(event.eventId(), false, 0.1);
        SecurityResponse securityResponse = createSecurityResponse(event.eventId(), false, false);
        // Mock service responses
        when(threatDetectionService.analyzeThreat(event)).thenReturn(CompletableFuture.completedFuture(threatAnalysis));
        when(securityPolicyService.determineResponse(eq(event), eq(threatAnalysis), isNull())).thenReturn(CompletableFuture.completedFuture(securityResponse));

        // Act
        CompletableFuture<SecurityEventResult> result = securityMonitorAgent.processSecurityEvent(event);
        SecurityEventResult actualResult = result.join();

        // Assert
        assertNotNull(actualResult);
        assertTrue(actualResult.isSuccess());
        
        // Verify that incident response was not called since no threat detected
        verify(incidentResponseService, never()).respondToThreat(any(), any(), any());
        verify(behavioralAnalysisService, never()).analyzeEntity(any(), any());

        assertEquals(1, securityMonitorAgent.getEventsProcessed());
        assertEquals(0, securityMonitorAgent.getThreatsDetected());
        assertEquals(0, securityMonitorAgent.getIncidentsHandled());
    }

    @Test
    void testProcessSecurityEvent_WithException() {
        // Arrange
        SecurityEvent event = createTestSecurityEvent();
        when(threatDetectionService.analyzeThreat(event)).thenReturn(CompletableFuture.failedFuture(new RuntimeException("Processing failed")));

        // Act
        CompletableFuture<SecurityEventResult> result = securityMonitorAgent.processSecurityEvent(event);
        SecurityEventResult actualResult = result.join();

        // Assert
        assertNotNull(actualResult);
        assertFalse(actualResult.isSuccess());
        assertTrue(actualResult.getMessage().contains("Processing failed"));

        assertEquals(1, securityMonitorAgent.getEventsProcessed());
        assertEquals(0, securityMonitorAgent.getThreatsDetected());
    }

    @Test
    void testGetHealthStatus() {
        // Arrange
        when(threatDetectionService.getStatus()).thenReturn("HEALTHY");
        when(behavioralAnalysisService.getStatus()).thenReturn("HEALTHY");
        when(incidentResponseService.getStatus()).thenReturn("HEALTHY");
        when(securityPolicyService.getStatus()).thenReturn("HEALTHY");
        when(securityEventProcessor.getStatus()).thenReturn("STREAMING");
        when(threatIntelligenceService.getStatus()).thenReturn("HEALTHY");

        securityMonitorAgent.initialize();

        // Act
        AgentHealthStatus healthStatus = securityMonitorAgent.getHealthStatus();

        // Assert
        assertNotNull(healthStatus);
        assertEquals("SMA-003", healthStatus.agentId());
        assertEquals("Security Monitor Agent", healthStatus.agentName());
        assertEquals("1.0.0", healthStatus.version());
        assertEquals("HEALTHY", healthStatus.status());
        assertNotNull(healthStatus.uptime());
        assertNotNull(healthStatus.componentStatus());
        assertEquals(6, healthStatus.componentStatus().size());
    }

    @Test
    void testGetSecurityMetrics() {
        // Arrange
        SecurityMetricsCollector.SecurityMetrics mockMetrics = new SecurityMetricsCollector.SecurityMetrics(
            100L, 5L, true, Instant.now()
        );
        when(metricsCollector.getCurrentMetrics()).thenReturn(mockMetrics);

        // Act
        SecurityMetricsCollector.SecurityMetrics metrics = securityMonitorAgent.getSecurityMetrics();

        // Assert
        assertNotNull(metrics);
        assertEquals(100L, metrics.totalEvents());
        assertEquals(5L, metrics.totalThreats());
        assertTrue(metrics.collecting());
    }

    @Test
    void testShutdown() {
        // Arrange
        securityMonitorAgent.initialize();

        // Act
        securityMonitorAgent.shutdown();

        // Assert
        assertFalse(securityMonitorAgent.isRunning());
        verify(securityEventProcessor).stopEventStreaming();
        verify(metricsCollector).stopCollection();
    }

    // Helper methods to create test objects

    private SecurityEvent createTestSecurityEvent() {
        return SecurityEvent.createDetailed(
            SecurityEventType.LOGIN_FAILURE,
            SecuritySeverity.HIGH,
            SecurityEventSource.WEB_APPLICATION,
            "user123",
            SecurityEntityType.USER,
            "Failed login attempt",
            "*************",
            "********",
            "Mozilla/5.0",
            "session123",
            SecurityLocation.fromIP("*************"),
            Map.of("attempts", 3),
            "raw login data"
        );
    }

    private ThreatAnalysisResult createThreatAnalysisResult(String eventId, boolean threatDetected, double confidence) {
        return new ThreatAnalysisResult(
            eventId,
            threatDetected,
            threatDetected ? "BRUTE_FORCE_ATTACK" : "NORMAL",
            confidence,
            confidence > 0.7 ? "HIGH" : "LOW",
            "TEST_METHOD",
            Map.of("test", "data"),
            threatDetected && confidence > 0.7,
            Instant.now(),
            50L
        );
    }

    private BehavioralAnalysisResult createBehavioralResult(String entityId) {
        return new BehavioralAnalysisResult(
            java.util.UUID.randomUUID().toString(),
            entityId,
            SecurityEntityType.USER,
            true,
            0.8,
            List.of("unusual_login_time"),
            Map.of("login_frequency", 2.5),
            "user_baseline_v1",
            Instant.now(),
            3600L
        );
    }

    private SecurityResponse createSecurityResponse(String eventId, boolean requiresResponse, boolean requiresUpdate) {
        if (requiresResponse) {
            return SecurityResponse.automated(eventId, "BLOCK_IP", "Automated response triggered by test.");
        } else {
            return new SecurityResponse(
                    java.util.UUID.randomUUID().toString(),
                    eventId,
                    SecurityResponseType.MANUAL,
                    "MONITOR",
                    "Monitoring only, no automated response.",
                    false,
                    SecurityResponseStatus.COMPLETED,
                    List.of(),
                    Map.of(),
                    Instant.now(),
                    Instant.now(),
                    "test-system",
                    0.0
            );
        }
    }

    private IncidentResponseResult createIncidentResult(String eventId) {
        return new IncidentResponseResult(
            eventId, "AUTOMATED_RESPONSE", true,
            Map.of("containment", "ip_blocked"), Instant.now(), 100L
        );
    }
}