# Test configuration for Security Monitor Agent

# Server configuration
server.port=8080
management.endpoints.web.exposure.include=health,metrics,info
management.endpoint.health.show-details=always

# Python ML service configuration
sma.python.service.url=http://localhost:8081
sma.python.service.timeout=5000

# Logging configuration
logging.level.ai.twodot.platform.agents.securitymonitor=INFO
logging.level.org.springframework=WARN
logging.level.org.apache.http=WARN

# Database configuration (H2 for testing)
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop

# Redis configuration (embedded for testing)
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.database=1

# Thread configuration
spring.threads.virtual.enabled=true

# Security configuration
sma.security.enabled=false
sma.auth.jwt.secret=test-secret-key-for-testing-only
sma.auth.jwt.expiration=3600

# Metrics configuration
sma.metrics.enabled=true
sma.metrics.collection.interval=30

# AI configuration
sma.ai.enabled=true
sma.ai.fallback.enabled=true
sma.ai.timeout=1000

# Testing specific
spring.test.database.replace=none