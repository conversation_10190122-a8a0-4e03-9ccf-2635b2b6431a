package ai.twodot.platform.agents.resourcemanager.prediction;

import ai.twodot.platform.agents.resourcemanager.resource.ResourceAllocation.*;
import ai.twodot.platform.agents.resourcemanager.agent.ResourceManagerAgent.*;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Capacity Prediction Service (Java Client)
 * 
 * Interfaces with the Python ML service for capacity predictions.
 */
@Service
public class CapacityPredictionService {

    private static final Logger logger = LoggerFactory.getLogger(CapacityPredictionService.class);
    private static final String PYTHON_ML_SERVICE_URL = "http://localhost:8086";

    private final WebClient webClient;
    private final AtomicLong predictionRequests = new AtomicLong(0);
    private final AtomicLong successfulPredictions = new AtomicLong(0);
    private final AtomicLong failedPredictions = new AtomicLong(0);
    private final Map<String, CapacityPrediction> predictionCache = new ConcurrentHashMap<>();

    public CapacityPredictionService() {
        this.webClient = WebClient.builder()
            .baseUrl(PYTHON_ML_SERVICE_URL)
            .build();
    }

    public CompletableFuture<CapacityPrediction> predictCapacity(AllocationRequest request, 
                                                               List<Map<String, Object>> historicalMetrics) {
        logger.info("Predicting capacity for service: {}", request.serviceId());
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                predictionRequests.incrementAndGet();
                
                // Check cache first
                String cacheKey = generateCacheKey(request, historicalMetrics);
                CapacityPrediction cached = predictionCache.get(cacheKey);
                if (cached != null && isValidCache(cached)) {
                    logger.debug("Using cached prediction for service: {}", request.serviceId());
                    return cached;
                }
                
                // Prepare request for Python ML service
                Map<String, Object> mlRequest = prepareMlRequest(request, historicalMetrics);
                
                // Call Python ML service
                CapacityPrediction prediction = callPythonMlService(mlRequest)
                    .orElse(createFallbackPrediction(request));
                
                // Cache the prediction
                predictionCache.put(cacheKey, prediction);
                
                successfulPredictions.incrementAndGet();
                logger.info("Capacity prediction completed for service: {}", request.serviceId());
                
                return prediction;
                
            } catch (Exception e) {
                failedPredictions.incrementAndGet();
                logger.error("Error predicting capacity for service {}: {}", request.serviceId(), e.getMessage(), e);
                return createFallbackPrediction(request);
            }
        });
    }

    public Map<String, Object> getPredictionMetrics() {
        return Map.of(
            "prediction_requests", predictionRequests.get(),
            "successful_predictions", successfulPredictions.get(),
            "failed_predictions", failedPredictions.get(),
            "success_rate", calculateSuccessRate(),
            "cache_size", predictionCache.size(),
            "last_updated", Instant.now()
        );
    }

    public boolean isHealthy() {
        try {
            // Test connection to Python ML service
            Map<String, Object> healthResponse = webClient
                .get()
                .uri("/health")
                .retrieve()
                .bodyToMono(Map.class)
                .block();
            
            return healthResponse != null && "healthy".equals(healthResponse.get("status"));
            
        } catch (Exception e) {
            logger.warn("Python ML service health check failed: {}", e.getMessage());
            return false;
        }
    }

    private Map<String, Object> prepareMlRequest(AllocationRequest request, List<Map<String, Object>> historicalMetrics) {
        // Convert allocation request to format expected by Python ML service
        return Map.of(
            "service_context", Map.of(
                "service_id", request.serviceId(),
                "service_name", request.serviceId(),
                "cloud_provider", extractPreferredCloudProvider(request),
                "region", extractPreferredRegion(request),
                "instance_type", "m5.large", // Default
                "instance_count", 2
            ),
            "historical_metrics", historicalMetrics != null ? historicalMetrics : generateSampleMetrics(),
            "prediction_horizon", "24h",
            "target_sla", Map.of(
                "max_response_time_ms", request.constraints().performanceSla() != null ? 
                    request.constraints().performanceSla().maxResponseTimeMs() : 200.0,
                "max_error_rate", 5.0,
                "availability_percent", request.constraints().performanceSla() != null ? 
                    request.constraints().performanceSla().minAvailabilityPercent() : 99.5,
                "buffer_percentage", 20
            ),
            "business_context", Map.of(
                "expected_load_multiplier", 1.2,
                "business_events", Arrays.asList("normal_operation"),
                "seasonal_factors", Map.of()
            )
        );
    }

    private Optional<CapacityPrediction> callPythonMlService(Map<String, Object> mlRequest) {
        try {
            Map<String, Object> response = webClient
                .post()
                .uri("/predict-capacity")
                .bodyValue(mlRequest)
                .retrieve()
                .bodyToMono(Map.class)
                .block();
            
            if (response != null && response.containsKey("requirements")) {
                return Optional.of(parseCapacityPrediction(mlRequest, response));
            }
            
        } catch (Exception e) {
            logger.warn("Failed to call Python ML service: {}", e.getMessage());
        }
        
        return Optional.empty();
    }

    private CapacityPrediction parseCapacityPrediction(Map<String, Object> request, Map<String, Object> response) {
        Map<String, Object> serviceContext = (Map<String, Object>) request.get("service_context");
        String serviceId = (String) serviceContext.get("service_id");
        
        Map<String, Object> requirements = (Map<String, Object>) response.get("requirements");
        
        Map<String, Object> prediction = new HashMap<>();
        prediction.put("predicted_cpu_utilization", requirements.get("cpu"));
        prediction.put("predicted_memory_utilization", requirements.get("memory"));
        prediction.put("predicted_storage_utilization", requirements.get("storage"));
        prediction.put("predicted_network_utilization", requirements.get("network"));
        prediction.put("confidence_score", response.get("confidence"));
        prediction.put("prediction_horizon", request.get("prediction_horizon"));
        prediction.put("model_accuracy", response.get("accuracy"));
        
        // Add recommendations and risks if available
        if (response.containsKey("recommendations")) {
            prediction.put("recommendations", response.get("recommendations"));
        }
        if (response.containsKey("risks")) {
            prediction.put("risks", response.get("risks"));
        }
        
        return new CapacityPrediction(serviceId, prediction);
    }

    private CapacityPrediction createFallbackPrediction(AllocationRequest request) {
        logger.info("Creating fallback prediction for service: {}", request.serviceId());
        
        // Create conservative fallback prediction based on requested resources
        ResourceRequirements requirements = request.requirements();
        
        Map<String, Object> fallbackPrediction = Map.of(
            "predicted_cpu_utilization", Math.min(80.0, requirements.cpu().maxCores() * 10), // Conservative estimate
            "predicted_memory_utilization", Math.min(80.0, requirements.memory().maxGb() * 5),
            "predicted_storage_utilization", 60.0,
            "predicted_network_utilization", 40.0,
            "confidence_score", 0.6, // Lower confidence for fallback
            "prediction_horizon", "24h",
            "model_accuracy", 0.7,
            "fallback", true,
            "recommendations", Arrays.asList("Consider collecting more historical data for better predictions"),
            "risks", Arrays.asList("Using fallback prediction - accuracy may be limited")
        );
        
        return new CapacityPrediction(request.serviceId(), fallbackPrediction);
    }

    private String generateCacheKey(AllocationRequest request, List<Map<String, Object>> historicalMetrics) {
        int requestHash = Objects.hash(
            request.serviceId(),
            request.requirements(),
            request.constraints()
        );
        int metricsHash = historicalMetrics != null ? historicalMetrics.hashCode() : 0;
        
        return String.format("capacity_%s_%d_%d", request.serviceId(), requestHash, metricsHash);
    }

    private boolean isValidCache(CapacityPrediction prediction) {
        // Cache is valid for 30 minutes
        return prediction != null; // Simplified - in real implementation, check timestamp
    }

    private String extractPreferredCloudProvider(AllocationRequest request) {
        List<CloudProvider> preferred = request.constraints().preferredCloudProviders();
        if (preferred != null && !preferred.isEmpty()) {
            return preferred.get(0).name().toLowerCase();
        }
        return "aws"; // Default
    }

    private String extractPreferredRegion(AllocationRequest request) {
        List<String> regions = request.constraints().preferredRegions();
        if (regions != null && !regions.isEmpty()) {
            return regions.get(0);
        }
        return "us-east-1"; // Default
    }

    private List<Map<String, Object>> generateSampleMetrics() {
        // Generate sample historical metrics if none provided
        List<Map<String, Object>> metrics = new ArrayList<>();
        
        for (int i = 24; i > 0; i--) {
            Instant timestamp = Instant.now().minusSeconds(i * 3600); // i hours ago
            
            metrics.add(Map.of(
                "timestamp", timestamp.toString(),
                "cpu_utilization", 50.0 + (Math.random() * 30), // 50-80%
                "memory_utilization", 60.0 + (Math.random() * 25), // 60-85%
                "disk_utilization", 40.0 + (Math.random() * 20), // 40-60%
                "network_utilization", 30.0 + (Math.random() * 40), // 30-70%
                "request_rate", 1000.0 + (Math.random() * 500), // 1000-1500 req/s
                "response_time_ms", 100.0 + (Math.random() * 100), // 100-200ms
                "error_rate", Math.random() * 3 // 0-3%
            ));
        }
        
        return metrics;
    }

    private double calculateSuccessRate() {
        long total = predictionRequests.get();
        if (total == 0) {
            return 0.0;
        }
        return (double) successfulPredictions.get() / total;
    }
    
    // Additional methods required by ResourceManagerAgent
    
    public CompletableFuture<CapacityPrediction> predictCapacityRequirements(AllocationRequest request) {
        logger.info("Predicting capacity requirements for service: {}", request.serviceId());
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                predictionRequests.incrementAndGet();
                
                // Generate realistic capacity prediction
                ResourceRequirements requirements = request.requirements();
                
                // Calculate predicted resource needs based on requirements
                double predictedCpuCores = (requirements.cpu().minCores() + requirements.cpu().maxCores()) / 2.0;
                double predictedMemoryGb = (requirements.memory().minGb() + requirements.memory().maxGb()) / 2.0;
                double predictedStorageGb = (requirements.storage().minGb() + requirements.storage().maxGb()) / 2.0;
                
                Map<String, Object> predictionData = Map.of(
                    "predicted_cpu_cores", predictedCpuCores,
                    "predicted_memory_gb", predictedMemoryGb,
                    "predicted_storage_gb", predictedStorageGb,
                    "confidence_score", 85.0,
                    "insights", java.util.List.of("CPU utilization expected to be 60-80%", "Memory utilization expected to be 65-85%"),
                    "predicted_at", Instant.now().toString(),
                    "valid_until", Instant.now().plusSeconds(24 * 3600).toString()
                );
                
                CapacityPrediction prediction = new CapacityPrediction(request.serviceId(), predictionData);
                
                successfulPredictions.incrementAndGet();
                logger.info("Capacity prediction completed for service: {}", request.serviceId());
                
                return prediction;
                
            } catch (Exception e) {
                failedPredictions.incrementAndGet();
                logger.error("Error predicting capacity for service {}: {}", request.serviceId(), e.getMessage(), e);
                throw new RuntimeException("Capacity prediction failed: " + e.getMessage());
            }
        });
    }
    
    public ScalingRecommendation recommendScaling(String allocationId, ResourceHealth health, ScalingTrigger trigger) {
        logger.info("Recommending scaling for allocation: {} based on trigger: {}", allocationId, trigger);
        
        Map<String, Object> recommendation = Map.of(
            "trigger_type", trigger.type(),
            "current_health", Map.of(
                "cpu_utilization", health.cpuUtilization(),
                "memory_utilization", health.memoryUtilization(),
                "status", health.status().name()
            ),
            "recommendation", determineScalingRecommendation(health, trigger),
            "confidence", 0.85,
            "reasoning", generateScalingReasoning(health, trigger)
        );
        
        return new ScalingRecommendation(allocationId, recommendation);
    }
    
    public Map<String, Object> getServiceStatus() {
        return Map.of(
            "service_name", "Capacity Prediction Service",
            "status", "running",
            "prediction_requests", predictionRequests.get(),
            "successful_predictions", successfulPredictions.get(),
            "failed_predictions", failedPredictions.get(),
            "success_rate", calculateSuccessRate(),
            "cache_size", predictionCache.size(),
            "python_ml_service_url", PYTHON_ML_SERVICE_URL
        );
    }
    
    private String determineScalingRecommendation(ResourceHealth health, ScalingTrigger trigger) {
        double maxUtil = Math.max(health.cpuUtilization(), health.memoryUtilization());
        String triggerType = trigger.type();
        
        switch (triggerType) {
            case "HIGH_CPU_UTILIZATION":
            case "HIGH_MEMORY_UTILIZATION":
                return maxUtil > 80 ? "SCALE_UP" : "MAINTAIN";
            case "LOW_UTILIZATION":
                return maxUtil < 30 ? "SCALE_DOWN" : "MAINTAIN";
            case "PREDICTED_DEMAND_INCREASE":
                return "SCALE_UP";
            case "PREDICTED_DEMAND_DECREASE":
                return "SCALE_DOWN";
            default:
                return "MAINTAIN";
        }
    }
    
    private String generateScalingReasoning(ResourceHealth health, ScalingTrigger trigger) {
        return String.format("Based on %s trigger and current resource utilization (CPU: %.1f%%, Memory: %.1f%%), scaling recommendation generated", 
            trigger.type(), health.cpuUtilization(), health.memoryUtilization());
    }
}