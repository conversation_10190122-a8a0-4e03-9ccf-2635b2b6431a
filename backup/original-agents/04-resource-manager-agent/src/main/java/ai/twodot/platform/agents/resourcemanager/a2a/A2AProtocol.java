package ai.twodot.platform.agents.resourcemanager.a2a;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.Instant;
import java.util.Map;
import java.util.UUID;

/**
 * Agent-to-Agent (A2A) Communication Protocol
 * 
 * Defines the standard message formats and communication patterns for inter-agent communication.
 */
public class A2AProtocol {

    // Message Types
    public enum MessageType {
        REQUEST,
        RESPONSE,
        EVENT,
        HEARTBEAT,
        ERROR
    }

    // Agent Types
    public enum AgentType {
        RESOURCE_MANAGER("RMA-004"),
        COST_BENEFIT_ANALYZER("CBA-001"),
        DEPLOYMENT_RISK_ASSESSOR("DRA-002"),
        SECURITY_MONITORING_AGENT("SMA-003");

        private final String agentId;

        AgentType(String agentId) {
            this.agentId = agentId;
        }

        public String getAgentId() {
            return agentId;
        }
    }

    // Operation Types
    public enum OperationType {
        // Cost-Benefit Analyzer operations
        ANALYZE_COST_BENEFIT,
        GET_BUDGET_RECOMMENDATIONS,
        VALIDATE_BUDGET_CONSTRAINTS,
        
        // Deployment Risk Assessor operations
        ASSESS_DEPLOYMENT_RISK,
        GET_RISK_MITIGATION_STRATEGIES,
        VALIDATE_DEPLOYMENT_PLAN,
        
        // Security Monitoring Agent operations
        VALIDATE_SECURITY_COMPLIANCE,
        GET_SECURITY_RECOMMENDATIONS,
        MONITOR_SECURITY_EVENTS,
        
        // General operations
        HEALTH_CHECK,
        GET_STATUS,
        GET_METRICS
    }

    // Priority Levels
    public enum Priority {
        LOW(1),
        MEDIUM(2),
        NORMAL(3),
        HIGH(4),
        CRITICAL(5);

        private final int level;

        Priority(int level) {
            this.level = level;
        }

        public int getLevel() {
            return level;
        }
    }

    /**
     * Base A2A Message
     */
    public record A2AMessage(
        @JsonProperty("message_id") String messageId,
        @JsonProperty("correlation_id") String correlationId,
        @JsonProperty("message_type") MessageType messageType,
        @JsonProperty("operation_type") OperationType operationType,
        @JsonProperty("source_agent") AgentType sourceAgent,
        @JsonProperty("target_agent") AgentType targetAgent,
        @JsonProperty("priority") Priority priority,
        @JsonProperty("payload") Map<String, Object> payload,
        @JsonProperty("metadata") Map<String, Object> metadata,
        @JsonProperty("timestamp") Instant timestamp,
        @JsonProperty("expires_at") Instant expiresAt
    ) {
        public A2AMessage {
            if (messageId == null) {
                messageId = UUID.randomUUID().toString();
            }
            if (timestamp == null) {
                timestamp = Instant.now();
            }
            if (expiresAt == null) {
                expiresAt = timestamp.plusSeconds(300); // 5 minutes default TTL
            }
        }

        // Convenience constructor for requests
        public static A2AMessage createRequest(OperationType operation, AgentType source, AgentType target, 
                                             Map<String, Object> payload, String correlationId) {
            return new A2AMessage(
                null, correlationId, MessageType.REQUEST, operation, source, target,
                Priority.NORMAL, payload, Map.of(), null, null
            );
        }

        // Convenience constructor for responses
        public static A2AMessage createResponse(A2AMessage request, Map<String, Object> payload) {
            return new A2AMessage(
                null, request.correlationId(), MessageType.RESPONSE, request.operationType(),
                request.targetAgent(), request.sourceAgent(), request.priority(),
                payload, Map.of("response_to", request.messageId()), null, null
            );
        }

        // Convenience constructor for events
        public static A2AMessage createEvent(OperationType operation, AgentType source, 
                                           Map<String, Object> payload) {
            return new A2AMessage(
                null, null, MessageType.EVENT, operation, source, null,
                Priority.NORMAL, payload, Map.of(), null, null
            );
        }

        // Convenience constructor for errors
        public static A2AMessage createError(A2AMessage request, String errorCode, String errorMessage) {
            Map<String, Object> errorPayload = Map.of(
                "error_code", errorCode,
                "error_message", errorMessage,
                "original_request", request.messageId()
            );
            
            return new A2AMessage(
                null, request.correlationId(), MessageType.ERROR, request.operationType(),
                request.targetAgent(), request.sourceAgent(), Priority.HIGH,
                errorPayload, Map.of(), null, null
            );
        }
    }

    /**
     * A2A Response wrapper
     */
    public record A2AResponse<T>(
        @JsonProperty("success") boolean success,
        @JsonProperty("data") T data,
        @JsonProperty("error_code") String errorCode,
        @JsonProperty("error_message") String errorMessage,
        @JsonProperty("processing_time_ms") long processingTimeMs,
        @JsonProperty("agent_info") AgentInfo agentInfo
    ) {
        public static <T> A2AResponse<T> success(T data, long processingTime, AgentInfo agentInfo) {
            return new A2AResponse<>(true, data, null, null, processingTime, agentInfo);
        }

        public static <T> A2AResponse<T> error(String errorCode, String errorMessage, AgentInfo agentInfo) {
            return new A2AResponse<>(false, null, errorCode, errorMessage, 0, agentInfo);
        }
    }

    /**
     * Agent Information
     */
    public record AgentInfo(
        @JsonProperty("agent_id") String agentId,
        @JsonProperty("agent_type") AgentType agentType,
        @JsonProperty("version") String version,
        @JsonProperty("status") String status,
        @JsonProperty("capabilities") Map<String, Object> capabilities
    ) {
        public static AgentInfo forResourceManager() {
            return new AgentInfo(
                "RMA-004",
                AgentType.RESOURCE_MANAGER,
                "1.0.0",
                "active",
                Map.of(
                    "multi_cloud_support", true,
                    "auto_scaling", true,
                    "cost_optimization", true,
                    "capacity_prediction", true,
                    "supported_clouds", new String[]{"AWS", "GCP", "Azure", "Kubernetes"}
                )
            );
        }
    }

    /**
     * Cost-Benefit Analysis Request/Response Models
     */
    public static class CostBenefitAnalysis {
        public record Request(
            @JsonProperty("allocation_id") String allocationId,
            @JsonProperty("service_id") String serviceId,
            @JsonProperty("resource_requirements") Map<String, Object> resourceRequirements,
            @JsonProperty("budget_constraints") Map<String, Object> budgetConstraints,
            @JsonProperty("time_horizon") String timeHorizon,
            @JsonProperty("business_context") Map<String, Object> businessContext
        ) {}

        public record Response(
            @JsonProperty("analysis_id") String analysisId,
            @JsonProperty("cost_benefit_ratio") double costBenefitRatio,
            @JsonProperty("total_cost") double totalCost,
            @JsonProperty("expected_benefits") double expectedBenefits,
            @JsonProperty("roi_percentage") double roiPercentage,
            @JsonProperty("payback_period_months") int paybackPeriodMonths,
            @JsonProperty("risk_adjusted_npv") double riskAdjustedNpv,
            @JsonProperty("cost_breakdown") Map<String, Double> costBreakdown,
            @JsonProperty("benefit_breakdown") Map<String, Double> benefitBreakdown,
            @JsonProperty("recommendations") java.util.List<String> recommendations,
            @JsonProperty("confidence_score") double confidenceScore
        ) {}
    }

    /**
     * Risk Assessment Request/Response Models
     */
    public static class RiskAssessment {
        public record Request(
            @JsonProperty("deployment_id") String deploymentId,
            @JsonProperty("service_id") String serviceId,
            @JsonProperty("deployment_plan") Map<String, Object> deploymentPlan,
            @JsonProperty("target_environment") Map<String, Object> targetEnvironment,
            @JsonProperty("compliance_requirements") java.util.List<String> complianceRequirements
        ) {}

        public record Response(
            @JsonProperty("assessment_id") String assessmentId,
            @JsonProperty("overall_risk_score") double overallRiskScore,
            @JsonProperty("risk_level") String riskLevel, // LOW, MEDIUM, HIGH, CRITICAL
            @JsonProperty("risk_categories") Map<String, RiskCategory> riskCategories,
            @JsonProperty("mitigation_strategies") java.util.List<MitigationStrategy> mitigationStrategies,
            @JsonProperty("compliance_status") Map<String, Boolean> complianceStatus,
            @JsonProperty("recommendations") java.util.List<String> recommendations,
            @JsonProperty("estimated_impact") Map<String, Object> estimatedImpact
        ) {}

        public record RiskCategory(
            @JsonProperty("category") String category,
            @JsonProperty("score") double score,
            @JsonProperty("likelihood") double likelihood,
            @JsonProperty("impact") double impact,
            @JsonProperty("factors") java.util.List<String> factors
        ) {}

        public record MitigationStrategy(
            @JsonProperty("strategy_id") String strategyId,
            @JsonProperty("description") String description,
            @JsonProperty("effectiveness") double effectiveness,
            @JsonProperty("implementation_cost") double implementationCost,
            @JsonProperty("priority") Priority priority
        ) {}
    }

    /**
     * Security Compliance Request/Response Models
     */
    public static class SecurityCompliance {
        public record Request(
            @JsonProperty("resource_id") String resourceId,
            @JsonProperty("service_id") String serviceId,
            @JsonProperty("security_context") Map<String, Object> securityContext,
            @JsonProperty("compliance_standards") java.util.List<String> complianceStandards,
            @JsonProperty("security_requirements") Map<String, Object> securityRequirements
        ) {}

        public record Response(
            @JsonProperty("compliance_id") String complianceId,
            @JsonProperty("overall_compliance_score") double overallComplianceScore,
            @JsonProperty("compliance_status") String complianceStatus, // COMPLIANT, NON_COMPLIANT, PARTIAL
            @JsonProperty("security_findings") java.util.List<SecurityFinding> securityFindings,
            @JsonProperty("compliance_gaps") java.util.List<ComplianceGap> complianceGaps,
            @JsonProperty("security_recommendations") java.util.List<SecurityRecommendation> securityRecommendations,
            @JsonProperty("remediation_plan") java.util.List<RemediationAction> remediationPlan
        ) {}

        public record SecurityFinding(
            @JsonProperty("finding_id") String findingId,
            @JsonProperty("severity") String severity, // CRITICAL, HIGH, MEDIUM, LOW, INFO
            @JsonProperty("category") String category,
            @JsonProperty("description") String description,
            @JsonProperty("affected_resources") java.util.List<String> affectedResources,
            @JsonProperty("cve_references") java.util.List<String> cveReferences
        ) {}

        public record ComplianceGap(
            @JsonProperty("standard") String standard,
            @JsonProperty("requirement") String requirement,
            @JsonProperty("current_state") String currentState,
            @JsonProperty("required_state") String requiredState,
            @JsonProperty("gap_severity") String gapSeverity
        ) {}

        public record SecurityRecommendation(
            @JsonProperty("recommendation_id") String recommendationId,
            @JsonProperty("title") String title,
            @JsonProperty("description") String description,
            @JsonProperty("priority") Priority priority,
            @JsonProperty("implementation_effort") String implementationEffort,
            @JsonProperty("expected_impact") String expectedImpact
        ) {}

        public record RemediationAction(
            @JsonProperty("action_id") String actionId,
            @JsonProperty("description") String description,
            @JsonProperty("priority") Priority priority,
            @JsonProperty("estimated_effort_hours") int estimatedEffortHours,
            @JsonProperty("dependencies") java.util.List<String> dependencies
        ) {}
    }
}