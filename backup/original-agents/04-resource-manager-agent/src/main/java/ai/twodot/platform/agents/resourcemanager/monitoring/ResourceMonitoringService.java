package ai.twodot.platform.agents.resourcemanager.monitoring;

import ai.twodot.platform.agents.resourcemanager.resource.ResourceAllocation.*;
import ai.twodot.platform.agents.resourcemanager.agent.ResourceManagerAgent.*;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Resource Monitoring Service
 * 
 * Monitors resource usage and health across all allocations.
 */
@Service
public class ResourceMonitoringService {

    private static final Logger logger = LoggerFactory.getLogger(ResourceMonitoringService.class);

    private final AtomicLong monitoringEvents = new AtomicLong(0);
    private final AtomicLong alertsGenerated = new AtomicLong(0);
    private final Map<String, ResourceHealth> currentHealth = new ConcurrentHashMap<>();
    private final Map<String, List<MonitoringEvent>> eventHistory = new ConcurrentHashMap<>();

    public ResourceHealth monitorResourceHealth(String allocationId) {
        logger.debug("Monitoring resource health for allocation: {}", allocationId);
        
        try {
            monitoringEvents.incrementAndGet();
            
            // Simulate resource health monitoring
            ResourceHealth health = collectResourceMetrics(allocationId);
            
            // Store current health
            currentHealth.put(allocationId, health);
            
            // Check for alerts
            checkHealthAlerts(allocationId, health);
            
            // Record monitoring event
            recordMonitoringEvent(allocationId, health);
            
            logger.debug("Resource health monitored for allocation {}: CPU={}, Memory={}", 
                allocationId, health.cpuUtilization(), health.memoryUtilization());
            
            return health;
            
        } catch (Exception e) {
            logger.error("Error monitoring resource health for allocation {}: {}", allocationId, e.getMessage(), e);
            return createErrorHealth(allocationId, e.getMessage());
        }
    }

    public ScalingRecommendation analyzeScalingNeeds(String allocationId, ResourceHealth health) {
        logger.info("Analyzing scaling needs for allocation: {}", allocationId);
        
        try {
            // Analyze current resource utilization
            ScalingDirection direction = determineScalingDirection(health);
            int recommendedInstanceCount = calculateRecommendedInstances(health);
            double confidence = calculateScalingConfidence(health);
            
            Map<String, Object> recommendation = Map.of(
                "direction", direction.name(),
                "recommended_instance_count", recommendedInstanceCount,
                "confidence", confidence,
                "reason", generateScalingReason(health, direction),
                "urgency_deadline", Instant.now().plusSeconds(300).toString()
            );
            
            ScalingRecommendation scalingRecommendation = new ScalingRecommendation(allocationId, recommendation);
            
            logger.info("Scaling recommendation for allocation {}: {} to {} instances (confidence: {})", 
                allocationId, direction, recommendedInstanceCount, confidence);
            
            return scalingRecommendation;
            
        } catch (Exception e) {
            logger.error("Error analyzing scaling needs for allocation {}: {}", allocationId, e.getMessage(), e);
            throw new RuntimeException("Scaling analysis failed: " + e.getMessage());
        }
    }

    public Map<String, Object> getMonitoringMetrics() {
        return Map.of(
            "monitoring_events", monitoringEvents.get(),
            "alerts_generated", alertsGenerated.get(),
            "active_allocations", currentHealth.size(),
            "average_cpu_utilization", calculateAverageCpuUtilization(),
            "average_memory_utilization", calculateAverageMemoryUtilization(),
            "healthy_allocations", countHealthyAllocations(),
            "last_updated", Instant.now()
        );
    }

    public ResourceHealth getCurrentHealth(String allocationId) {
        return currentHealth.get(allocationId);
    }

    public List<MonitoringEvent> getEventHistory(String allocationId) {
        return eventHistory.getOrDefault(allocationId, new ArrayList<>());
    }

    private ResourceHealth collectResourceMetrics(String allocationId) {
        // Simulate realistic resource metrics
        Random random = new Random();
        
        // Generate varying metrics based on time and allocation ID
        long hash = allocationId.hashCode();
        double baseLoad = (hash % 40) + 30; // Base load between 30-70%
        
        // Add some variability
        double cpuUtil = Math.max(0, Math.min(100, baseLoad + random.nextGaussian() * 10));
        double memoryUtil = Math.max(0, Math.min(100, baseLoad + 10 + random.nextGaussian() * 8));
        double diskUtil = Math.max(0, Math.min(100, baseLoad - 10 + random.nextGaussian() * 5));
        double networkUtil = Math.max(0, Math.min(100, baseLoad - 5 + random.nextGaussian() * 15));
        
        return new ResourceHealth(
            allocationId,
            parseHealthStatus(determineHealthStatus(cpuUtil, memoryUtil)),
            cpuUtil,
            memoryUtil,
            diskUtil,
            networkUtil,
            random.nextDouble() * 2, // Error rate 0-2%
            calculateLatency(cpuUtil),
            Instant.now(),
            calculateThroughput(cpuUtil, memoryUtil),
            java.util.List.of() // empty predicted issues
        );
    }

    private void checkHealthAlerts(String allocationId, ResourceHealth health) {
        List<String> alerts = new ArrayList<>();
        
        if (health.cpuUtilization() > 90) {
            alerts.add("HIGH_CPU_UTILIZATION");
        }
        if (health.memoryUtilization() > 90) {
            alerts.add("HIGH_MEMORY_UTILIZATION");
        }
        if (health.errorRate() > 5.0) {
            alerts.add("HIGH_ERROR_RATE");
        }
        if (health.averageLatencyMs() > 1000) {
            alerts.add("HIGH_LATENCY");
        }
        
        if (!alerts.isEmpty()) {
            alertsGenerated.addAndGet(alerts.size());
            logger.warn("Health alerts for allocation {}: {}", allocationId, alerts);
        }
    }

    private void recordMonitoringEvent(String allocationId, ResourceHealth health) {
        MonitoringEvent event = new MonitoringEvent(
            allocationId,
            "HEALTH_CHECK",
            Map.of(
                "cpu_utilization", health.cpuUtilization(),
                "memory_utilization", health.memoryUtilization(),
                "status", health.status()
            ),
            health.timestamp()
        );
        
        eventHistory.computeIfAbsent(allocationId, k -> new ArrayList<>()).add(event);
        
        // Keep only last 100 events per allocation
        List<MonitoringEvent> events = eventHistory.get(allocationId);
        if (events.size() > 100) {
            events.subList(0, events.size() - 100).clear();
        }
    }

    private ScalingDirection determineScalingDirection(ResourceHealth health) {
        double cpuUtil = health.cpuUtilization();
        double memoryUtil = health.memoryUtilization();
        
        if (cpuUtil > 80 || memoryUtil > 80) {
            return ScalingDirection.UP;
        } else if (cpuUtil < 30 && memoryUtil < 30) {
            return ScalingDirection.DOWN;
        } else {
            return ScalingDirection.NONE;
        }
    }

    private int calculateRecommendedInstances(ResourceHealth health) {
        double utilizationFactor = Math.max(health.cpuUtilization(), health.memoryUtilization()) / 100.0;
        
        if (utilizationFactor > 0.8) {
            return 3; // Scale up
        } else if (utilizationFactor < 0.3) {
            return 1; // Scale down
        } else {
            return 2; // Maintain
        }
    }

    private double calculateScalingConfidence(ResourceHealth health) {
        // Higher confidence for extreme values
        double maxUtil = Math.max(health.cpuUtilization(), health.memoryUtilization());
        
        if (maxUtil > 90 || maxUtil < 20) {
            return 0.95;
        } else if (maxUtil > 80 || maxUtil < 30) {
            return 0.85;
        } else {
            return 0.70;
        }
    }

    private String generateScalingReason(ResourceHealth health, ScalingDirection direction) {
        switch (direction) {
            case UP:
                return String.format("High resource utilization detected: CPU=%.1f%%, Memory=%.1f%%", 
                    health.cpuUtilization(), health.memoryUtilization());
            case DOWN:
                return String.format("Low resource utilization detected: CPU=%.1f%%, Memory=%.1f%%", 
                    health.cpuUtilization(), health.memoryUtilization());
            default:
                return "Resource utilization within optimal range";
        }
    }

    private double calculateLatency(double cpuUtilization) {
        // Simulate latency increase with CPU utilization
        return 100 + (cpuUtilization * 10); // Base 100ms + utilization factor
    }

    private double calculateThroughput(double cpuUtilization, double memoryUtilization) {
        // Simulate throughput decrease with high utilization
        double utilizationFactor = Math.max(cpuUtilization, memoryUtilization) / 100.0;
        return Math.max(100, 1000 * (1.0 - utilizationFactor * 0.5)); // Throughput decreases with utilization
    }

    private String determineHealthStatus(double cpuUtil, double memoryUtil) {
        double maxUtil = Math.max(cpuUtil, memoryUtil);
        
        if (maxUtil > 90) {
            return "Critical";
        } else if (maxUtil > 75) {
            return "Warning";
        } else {
            return "Healthy";
        }
    }

    private ResourceHealth createErrorHealth(String allocationId, String errorMessage) {
        return new ResourceHealth(
            allocationId,
            HealthStatus.CRITICAL,
            0.0, 0.0, 0.0, 0.0, // cpu, memory, disk, network
            100.0, // High error rate
            0.0, // response time
            Instant.now(),
            0.0, // health score
            java.util.List.of(errorMessage) // predicted issues
        );
    }
    
    private HealthStatus parseHealthStatus(String statusString) {
        switch (statusString) {
            case "Critical":
                return HealthStatus.CRITICAL;
            case "Warning":
                return HealthStatus.WARNING;
            case "Healthy":
            default:
                return HealthStatus.HEALTHY;
        }
    }

    private double calculateAverageCpuUtilization() {
        return currentHealth.values().stream()
            .mapToDouble(ResourceHealth::cpuUtilization)
            .average()
            .orElse(0.0);
    }

    private double calculateAverageMemoryUtilization() {
        return currentHealth.values().stream()
            .mapToDouble(ResourceHealth::memoryUtilization)
            .average()
            .orElse(0.0);
    }

    private long countHealthyAllocations() {
        return currentHealth.values().stream()
            .filter(health -> HealthStatus.HEALTHY.equals(health.status()))
            .count();
    }

    // Additional methods required by ResourceManagerAgent
    
    public void startMonitoring(AllocationResult allocationResult) {
        logger.info("Starting monitoring for allocation: {}", allocationResult.allocationId());
        
        // Initialize monitoring for the allocation
        String allocationId = allocationResult.allocationId();
        
        // Create initial health status
        ResourceHealth initialHealth = new ResourceHealth(
            allocationId,
            HealthStatus.HEALTHY,
            30.0, 25.0, 20.0, 15.0, // cpu, memory, disk, network
            0.5, // error rate
            100.0, // latency
            Instant.now(),
            95.0, // health score
            java.util.List.of()
        );
        
        currentHealth.put(allocationId, initialHealth);
        logger.info("Monitoring started for allocation: {}", allocationId);
    }
    
    public ResourceHealth getResourceHealth(String allocationId) {
        logger.debug("Getting resource health for allocation: {}", allocationId);
        
        ResourceHealth health = currentHealth.get(allocationId);
        if (health == null) {
            // Generate new health data if not found
            health = collectResourceMetrics(allocationId);
            currentHealth.put(allocationId, health);
        }
        
        return health;
    }
    
    public boolean isMonitoringActive() {
        return !currentHealth.isEmpty();
    }

    public record MonitoringEvent(
        String allocationId,
        String eventType,
        Map<String, Object> data,
        Instant timestamp
    ) {}
}