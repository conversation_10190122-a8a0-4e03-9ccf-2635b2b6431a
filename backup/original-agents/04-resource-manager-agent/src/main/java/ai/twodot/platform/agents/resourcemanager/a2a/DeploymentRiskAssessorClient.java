package ai.twodot.platform.agents.resourcemanager.a2a;

import ai.twodot.platform.agents.resourcemanager.a2a.A2AProtocol.*;
import ai.twodot.platform.agents.resourcemanager.resource.ResourceAllocation.*;
import ai.twodot.platform.agents.resourcemanager.agent.ResourceManagerAgent.*;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Deployment Risk Assessor Client (DRA-002)
 * 
 * Integrates with the Deployment Risk Assessor agent to evaluate deployment risks and mitigation strategies.
 */
@Service
public class DeploymentRiskAssessorClient {

    private static final Logger logger = LoggerFactory.getLogger(DeploymentRiskAssessorClient.class);

    @Autowired
    private A2ACommunicationService communicationService;

    private final AtomicLong assessmentRequests = new AtomicLong(0);
    private final AtomicLong successfulAssessments = new AtomicLong(0);
    private final AtomicLong failedAssessments = new AtomicLong(0);
    private final Map<String, CachedAssessment> assessmentCache = new HashMap<>();

    /**
     * Assess deployment risk for a resource allocation request
     */
    public CompletableFuture<DeploymentRiskAssessmentResult> assessDeploymentRisk(AllocationRequest request,
                                                                                 MultiCloudDeploymentPlan deploymentPlan) {
        logger.info("Requesting deployment risk assessment for allocation: {}", request.allocationId());
        
        assessmentRequests.incrementAndGet();
        
        // Check cache first
        String cacheKey = generateCacheKey(request, deploymentPlan);
        CachedAssessment cached = assessmentCache.get(cacheKey);
        if (cached != null && isValidCache(cached)) {
            logger.debug("Using cached risk assessment for allocation: {}", request.allocationId());
            return CompletableFuture.completedFuture(cached.result());
        }
        
        // Prepare request payload
        Map<String, Object> payload = createRiskAssessmentPayload(request, deploymentPlan);
        
        return communicationService
            .sendRequest(OperationType.ASSESS_DEPLOYMENT_RISK, AgentType.DEPLOYMENT_RISK_ASSESSOR, 
                        payload, RiskAssessment.Response.class)
            .thenApply(response -> {
                if (response.success()) {
                    successfulAssessments.incrementAndGet();
                    DeploymentRiskAssessmentResult result = convertToRiskAssessmentResult(response.data());
                    
                    // Cache the result
                    assessmentCache.put(cacheKey, new CachedAssessment(result, Instant.now()));
                    
                    logger.info("Risk assessment completed for allocation {}: risk_level={}, score={}", 
                        request.allocationId(), result.riskLevel(), result.overallRiskScore());
                    
                    return result;
                } else {
                    failedAssessments.incrementAndGet();
                    logger.error("Risk assessment failed for allocation {}: {} - {}", 
                        request.allocationId(), response.errorCode(), response.errorMessage());
                    
                    return createFallbackRiskAssessment(request, deploymentPlan);
                }
            })
            .exceptionally(throwable -> {
                failedAssessments.incrementAndGet();
                logger.error("Error during risk assessment for allocation {}: {}", 
                    request.allocationId(), throwable.getMessage(), throwable);
                
                return createFallbackRiskAssessment(request, deploymentPlan);
            });
    }

    /**
     * Get risk mitigation strategies for a deployment
     */
    public CompletableFuture<RiskMitigationResult> getRiskMitigationStrategies(String deploymentId,
                                                                              List<String> identifiedRisks) {
        logger.info("Requesting risk mitigation strategies for deployment: {}", deploymentId);
        
        Map<String, Object> payload = Map.of(
            "deployment_id", deploymentId,
            "identified_risks", identifiedRisks,
            "mitigation_scope", "deployment",
            "urgency_level", "normal"
        );
        
        return communicationService
            .sendRequest(OperationType.GET_RISK_MITIGATION_STRATEGIES, AgentType.DEPLOYMENT_RISK_ASSESSOR, 
                        payload, Map.class)
            .thenApply(response -> {
                if (response.success()) {
                    return convertToRiskMitigationResult(response.data());
                } else {
                    logger.error("Risk mitigation request failed for deployment {}: {} - {}", 
                        deploymentId, response.errorCode(), response.errorMessage());
                    return createFallbackRiskMitigation(deploymentId, identifiedRisks);
                }
            })
            .exceptionally(throwable -> {
                logger.error("Error getting risk mitigation strategies for deployment {}: {}", 
                    deploymentId, throwable.getMessage(), throwable);
                return createFallbackRiskMitigation(deploymentId, identifiedRisks);
            });
    }

    /**
     * Validate a deployment plan for risk compliance
     */
    public CompletableFuture<DeploymentValidationResult> validateDeploymentPlan(MultiCloudDeploymentPlan deploymentPlan,
                                                                               List<String> complianceRequirements) {
        logger.info("Validating deployment plan for service: {}", deploymentPlan.serviceId());
        
        Map<String, Object> payload = createDeploymentValidationPayload(deploymentPlan, complianceRequirements);
        
        return communicationService
            .sendRequest(OperationType.VALIDATE_DEPLOYMENT_PLAN, AgentType.DEPLOYMENT_RISK_ASSESSOR, 
                        payload, Map.class)
            .thenApply(response -> {
                if (response.success()) {
                    return convertToDeploymentValidationResult(response.data());
                } else {
                    logger.error("Deployment validation failed for service {}: {} - {}", 
                        deploymentPlan.serviceId(), response.errorCode(), response.errorMessage());
                    return createFallbackDeploymentValidation(deploymentPlan);
                }
            })
            .exceptionally(throwable -> {
                logger.error("Error validating deployment plan for service {}: {}", 
                    deploymentPlan.serviceId(), throwable.getMessage(), throwable);
                return createFallbackDeploymentValidation(deploymentPlan);
            });
    }

    /**
     * Get DRA client metrics
     */
    public Map<String, Object> getMetrics() {
        return Map.of(
            "assessment_requests", assessmentRequests.get(),
            "successful_assessments", successfulAssessments.get(),
            "failed_assessments", failedAssessments.get(),
            "success_rate", calculateSuccessRate(),
            "cache_size", assessmentCache.size(),
            "dra_agent_available", communicationService.isAgentAvailable(AgentType.DEPLOYMENT_RISK_ASSESSOR),
            "last_updated", Instant.now()
        );
    }

    // Private methods

    private Map<String, Object> createRiskAssessmentPayload(AllocationRequest request, 
                                                           MultiCloudDeploymentPlan deploymentPlan) {
        Map<String, Object> deploymentPlanMap = new HashMap<>(deploymentPlan.plan());
        deploymentPlanMap.put("service_id", deploymentPlan.serviceId());
        
        Map<String, Object> targetEnvironment = Map.of(
            "cloud_provider", deploymentPlan.plan().getOrDefault("optimal_provider", "AWS"),
            "region", request.constraints().preferredRegions().isEmpty() ? 
                "us-east-1" : request.constraints().preferredRegions().get(0),
            "environment_type", determineEnvironmentType(request),
            "high_availability", request.requirements().scaling().autoScalingEnabled(),
            "multi_zone", true
        );

        List<String> complianceRequirements = new ArrayList<>();
        if (request.constraints().complianceRequirements() != null) {
            complianceRequirements.addAll(request.constraints().complianceRequirements());
        }
        
        // Add default compliance requirements based on service criticality
        if (request.priority() == AllocationPriority.CRITICAL) {
            complianceRequirements.addAll(List.of("SOC2", "ISO27001", "HIPAA"));
        } else if (request.priority() == AllocationPriority.HIGH) {
            complianceRequirements.addAll(List.of("SOC2", "ISO27001"));
        }

        return Map.of(
            "deployment_id", request.allocationId(),
            "service_id", request.serviceId(),
            "deployment_plan", deploymentPlanMap,
            "target_environment", targetEnvironment,
            "compliance_requirements", complianceRequirements
        );
    }

    private Map<String, Object> createDeploymentValidationPayload(MultiCloudDeploymentPlan deploymentPlan,
                                                                 List<String> complianceRequirements) {
        Map<String, Object> deploymentConfig = Map.of(
            "service_id", deploymentPlan.serviceId(),
            "deployment_strategy", deploymentPlan.plan().getOrDefault("deployment_strategy", "rolling"),
            "cloud_configuration", deploymentPlan.plan(),
            "security_configuration", Map.of(
                "encryption_at_rest", true,
                "encryption_in_transit", true,
                "network_security", "vpc_isolation"
            ),
            "monitoring_configuration", Map.of(
                "metrics_enabled", true,
                "logging_enabled", true,
                "alerting_enabled", true
            )
        );

        Map<String, Object> validationCriteria = Map.of(
            "check_security_compliance", true,
            "check_performance_requirements", true,
            "check_availability_requirements", true,
            "check_data_protection", true,
            "check_disaster_recovery", true
        );

        return Map.of(
            "deployment_config", deploymentConfig,
            "compliance_requirements", complianceRequirements != null ? complianceRequirements : List.of(),
            "validation_criteria", validationCriteria,
            "validation_scope", "full"
        );
    }

    private DeploymentRiskAssessmentResult convertToRiskAssessmentResult(RiskAssessment.Response response) {
        Map<String, RiskCategoryResult> riskCategories = new HashMap<>();
        for (Map.Entry<String, RiskAssessment.RiskCategory> entry : response.riskCategories().entrySet()) {
            RiskAssessment.RiskCategory category = entry.getValue();
            riskCategories.put(entry.getKey(), new RiskCategoryResult(
                category.category(),
                category.score(),
                category.likelihood(),
                category.impact(),
                category.factors()
            ));
        }

        List<MitigationStrategyResult> mitigationStrategies = response.mitigationStrategies().stream()
            .map(strategy -> new MitigationStrategyResult(
                strategy.strategyId(),
                strategy.description(),
                strategy.effectiveness(),
                strategy.implementationCost(),
                strategy.priority().name()
            ))
            .toList();

        return new DeploymentRiskAssessmentResult(
            response.assessmentId(),
            response.overallRiskScore(),
            response.riskLevel(),
            riskCategories,
            mitigationStrategies,
            response.complianceStatus(),
            response.recommendations(),
            response.estimatedImpact(),
            Instant.now()
        );
    }

    private RiskMitigationResult convertToRiskMitigationResult(Map<String, Object> data) {
        List<Map<String, Object>> strategiesData = (List<Map<String, Object>>) data.getOrDefault("strategies", List.of());
        List<MitigationStrategyResult> strategies = strategiesData.stream()
            .map(strategyData -> new MitigationStrategyResult(
                (String) strategyData.get("strategy_id"),
                (String) strategyData.get("description"),
                (Double) strategyData.getOrDefault("effectiveness", 0.8),
                (Double) strategyData.getOrDefault("implementation_cost", 1000.0),
                (String) strategyData.getOrDefault("priority", "MEDIUM")
            ))
            .toList();

        return new RiskMitigationResult(
            (String) data.get("mitigation_id"),
            strategies,
            (Double) data.getOrDefault("overall_effectiveness", 0.8),
            (Double) data.getOrDefault("total_implementation_cost", 5000.0),
            (List<String>) data.getOrDefault("recommendations", List.of()),
            Instant.now()
        );
    }

    private DeploymentValidationResult convertToDeploymentValidationResult(Map<String, Object> data) {
        return new DeploymentValidationResult(
            (String) data.get("validation_id"),
            (Boolean) data.getOrDefault("is_valid", true),
            (Double) data.getOrDefault("compliance_score", 85.0),
            (List<String>) data.getOrDefault("validation_errors", List.of()),
            (List<String>) data.getOrDefault("validation_warnings", List.of()),
            (List<String>) data.getOrDefault("recommendations", List.of()),
            (Map<String, Boolean>) data.getOrDefault("compliance_status", Map.of()),
            Instant.now()
        );
    }

    private DeploymentRiskAssessmentResult createFallbackRiskAssessment(AllocationRequest request,
                                                                       MultiCloudDeploymentPlan deploymentPlan) {
        logger.info("Creating fallback risk assessment for allocation: {}", request.allocationId());
        
        // Basic risk assessment based on request characteristics
        double riskScore = calculateBasicRiskScore(request);
        String riskLevel = determineRiskLevel(riskScore);
        
        Map<String, RiskCategoryResult> riskCategories = Map.of(
            "security", new RiskCategoryResult("security", 60.0, 0.3, 0.7, 
                List.of("Standard cloud security controls", "Network isolation required")),
            "availability", new RiskCategoryResult("availability", 70.0, 0.4, 0.6, 
                List.of("Multi-zone deployment", "Auto-scaling configured")),
            "performance", new RiskCategoryResult("performance", 50.0, 0.2, 0.5, 
                List.of("Resource requirements within normal ranges")),
            "compliance", new RiskCategoryResult("compliance", 80.0, 0.6, 0.8, 
                List.of("Industry standard compliance requirements"))
        );
        
        List<MitigationStrategyResult> mitigationStrategies = List.of(
            new MitigationStrategyResult("MIT-001", "Implement comprehensive monitoring", 0.8, 2000.0, "HIGH"),
            new MitigationStrategyResult("MIT-002", "Configure automated backups", 0.9, 1000.0, "HIGH"),
            new MitigationStrategyResult("MIT-003", "Set up disaster recovery procedures", 0.7, 5000.0, "MEDIUM")
        );
        
        return new DeploymentRiskAssessmentResult(
            UUID.randomUUID().toString(),
            riskScore,
            riskLevel,
            riskCategories,
            mitigationStrategies,
            Map.of("SOC2", true, "ISO27001", false),
            List.of("Implement security best practices", "Monitor resource utilization", "Regular compliance audits"),
            Map.of("financial_impact", "medium", "operational_impact", "low"),
            Instant.now()
        );
    }

    private RiskMitigationResult createFallbackRiskMitigation(String deploymentId, List<String> identifiedRisks) {
        List<MitigationStrategyResult> strategies = List.of(
            new MitigationStrategyResult("FALLBACK-001", "Implement standard monitoring", 0.7, 1500.0, "HIGH"),
            new MitigationStrategyResult("FALLBACK-002", "Configure alerting", 0.8, 800.0, "HIGH"),
            new MitigationStrategyResult("FALLBACK-003", "Set up backup procedures", 0.9, 1200.0, "MEDIUM")
        );
        
        return new RiskMitigationResult(
            UUID.randomUUID().toString(),
            strategies,
            0.8,
            3500.0,
            List.of("Implement comprehensive monitoring", "Regular security assessments", "Disaster recovery testing"),
            Instant.now()
        );
    }

    private DeploymentValidationResult createFallbackDeploymentValidation(MultiCloudDeploymentPlan deploymentPlan) {
        return new DeploymentValidationResult(
            UUID.randomUUID().toString(),
            true, // Assume valid by default
            85.0, // Good compliance score
            List.of(), // No errors
            List.of("Consider implementing additional monitoring"),
            List.of("Standard deployment practices followed", "Security controls in place"),
            Map.of("basic_security", true, "monitoring", true, "backup", false),
            Instant.now()
        );
    }

    private double calculateBasicRiskScore(AllocationRequest request) {
        double score = 50.0; // Base score
        
        // Adjust based on priority
        switch (request.priority()) {
            case CRITICAL -> score += 30;
            case HIGH -> score += 20;
            case NORMAL -> score += 10;
            case LOW -> score += 0;
        }
        
        // Adjust based on resource scale
        if (request.requirements().scaling().maxInstances() > 10) {
            score += 15; // Large scale increases risk
        }
        
        // Adjust based on compliance requirements
        if (request.constraints().complianceRequirements() != null && 
            !request.constraints().complianceRequirements().isEmpty()) {
            score += 10; // Compliance requirements increase risk
        }
        
        return Math.min(100.0, score);
    }

    private String determineRiskLevel(double score) {
        if (score >= 80) return "CRITICAL";
        if (score >= 60) return "HIGH";
        if (score >= 40) return "MEDIUM";
        return "LOW";
    }

    private String determineEnvironmentType(AllocationRequest request) {
        if (request.constraints().budgetLimit() != null) {
            return switch (request.constraints().budgetLimit().environment()) {
                case "production" -> "production";
                case "staging" -> "staging";
                case "development" -> "development";
                default -> "development";
            };
        }
        return "development";
    }

    private String generateCacheKey(AllocationRequest request, MultiCloudDeploymentPlan deploymentPlan) {
        return String.format("dra_%s_%d_%d", request.serviceId(), 
            Objects.hash(request.requirements(), request.constraints()), 
            Objects.hash(deploymentPlan.plan()));
    }

    private boolean isValidCache(CachedAssessment cached) {
        return cached.timestamp().isAfter(Instant.now().minusSeconds(1800)); // 30 minutes TTL
    }

    private double calculateSuccessRate() {
        long total = assessmentRequests.get();
        if (total == 0) {
            return 1.0;
        }
        return (double) successfulAssessments.get() / total;
    }

    // Result classes

    public record DeploymentRiskAssessmentResult(
        String assessmentId,
        double overallRiskScore,
        String riskLevel,
        Map<String, RiskCategoryResult> riskCategories,
        List<MitigationStrategyResult> mitigationStrategies,
        Map<String, Boolean> complianceStatus,
        List<String> recommendations,
        Map<String, Object> estimatedImpact,
        Instant assessedAt
    ) {}

    public record RiskCategoryResult(
        String category,
        double score,
        double likelihood,
        double impact,
        List<String> factors
    ) {}

    public record MitigationStrategyResult(
        String strategyId,
        String description,
        double effectiveness,
        double implementationCost,
        String priority
    ) {}

    public record RiskMitigationResult(
        String mitigationId,
        List<MitigationStrategyResult> strategies,
        double overallEffectiveness,
        double totalImplementationCost,
        List<String> recommendations,
        Instant generatedAt
    ) {}

    public record DeploymentValidationResult(
        String validationId,
        boolean isValid,
        double complianceScore,
        List<String> validationErrors,
        List<String> validationWarnings,
        List<String> recommendations,
        Map<String, Boolean> complianceStatus,
        Instant validatedAt
    ) {}

    private record CachedAssessment(
        DeploymentRiskAssessmentResult result,
        Instant timestamp
    ) {}
}