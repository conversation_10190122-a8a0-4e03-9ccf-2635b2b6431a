package ai.twodot.platform.agents.resourcemanager.resource;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.Instant;
import java.util.Map;

/**
 * Resource allocation data models for the Resource Manager Agent.
 * Represents resource allocations across multi-cloud environments.
 */
public class ResourceAllocation {

    /**
     * Resource allocation request
     */
    public record AllocationRequest(
        @JsonProperty("allocation_id") String allocationId,
        @JsonProperty("service_id") String serviceId,
        @JsonProperty("resource_requirements") ResourceRequirements requirements,
        @JsonProperty("constraints") AllocationConstraints constraints,
        @JsonProperty("priority") AllocationPriority priority,
        @JsonProperty("metadata") Map<String, Object> metadata,
        @JsonProperty("requested_at") Instant requestedAt
    ) {}

    /**
     * Resource requirements specification
     */
    public record ResourceRequirements(
        @JsonProperty("cpu") CpuRequirement cpu,
        @JsonProperty("memory") MemoryRequirement memory,
        @JsonProperty("storage") StorageRequirement storage,
        @JsonProperty("network") NetworkRequirement network,
        @JsonProperty("gpu") GpuRequirement gpu,
        @JsonProperty("scaling") ScalingRequirement scaling
    ) {}

    /**
     * CPU resource requirement
     */
    public record CpuRequirement(
        @JsonProperty("min_cores") double minCores,
        @JsonProperty("max_cores") double maxCores,
        @JsonProperty("target_utilization") double targetUtilization,
        @JsonProperty("cpu_type") String cpuType,
        @JsonProperty("architecture") String architecture
    ) {}

    /**
     * Memory resource requirement
     */
    public record MemoryRequirement(
        @JsonProperty("min_gb") double minGb,
        @JsonProperty("max_gb") double maxGb,
        @JsonProperty("memory_type") String memoryType,
        @JsonProperty("target_utilization") double targetUtilization
    ) {}

    /**
     * Storage resource requirement
     */
    public record StorageRequirement(
        @JsonProperty("min_gb") double minGb,
        @JsonProperty("max_gb") double maxGb,
        @JsonProperty("storage_type") StorageType storageType,
        @JsonProperty("iops") int iops,
        @JsonProperty("throughput_mbps") int throughputMbps,
        @JsonProperty("backup_required") boolean backupRequired
    ) {}

    /**
     * Network resource requirement
     */
    public record NetworkRequirement(
        @JsonProperty("min_bandwidth_mbps") double minBandwidthMbps,
        @JsonProperty("max_bandwidth_mbps") double maxBandwidthMbps,
        @JsonProperty("latency_ms") double latencyMs,
        @JsonProperty("availability_zones") int availabilityZones,
        @JsonProperty("load_balancer_required") boolean loadBalancerRequired
    ) {}

    /**
     * GPU resource requirement
     */
    public record GpuRequirement(
        @JsonProperty("gpu_count") int gpuCount,
        @JsonProperty("gpu_type") String gpuType,
        @JsonProperty("memory_gb") double memoryGb,
        @JsonProperty("cuda_version") String cudaVersion
    ) {}

    /**
     * Scaling requirement specification
     */
    public record ScalingRequirement(
        @JsonProperty("min_instances") int minInstances,
        @JsonProperty("max_instances") int maxInstances,
        @JsonProperty("target_cpu_utilization") double targetCpuUtilization,
        @JsonProperty("target_memory_utilization") double targetMemoryUtilization,
        @JsonProperty("scale_up_cooldown_seconds") int scaleUpCooldownSeconds,
        @JsonProperty("scale_down_cooldown_seconds") int scaleDownCooldownSeconds,
        @JsonProperty("predictive_scaling_enabled") boolean predictiveScalingEnabled,
        @JsonProperty("auto_scaling_enabled") boolean autoScalingEnabled
    ) {}

    /**
     * Allocation constraints
     */
    public record AllocationConstraints(
        @JsonProperty("preferred_clouds") java.util.List<CloudProvider> preferredClouds,
        @JsonProperty("excluded_clouds") java.util.List<CloudProvider> excludedClouds,
        @JsonProperty("regions") java.util.List<String> regions,
        @JsonProperty("availability_zones") java.util.List<String> availabilityZones,
        @JsonProperty("budget_limit") BudgetConstraint budgetLimit,
        @JsonProperty("compliance_requirements") java.util.List<String> complianceRequirements,
        @JsonProperty("performance_sla") PerformanceSla performanceSla,
        @JsonProperty("sustainability_goals") SustainabilityGoals sustainabilityGoals,
        @JsonProperty("preferred_regions") java.util.List<String> preferredRegions,
        @JsonProperty("preferred_cloud_providers") java.util.List<CloudProvider> preferredCloudProviders
    ) {}

    /**
     * Budget constraint specification
     */
    public record BudgetConstraint(
        @JsonProperty("max_hourly_cost") double maxHourlyCost,
        @JsonProperty("max_monthly_cost") double maxMonthlyCost,
        @JsonProperty("currency") String currency,
        @JsonProperty("cost_center") String costCenter,
        @JsonProperty("monthly_limit") double monthlyLimit,
        @JsonProperty("environment") String environment
    ) {}

    /**
     * Performance SLA specification
     */
    public record PerformanceSla(
        @JsonProperty("availability_percent") double availabilityPercent,
        @JsonProperty("max_response_time_ms") double maxResponseTimeMs,
        @JsonProperty("max_error_rate_percent") double maxErrorRatePercent,
        @JsonProperty("min_throughput_rps") double minThroughputRps,
        @JsonProperty("min_availability_percent") double minAvailabilityPercent
    ) {}

    /**
     * Sustainability goals
     */
    public record SustainabilityGoals(
        @JsonProperty("max_carbon_footprint_kg") double maxCarbonFootprintKg,
        @JsonProperty("renewable_energy_preference") boolean renewableEnergyPreference,
        @JsonProperty("green_regions_only") boolean greenRegionsOnly,
        @JsonProperty("carbon_offset_enabled") boolean carbonOffsetEnabled
    ) {}

    /**
     * Resource allocation result
     */
    public record AllocationResult(
        @JsonProperty("allocation_id") String allocationId,
        @JsonProperty("status") AllocationStatus status,
        @JsonProperty("allocated_resources") AllocatedResources allocatedResources,
        @JsonProperty("cost_estimate") CostEstimate costEstimate,
        @JsonProperty("performance_prediction") PerformancePrediction performancePrediction,
        @JsonProperty("carbon_footprint") CarbonFootprint carbonFootprint,
        @JsonProperty("allocation_time_ms") long allocationTimeMs,
        @JsonProperty("allocated_at") Instant allocatedAt,
        @JsonProperty("error_message") String errorMessage
    ) {}

    /**
     * Allocated resources details
     */
    public record AllocatedResources(
        @JsonProperty("cloud_provider") CloudProvider cloudProvider,
        @JsonProperty("region") String region,
        @JsonProperty("availability_zone") String availabilityZone,
        @JsonProperty("instance_type") String instanceType,
        @JsonProperty("instance_count") int instanceCount,
        @JsonProperty("cpu_cores") double cpuCores,
        @JsonProperty("memory_gb") double memoryGb,
        @JsonProperty("storage_gb") double storageGb,
        @JsonProperty("network_bandwidth_mbps") double networkBandwidthMbps,
        @JsonProperty("gpu_count") int gpuCount,
        @JsonProperty("resource_ids") java.util.List<String> resourceIds
    ) {}

    /**
     * Cost estimate
     */
    public record CostEstimate(
        @JsonProperty("hourly_cost") double hourlyCost,
        @JsonProperty("daily_cost") double dailyCost,
        @JsonProperty("monthly_cost") double monthlyCost,
        @JsonProperty("currency") String currency,
        @JsonProperty("cost_breakdown") Map<String, Double> costBreakdown,
        @JsonProperty("savings_vs_on_demand") double savingsVsOnDemand,
        @JsonProperty("cost_optimization_opportunities") java.util.List<String> optimizationOpportunities
    ) {}

    /**
     * Performance prediction
     */
    public record PerformancePrediction(
        @JsonProperty("expected_cpu_utilization") double expectedCpuUtilization,
        @JsonProperty("expected_memory_utilization") double expectedMemoryUtilization,
        @JsonProperty("expected_response_time_ms") double expectedResponseTimeMs,
        @JsonProperty("expected_throughput_rps") double expectedThroughputRps,
        @JsonProperty("confidence_score") double confidenceScore,
        @JsonProperty("bottleneck_prediction") String bottleneckPrediction
    ) {}

    /**
     * Carbon footprint estimation
     */
    public record CarbonFootprint(
        @JsonProperty("estimated_co2_kg_per_hour") double estimatedCo2KgPerHour,
        @JsonProperty("estimated_co2_kg_per_day") double estimatedCo2KgPerDay,
        @JsonProperty("estimated_co2_kg_per_month") double estimatedCo2KgPerMonth,
        @JsonProperty("renewable_energy_percentage") double renewableEnergyPercentage,
        @JsonProperty("carbon_intensity_g_per_kwh") double carbonIntensityGPerKwh,
        @JsonProperty("offset_cost") double offsetCost
    ) {}

    /**
     * Cloud provider enumeration
     */
    public enum CloudProvider {
        AWS,
        GCP,
        AZURE,
        KUBERNETES_ON_PREMISES,
        HYBRID,
        MULTI_CLOUD
    }

    /**
     * Storage type enumeration
     */
    public enum StorageType {
        SSD,
        HDD,
        NVME,
        NETWORK_ATTACHED,
        OBJECT_STORAGE,
        BLOCK_STORAGE
    }

    /**
     * Allocation priority enumeration
     */
    public enum AllocationPriority {
        LOW,
        NORMAL,
        HIGH,
        CRITICAL,
        EMERGENCY
    }

    /**
     * Allocation status enumeration
     */
    public enum AllocationStatus {
        PENDING,
        ANALYZING,
        ALLOCATING,
        ALLOCATED,
        SCALING,
        OPTIMIZING,
        FAILED,
        TERMINATED
    }

    /**
     * Resource scaling decision
     */
    public record ScalingDecision(
        @JsonProperty("service_id") String serviceId,
        @JsonProperty("current_instances") int currentInstances,
        @JsonProperty("target_instances") int targetInstances,
        @JsonProperty("scaling_direction") ScalingDirection scalingDirection,
        @JsonProperty("scaling_reason") String scalingReason,
        @JsonProperty("confidence_score") double confidenceScore,
        @JsonProperty("estimated_completion_time") Instant estimatedCompletionTime,
        @JsonProperty("cost_impact") double costImpact,
        @JsonProperty("performance_impact") String performanceImpact
    ) {}

    /**
     * Scaling direction enumeration
     */
    public enum ScalingDirection {
        UP,
        DOWN,
        NONE
    }

    /**
     * Resource health status
     */
    public record ResourceHealth(
        @JsonProperty("resource_id") String resourceId,
        @JsonProperty("health_status") HealthStatus healthStatus,
        @JsonProperty("cpu_utilization") double cpuUtilization,
        @JsonProperty("memory_utilization") double memoryUtilization,
        @JsonProperty("disk_utilization") double diskUtilization,
        @JsonProperty("network_utilization") double networkUtilization,
        @JsonProperty("error_rate") double errorRate,
        @JsonProperty("response_time_ms") double responseTimeMs,
        @JsonProperty("last_check") Instant lastCheck,
        @JsonProperty("health_score") double healthScore,
        @JsonProperty("predicted_issues") java.util.List<String> predictedIssues
    ) {
        // Backward compatibility methods
        public double averageLatencyMs() { return responseTimeMs; }
        public HealthStatus status() { return healthStatus; }
        public Instant timestamp() { return lastCheck; }
    }

    /**
     * Health status enumeration
     */
    public enum HealthStatus {
        HEALTHY,
        WARNING,
        CRITICAL,
        UNKNOWN,
        MAINTENANCE
    }
}