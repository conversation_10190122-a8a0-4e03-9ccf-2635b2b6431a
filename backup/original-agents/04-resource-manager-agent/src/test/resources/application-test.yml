# Test configuration for Resource Manager Agent

spring:
  profiles:
    active: test
  application:
    name: resource-manager-agent-test
  
  # Disable banner for cleaner test output
  main:
    banner-mode: off
    
  # Test-specific logging
  logging:
    level:
      ai.twodot.platform.agents.resourcemanager: DEBUG
      org.springframework: WARN
      org.mockito: WARN
    pattern:
      console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# Resource Manager Agent Configuration
resource-manager:
  # Test environment settings
  environment: test
  
  # Capacity prediction configuration
  capacity-prediction:
    python-service-url: http://localhost:8086
    connection-timeout: 5000
    read-timeout: 10000
    fallback-enabled: true
    cache-ttl-minutes: 30
    
  # Multi-cloud provider configuration
  cloud-providers:
    aws:
      enabled: true
      test-mode: true
      region: us-east-1
      instance-types:
        - m5.large
        - c5.large
        - r5.large
      connection-timeout: 5000
      
    gcp:
      enabled: true
      test-mode: true
      region: us-central1
      machine-types:
        - n1-standard-2
        - n1-standard-4
      connection-timeout: 5000
      
    azure:
      enabled: false  # Disabled for testing
      test-mode: true
      
    kubernetes:
      enabled: true
      test-mode: true
      namespace: test
      
  # Auto-scaling configuration
  auto-scaling:
    enabled: true
    default-cpu-threshold: 70.0
    default-memory-threshold: 80.0
    scale-up-cooldown-seconds: 300
    scale-down-cooldown-seconds: 600
    max-scaling-events-per-hour: 10
    
  # Cost optimization
  cost-optimization:
    enabled: true
    target-savings-percentage: 20.0
    recommendations-enabled: true
    
  # Monitoring configuration
  monitoring:
    health-check-interval-seconds: 30
    metrics-collection-interval-seconds: 60
    alert-thresholds:
      cpu-utilization: 90.0
      memory-utilization: 90.0
      error-rate: 5.0
      
  # Testing specific settings
  testing:
    # Use mock cloud providers in tests
    use-mock-providers: true
    
    # Simulate network delays
    simulate-network-delays: false
    
    # Fast timeouts for tests
    fast-timeouts: true
    
    # Mock data generation
    generate-mock-metrics: true
    mock-metrics-count: 48
    
# Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,metrics,info
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      simple:
        enabled: true
        
# Test database (if needed)
# spring:
#   datasource:
#     url: jdbc:h2:mem:testdb
#     driver-class-name: org.h2.Driver
#     username: sa
#     password: 
#   jpa:
#     database-platform: org.hibernate.dialect.H2Dialect
#     hibernate:
#       ddl-auto: create-drop