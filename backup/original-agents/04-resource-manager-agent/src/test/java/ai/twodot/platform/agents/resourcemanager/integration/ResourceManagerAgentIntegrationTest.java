/*
package ai.twodot.platform.agents.resourcemanager.integration;

import ai.twodot.platform.agents.resourcemanager.ResourceManagerAgentApplication;
import ai.twodot.platform.agents.resourcemanager.resource.ResourceAllocation.*;
import ai.twodot.platform.agents.resourcemanager.agent.ResourceManagerAgent;
import ai.twodot.platform.agents.resourcemanager.agent.ResourceManagerAgent.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = ResourceManagerAgentApplication.class)
@ActiveProfiles("test")
class ResourceManagerAgentIntegrationTest {

    @Autowired
    private ResourceManagerAgent resourceManagerAgent;

    private AllocationRequest testRequest;

    @BeforeEach
    void setUp() {
        testRequest = createTestAllocationRequest();
    }

    @Test
    void testCompleteResourceAllocationWorkflow() throws Exception {
        // Act - Execute complete allocation workflow
        CompletableFuture<ResourceAllocationResult> future = 
            resourceManagerAgent.processResourceAllocation(testRequest);
        
        ResourceAllocationResult result = future.get(30, TimeUnit.SECONDS);

        // Assert
        assertNotNull(result);
        // assertEquals(testRequest.allocationId(), result.allocationId());
        // assertEquals(testRequest.serviceId(), result.serviceId());
        
        assertNotNull(result.allocationResult());
        assertNotNull(result.capacityPrediction());
        assertNotNull(result.costOptimization());
        assertNotNull(result.deploymentPlan());
        
        // Verify allocation was successful
        AllocationResult allocation = result.allocationResult();
        assertEquals(AllocationStatus.ALLOCATED, allocation.status());
        assertNull(allocation.errorMessage());
        
        // Verify capacity prediction
        CapacityPrediction prediction = result.capacityPrediction();
        assertEquals(testRequest.serviceId(), prediction.serviceId());
        // assertNotNull(prediction.prediction());
        
        // Verify cost optimization
        CostOptimizationRecommendation costOpt = result.costOptimization();
        assertEquals(testRequest.serviceId(), costOpt.serviceId());
        assertNotNull(costOpt.recommendations());
        
        // Verify deployment plan
        MultiCloudDeploymentPlan plan = result.deploymentPlan();
        assertEquals(testRequest.serviceId(), plan.serviceId());
        assertNotNull(plan.plan());
    }

    @Test
    void testAutoScalingWorkflow() throws Exception {
        // Arrange - First allocate resources
        CompletableFuture<ResourceAllocationResult> allocationFuture = 
            resourceManagerAgent.processResourceAllocation(testRequest);
        
        ResourceAllocationResult allocationResult = allocationFuture.get(30, TimeUnit.SECONDS);
        assertNotNull(allocationResult);
        
        // String allocationId = allocationResult.allocationId();
        
        // Act - Execute auto-scaling
        // CompletableFuture<AutoScalingResult> scalingFuture = 
        //     resourceManagerAgent.executeAutoScaling(allocationId);
        
        // AutoScalingResult scalingResult = scalingFuture.get(30, TimeUnit.SECONDS);

        // Assert
        // assertNotNull(scalingResult);
        // assertEquals(allocationId, scalingResult.allocationId());
        // assertNotNull(scalingResult.resourceHealth());
        // assertNotNull(scalingResult.scalingRecommendation());
        // assertNotNull(scalingResult.scalingDecision());
        
        // Verify scaling decision
        // ScalingDecision decision = scalingResult.scalingDecision();
        // assertTrue(decision.confidence() > 0);
        // assertNotNull(decision.reason());
        
        // Verify health monitoring
        // ResourceHealth health = scalingResult.resourceHealth();
        // assertTrue(health.cpuUtilization() >= 0 && health.cpuUtilization() <= 100);
        // assertTrue(health.memoryUtilization() >= 0 && health.memoryUtilization() <= 100);
    }

    @Test
    void testCostOptimizationWorkflow() throws Exception {
        // Act
        CompletableFuture<CostOptimizationResult> future = 
            resourceManagerAgent.optimizeCosts(testRequest.serviceId());
        
        CostOptimizationResult result = future.get(30, TimeUnit.SECONDS);

        // Assert
        assertNotNull(result);
        // assertEquals(testRequest.allocationId(), result.allocationId());
        // assertEquals(testRequest.serviceId(), result.serviceId());
        
        // assertNotNull(result.capacityPrediction());
        // assertNotNull(result.costOptimization());
        
        // Verify cost optimization recommendations
        // CostOptimizationRecommendation optimization = result.costOptimization();
        // Map<String, Object> recommendations = optimization.recommendations();
        
        // assertTrue(recommendations.containsKey("right_sizing"));
        // assertTrue(recommendations.containsKey("reserved_instances"));
        // assertTrue(recommendations.containsKey("auto_scaling"));
        // assertTrue(recommendations.containsKey("storage_optimization"));
        
        // Each recommendation should have structure
        // for (String key : recommendations.keySet()) {
        //     Map<String, Object> recommendation = (Map<String, Object>) recommendations.get(key);
        //     assertTrue(recommendation.containsKey("recommended"));
        //     assertTrue(recommendation.containsKey("potential_savings"));
        //     assertTrue(recommendation.containsKey("description"));
        // }
    }

    @Test
    void testHighPriorityRequestProcessing() throws Exception {
        // Arrange - Create high priority request
        AllocationRequest highPriorityRequest = createHighPriorityAllocationRequest();

        // Act
        CompletableFuture<ResourceAllocationResult> future = 
            resourceManagerAgent.processResourceAllocation(highPriorityRequest);
        
        ResourceAllocationResult result = future.get(30, TimeUnit.SECONDS);

        // Assert
        assertNotNull(result);
        // assertEquals(highPriorityRequest.allocationId(), result.allocationId());
        
        // High priority requests should be processed successfully
        AllocationResult allocation = result.allocationResult();
        assertEquals(AllocationStatus.ALLOCATED, allocation.status());
    }

    @Test
    void testMultipleSimultaneousRequests() throws Exception {
        // Arrange - Create multiple requests
        List<AllocationRequest> requests = Arrays.asList(
            createTestAllocationRequest(),
            createTestAllocationRequest(),
            createTestAllocationRequest()
        );

        // Act - Process all requests simultaneously
        List<CompletableFuture<ResourceAllocationResult>> futures = new ArrayList<>();
        for (AllocationRequest request : requests) {
            futures.add(resourceManagerAgent.processResourceAllocation(request));
        }

        // Wait for all to complete
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
            futures.toArray(new CompletableFuture[0])
        );
        allFutures.get(60, TimeUnit.SECONDS);

        // Assert - All requests should be processed
        for (CompletableFuture<ResourceAllocationResult> future : futures) {
            ResourceAllocationResult result = future.get();
            assertNotNull(result);
            assertEquals(AllocationStatus.ALLOCATED, result.allocationResult().status());
        }
    }

    @Test
    void testResourceManagerMetrics() {
        // Act
        // Map<String, Object> metrics = resourceManagerAgent.getMetrics();

        // Assert
        // assertNotNull(metrics);
        // assertTrue(metrics.containsKey("total_allocation_requests"));
        // assertTrue(metrics.containsKey("successful_allocations"));
        // assertTrue(metrics.containsKey("failed_allocations"));
        // assertTrue(metrics.containsKey("average_allocation_time_ms"));
        // assertTrue(metrics.containsKey("active_allocations"));
        // assertTrue(metrics.containsKey("auto_scaling_events"));
        // assertTrue(metrics.containsKey("cost_optimizations_performed"));
        // assertTrue(metrics.containsKey("last_updated"));
        
        // Verify metric types
        // assertTrue(metrics.get("total_allocation_requests") instanceof Number);
        // assertTrue(metrics.get("successful_allocations") instanceof Number);
        // assertTrue(metrics.get("failed_allocations") instanceof Number);
    }

    @Test
    void testServiceHealthCheck() {
        // Act
        // boolean isHealthy = resourceManagerAgent.isHealthy();

        // Assert
        // assertTrue(isHealthy, "ResourceManagerAgent should be healthy in test environment");
    }

    @Test
    void testInvalidAllocationRequest() throws Exception {
        // Arrange - Create invalid request
        AllocationRequest invalidRequest = createInvalidAllocationRequest();

        // Act
        CompletableFuture<ResourceAllocationResult> future = 
            resourceManagerAgent.processResourceAllocation(invalidRequest);
        
        ResourceAllocationResult result = future.get(30, TimeUnit.SECONDS);

        // Assert - Should handle gracefully
        assertNotNull(result);
        AllocationResult allocation = result.allocationResult();
        
        // Should either succeed with warnings or fail gracefully
        assertTrue(allocation.status() == AllocationStatus.ALLOCATED || 
                  allocation.status() == AllocationStatus.FAILED);
    }

    // Helper methods

    private AllocationRequest createTestAllocationRequest() {
        CpuRequirement cpu = new CpuRequirement(2.0, 8.0, 70.0, "x86_64", "x86_64");
        MemoryRequirement memory = new MemoryRequirement(4.0, 16.0, "DDR4", 80.0);
        StorageRequirement storage = new StorageRequirement(20.0, 100.0, StorageType.SSD, 1000, 100, true);
        NetworkRequirement network = new NetworkRequirement(100.0, 1000.0, 10.0, 2, true);
        GpuRequirement gpu = new GpuRequirement(0, "", 0.0, "");
        ScalingRequirement scaling = new ScalingRequirement(1, 10, 70.0, 80.0, 300, 600, true, false);
        
        ResourceRequirements requirements = new ResourceRequirements(cpu, memory, storage, network, gpu, scaling);
        
        BudgetConstraint budget = new BudgetConstraint(100.0, 2000.0, "USD", "development", 0.8, "alert");
        PerformanceSla sla = new PerformanceSla(99.5, 200.0, 1.0, 1000.0, 0.9);
        SustainabilityGoals sustainability = new SustainabilityGoals(500.0, true, false, true);
        
        AllocationConstraints constraints = new AllocationConstraints(
            List.of(CloudProvider.AWS), List.of(), List.of("us-east-1"), List.of(),
            budget, List.of(), sla, sustainability, List.of(), List.of()
        );
        
        return new AllocationRequest(
            UUID.randomUUID().toString(),
            "test-service-" + System.currentTimeMillis(),
            requirements,
            constraints,
            AllocationPriority.NORMAL,
            Map.of("test", "true", "integration", "true"),
            Instant.now()
        );
    }

    private AllocationRequest createHighPriorityAllocationRequest() {
        AllocationRequest baseRequest = createTestAllocationRequest();
        
        return new AllocationRequest(
            UUID.randomUUID().toString(),
            "high-priority-service-" + System.currentTimeMillis(),
            baseRequest.requirements(),
            baseRequest.constraints(),
            AllocationPriority.HIGH,
            Map.of("priority", "high", "urgent", "true"),
            Instant.now()
        );
    }

    private AllocationRequest createInvalidAllocationRequest() {
        // Create request with unrealistic resource requirements
        CpuRequirement cpu = new CpuRequirement(1000.0, 2000.0, 70.0, "x86_64", "x86_64");
        MemoryRequirement memory = new MemoryRequirement(10000.0, 20000.0, "DDR4", 80.0);
        StorageRequirement storage = new StorageRequirement(100000.0, 200000.0, StorageType.SSD, 1000, 100, true);
        NetworkRequirement network = new NetworkRequirement(100000.0, 200000.0, 1.0, 10, true);
        GpuRequirement gpu = new GpuRequirement(100, "SuperGPU", 10000.0, "99.0");
        ScalingRequirement scaling = new ScalingRequirement(1000, 10000, 70.0, 80.0, 300, 600, true, false);
        
        ResourceRequirements requirements = new ResourceRequirements(cpu, memory, storage, network, gpu, scaling);
        
        AllocationConstraints constraints = new AllocationConstraints(
            List.of(), List.of(), List.of("invalid-region"), List.of(),
            null, List.of(), null, null, List.of(), List.of()
        );
        
        return new AllocationRequest(
            UUID.randomUUID().toString(),
            "invalid-service",
            requirements,
            constraints,
            AllocationPriority.NORMAL,
            Map.of("invalid", "true"),
            Instant.now()
        );
    }
}
*/