package ai.twodot.platform.agents.resourcemanager.cloud;

import ai.twodot.platform.agents.resourcemanager.resource.ResourceAllocation.*;
import ai.twodot.platform.agents.resourcemanager.agent.ResourceManagerAgent.MultiCloudDeploymentPlan;
import ai.twodot.platform.agents.resourcemanager.cloud.CloudProviderInterface.*;
import ai.twodot.platform.agents.resourcemanager.cloud.MultiCloudManager.*;

import software.amazon.awssdk.services.ec2.Ec2Client;
import software.amazon.awssdk.services.ec2.model.*;
import software.amazon.awssdk.services.autoscaling.AutoScalingClient;
import software.amazon.awssdk.services.autoscaling.model.*;
import software.amazon.awssdk.services.cloudwatch.CloudWatchClient;
import software.amazon.awssdk.services.costexplorer.CostExplorerClient;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.Instant;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for AwsCloudProvider
 */
@ExtendWith(MockitoExtension.class)
class AwsCloudProviderTest {

    @Mock
    private Ec2Client ec2Client;

    @Mock
    private AutoScalingClient autoScalingClient;

    @Mock
    private CloudWatchClient cloudWatchClient;

    @Mock
    private CostExplorerClient costExplorerClient;

    @InjectMocks
    private AwsCloudProvider awsCloudProvider;

    private AllocationRequest testRequest;

    @BeforeEach
    void setUp() {
        // Inject mocked clients
        ReflectionTestUtils.setField(awsCloudProvider, "ec2Client", ec2Client);
        ReflectionTestUtils.setField(awsCloudProvider, "autoScalingClient", autoScalingClient);
        ReflectionTestUtils.setField(awsCloudProvider, "cloudWatchClient", cloudWatchClient);
        ReflectionTestUtils.setField(awsCloudProvider, "costExplorerClient", costExplorerClient);

        testRequest = createTestAllocationRequest();
        
        // Mock successful AWS API responses
        mockAwsApiResponses();
        
        // Initialize the provider
        awsCloudProvider.initialize();
    }

    @Test
    void testInitialization() {
        // Verify that instance types and regions are loaded
        Map<String, ai.twodot.platform.agents.resourcemanager.cloud.CloudProviderInterface.InstanceTypeInfo> instanceTypes = awsCloudProvider.getAvailableInstanceTypes();
        assertNotNull(instanceTypes);
        assertFalse(instanceTypes.isEmpty());
        assertTrue(instanceTypes.containsKey("m5.large"));

        Map<String, RegionInfo> regions = awsCloudProvider.getAvailableRegions();
        assertNotNull(regions);
        assertFalse(regions.isEmpty());
        assertTrue(regions.containsKey("us-east-1"));
    }

    @Test
    void testTestConnectionSuccess() {
        // Arrange
        DescribeRegionsResponse regionsResponse = DescribeRegionsResponse.builder()
            .regions(Region.builder().regionName("us-east-1").build(),
                    Region.builder().regionName("us-west-2").build())
            .build();
        
        when(ec2Client.describeRegions()).thenReturn(regionsResponse);

        // Act
        boolean connected = awsCloudProvider.testConnection();

        // Assert
        assertTrue(connected);
        verify(ec2Client).describeRegions();
    }

    @Test
    void testTestConnectionFailure() {
        // Arrange
        when(ec2Client.describeRegions()).thenThrow(new RuntimeException("AWS API error"));

        // Act
        boolean connected = awsCloudProvider.testConnection();

        // Assert
        assertFalse(connected);
        verify(ec2Client).describeRegions();
    }

    @Test
    void testEstimateDeployment() {
        // Act
        CloudDeploymentOption option = awsCloudProvider.estimateDeployment(testRequest);

        // Assert
        assertNotNull(option);
        assertEquals(CloudProvider.AWS, option.cloudProvider());
        assertEquals("us-east-1", option.region());
        assertNotNull(option.instanceType());
        assertTrue(option.estimatedCost() > 0);
        assertTrue(option.performanceScore() >= 0 && option.performanceScore() <= 100);
        assertTrue(option.reliabilityScore() >= 0 && option.reliabilityScore() <= 100);
        assertTrue(option.sustainabilityScore() >= 0 && option.sustainabilityScore() <= 100);
        assertEquals("auto_scaling_group", option.deploymentStrategy());
        assertNotNull(option.details());
    }

    @Test
    void testAllocateResourcesSuccess() {
        // Arrange
        MultiCloudDeploymentPlan deploymentPlan = new MultiCloudDeploymentPlan("test-service", 
            Map.of("instance_type", "m5.large", "instance_count", 2));

        // Act
        CloudAllocationResult result = awsCloudProvider.allocateResources(testRequest, deploymentPlan);

        // Assert
        assertNotNull(result);
        assertEquals("us-east-1", result.region());
        assertEquals("us-east-1a", result.availabilityZone());
        assertNotNull(result.instanceType());
        assertTrue(result.instanceCount() > 0);
        assertTrue(result.cpuCores() > 0);
        assertTrue(result.memoryGb() > 0);
        assertTrue(result.storageGb() > 0);
        assertTrue(result.hourlyCost() > 0);
        assertNotNull(result.resourceIds());
        assertFalse(result.resourceIds().isEmpty());
    }

    @Test
    void testDeallocateResourcesSuccess() {
        // Arrange
        String allocationId = "test-allocation-123";
        
        // First allocate resources to have something to deallocate
        MultiCloudDeploymentPlan deploymentPlan = new MultiCloudDeploymentPlan("test-service", 
            Map.of("instance_type", "m5.large", "instance_count", 1));
        awsCloudProvider.allocateResources(testRequest, deploymentPlan);

        // Mock successful deletion
        when(autoScalingClient.deleteAutoScalingGroup(any(DeleteAutoScalingGroupRequest.class)))
            .thenReturn(DeleteAutoScalingGroupResponse.builder().build());

        // Act
        boolean result = awsCloudProvider.deallocateResources(allocationId);

        // Assert
        assertTrue(result);
    }

    @Test
    void testDeallocateResourcesNotFound() {
        // Act
        boolean result = awsCloudProvider.deallocateResources("non-existent-allocation");

        // Assert
        assertFalse(result);
    }

    @Test
    void testScaleResourcesSuccess() {
        // Arrange
        String allocationId = "test-allocation-123";
        int targetInstanceCount = 5;
        
        // First allocate resources
        MultiCloudDeploymentPlan deploymentPlan = new MultiCloudDeploymentPlan("test-service", 
            Map.of("instance_type", "m5.large", "instance_count", 2));
        awsCloudProvider.allocateResources(testRequest, deploymentPlan);

        // Mock successful scaling
        when(autoScalingClient.updateAutoScalingGroup(any(UpdateAutoScalingGroupRequest.class)))
            .thenReturn(UpdateAutoScalingGroupResponse.builder().build());

        // Act
        CloudAllocationResult result = awsCloudProvider.scaleResources(allocationId, targetInstanceCount);

        // Assert
        assertNotNull(result);
        assertEquals(targetInstanceCount, result.instanceCount());
        assertTrue(result.cpuCores() > 0);
        assertTrue(result.memoryGb() > 0);
    }

    @Test
    void testScaleResourcesNotFound() {
        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            awsCloudProvider.scaleResources("non-existent-allocation", 5);
        });
    }

    @Test
    void testGetResourceMetricsSuccess() {
        // Arrange
        String allocationId = "test-allocation-123";
        
        // First allocate resources
        MultiCloudDeploymentPlan deploymentPlan = new MultiCloudDeploymentPlan("test-service", 
            Map.of("instance_type", "m5.large", "instance_count", 2));
        awsCloudProvider.allocateResources(testRequest, deploymentPlan);

        // Mock Auto Scaling Group response
        AutoScalingGroup asg = AutoScalingGroup.builder()
            .autoScalingGroupName("test-asg")
            .desiredCapacity(2)
            .instances(
                software.amazon.awssdk.services.autoscaling.model.Instance.builder().instanceId("i-1234567890abcdef0").lifecycleState("InService").build(),
                software.amazon.awssdk.services.autoscaling.model.Instance.builder().instanceId("i-abcdef1234567890").lifecycleState("InService").build()
            )
            .availabilityZones("us-east-1a", "us-east-1b")
            .createdTime(Instant.now())
            .build();

        DescribeAutoScalingGroupsResponse asgResponse = DescribeAutoScalingGroupsResponse.builder()
            .autoScalingGroups(asg)
            .build();

        when(autoScalingClient.describeAutoScalingGroups(any(DescribeAutoScalingGroupsRequest.class)))
            .thenReturn(asgResponse);

        // Act
        Map<String, Object> metrics = awsCloudProvider.getResourceMetrics(allocationId);

        // Assert
        assertNotNull(metrics);
        assertEquals(2, metrics.get("desired_capacity"));
        assertEquals(2, metrics.get("current_capacity"));
        assertEquals(2L, metrics.get("healthy_instances"));
        assertEquals("Running", metrics.get("status"));
    }

    @Test
    void testGetResourceMetricsNotFound() {
        // Act
        Map<String, Object> metrics = awsCloudProvider.getResourceMetrics("non-existent-allocation");

        // Assert
        assertNotNull(metrics);
        assertTrue(metrics.containsKey("error"));
    }

    @Test
    void testGetProviderMetrics() {
        // Act
        Map<String, Object> metrics = awsCloudProvider.getProviderMetrics();

        // Assert
        assertNotNull(metrics);
        assertEquals("AWS", metrics.get("provider"));
        assertTrue((Integer) metrics.get("regions_available") > 0);
        assertTrue((Integer) metrics.get("instance_types_available") > 0);
        assertTrue(metrics.containsKey("active_allocations"));
        assertTrue(metrics.containsKey("connection_status"));
        assertTrue(metrics.containsKey("last_updated"));
    }

    @Test
    void testValidateAllocationRequestValid() {
        // Act
        ValidationResult result = awsCloudProvider.validateAllocationRequest(testRequest);

        // Assert
        assertTrue(result.valid());
        assertNull(result.errorMessage());
    }

    @Test
    void testValidateAllocationRequestWithWarnings() {
        // Arrange - create request with high resource requirements
        AllocationRequest highResourceRequest = createHighResourceAllocationRequest();

        // Act
        ValidationResult result = awsCloudProvider.validateAllocationRequest(highResourceRequest);

        // Assert
        assertTrue(result.valid());
        assertNull(result.errorMessage());
        assertFalse(result.warnings().isEmpty());
    }

    @Test
    void testValidateAllocationRequestInvalidRegion() {
        // Arrange - create request with invalid region
        AllocationRequest invalidRegionRequest = createInvalidRegionAllocationRequest();

        // Act
        ValidationResult result = awsCloudProvider.validateAllocationRequest(invalidRegionRequest);

        // Assert
        assertFalse(result.valid());
        assertNotNull(result.errorMessage());
        assertTrue(result.errorMessage().contains("Invalid region"));
    }

    @Test
    void testEstimateCost() {
        // Act
        ai.twodot.platform.agents.resourcemanager.cloud.CloudProviderInterface.CostEstimate estimate = awsCloudProvider.estimateCost("m5.large", 2, "us-east-1");

        // Assert
        assertNotNull(estimate);
        assertTrue(estimate.hourlyCost() > 0);
        assertTrue(estimate.dailyCost() > estimate.hourlyCost());
        assertTrue(estimate.monthlyCost() > estimate.dailyCost());
        assertEquals("USD", estimate.currency());
        assertNotNull(estimate.costBreakdown());
        assertTrue(estimate.spotInstanceSavings() > 0);
        assertTrue(estimate.reservedInstanceSavings() > 0);
    }

    @Test
    void testGetAvailableInstanceTypes() {
        // Act
        Map<String, ai.twodot.platform.agents.resourcemanager.cloud.CloudProviderInterface.InstanceTypeInfo> instanceTypes = awsCloudProvider.getAvailableInstanceTypes();

        // Assert
        assertNotNull(instanceTypes);
        assertFalse(instanceTypes.isEmpty());
        
        // Check specific instance types
        assertTrue(instanceTypes.containsKey("m5.large"));
        assertTrue(instanceTypes.containsKey("c5.large"));
        assertTrue(instanceTypes.containsKey("r5.large"));
        
        ai.twodot.platform.agents.resourcemanager.cloud.CloudProviderInterface.InstanceTypeInfo m5Large = instanceTypes.get("m5.large");
        assertEquals("m5.large", m5Large.instanceType());
        assertEquals(2.0, m5Large.cpuCores());
        assertEquals(8.0, m5Large.memoryGb());
        assertTrue(m5Large.hourlyCost() > 0);
    }

    @Test
    void testGetAvailableRegions() {
        // Act
        Map<String, RegionInfo> regions = awsCloudProvider.getAvailableRegions();

        // Assert
        assertNotNull(regions);
        assertFalse(regions.isEmpty());
        
        assertTrue(regions.containsKey("us-east-1"));
        assertTrue(regions.containsKey("us-west-2"));
        assertTrue(regions.containsKey("eu-west-1"));
        
        RegionInfo usEast1 = regions.get("us-east-1");
        assertEquals("us-east-1", usEast1.regionCode());
        assertEquals("US East (N. Virginia)", usEast1.regionName());
        assertTrue(usEast1.availabilityZoneSupport());
        assertTrue(usEast1.availabilityZoneCount() > 0);
    }

    // Helper methods

    private void mockAwsApiResponses() {
        // Mock describe regions response
        DescribeRegionsResponse regionsResponse = DescribeRegionsResponse.builder()
            .regions(
                Region.builder().regionName("us-east-1").build(),
                Region.builder().regionName("us-west-2").build()
            )
            .build();
        when(ec2Client.describeRegions()).thenReturn(regionsResponse);
    }

    private AllocationRequest createTestAllocationRequest() {
        CpuRequirement cpu = new CpuRequirement(2.0, 8.0, 70.0, "x86_64", "x86_64");
        MemoryRequirement memory = new MemoryRequirement(4.0, 16.0, "DDR4", 80.0);
        StorageRequirement storage = new StorageRequirement(20.0, 100.0, StorageType.SSD, 1000, 100, true);
        NetworkRequirement network = new NetworkRequirement(100.0, 1000.0, 10.0, 2, true);
        GpuRequirement gpu = new GpuRequirement(0, "", 0.0, "");
        ScalingRequirement scaling = new ScalingRequirement(1, 10, 70.0, 80.0, 300, 600, true, false);
        
        ResourceRequirements requirements = new ResourceRequirements(cpu, memory, storage, network, gpu, scaling);
        
        BudgetConstraint budget = new BudgetConstraint(100.0, 2000.0, "USD", "development", 0.8, "alert");
        PerformanceSla sla = new PerformanceSla(99.5, 200.0, 1.0, 1000.0, 0.9);
        SustainabilityGoals sustainability = new SustainabilityGoals(500.0, true, false, true);
        
        AllocationConstraints constraints = new AllocationConstraints(
            List.of(CloudProvider.AWS), List.of(), List.of("us-east-1"), List.of(),
            budget, List.of(), sla, sustainability, List.of(), List.of()
        );
        
        return new AllocationRequest(
            "test-allocation-123",
            "test-service",
            requirements,
            constraints,
            AllocationPriority.NORMAL,
            Map.of("test", "true"),
            Instant.now()
        );
    }

    private AllocationRequest createHighResourceAllocationRequest() {
        CpuRequirement cpu = new CpuRequirement(64.0, 128.0, 70.0, "x86_64", "x86_64");
        MemoryRequirement memory = new MemoryRequirement(512.0, 1024.0, "DDR4", 80.0);
        StorageRequirement storage = new StorageRequirement(1000.0, 10000.0, StorageType.SSD, 10000, 1000, true);
        NetworkRequirement network = new NetworkRequirement(1000.0, 10000.0, 1.0, 3, true);
        GpuRequirement gpu = new GpuRequirement(4, "V100", 32.0, "11.0");
        ScalingRequirement scaling = new ScalingRequirement(10, 100, 70.0, 80.0, 300, 600, true, false);
        
        ResourceRequirements requirements = new ResourceRequirements(cpu, memory, storage, network, gpu, scaling);
        
        AllocationConstraints constraints = new AllocationConstraints(
            List.of(CloudProvider.AWS), List.of(), List.of("us-east-1"), List.of(),
            null, List.of(), null, null, List.of(), List.of()
        );
        
        return new AllocationRequest(
            "high-resource-allocation",
            "high-resource-service",
            requirements,
            constraints,
            AllocationPriority.HIGH,
            Map.of(),
            Instant.now()
        );
    }

    private AllocationRequest createInvalidRegionAllocationRequest() {
        AllocationRequest baseRequest = createTestAllocationRequest();
        
        AllocationConstraints invalidConstraints = new AllocationConstraints(
            List.of(CloudProvider.AWS), 
            List.of(), 
            List.of("invalid-region-123"), // Invalid region
            List.of(),
            baseRequest.constraints().budgetLimit(),
            baseRequest.constraints().complianceRequirements(),
            baseRequest.constraints().performanceSla(),
            baseRequest.constraints().sustainabilityGoals(),
            List.of(),
            List.of()
        );
        
        return new AllocationRequest(
            baseRequest.allocationId(),
            baseRequest.serviceId(),
            baseRequest.requirements(),
            invalidConstraints,
            baseRequest.priority(),
            baseRequest.metadata(),
            baseRequest.requestedAt()
        );
    }
}