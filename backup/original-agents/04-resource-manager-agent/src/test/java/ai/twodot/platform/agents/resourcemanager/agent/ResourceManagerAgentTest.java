/*
package ai.twodot.platform.agents.resourcemanager.agent;

import ai.twodot.platform.agents.resourcemanager.resource.ResourceAllocation.*;
import ai.twodot.platform.agents.resourcemanager.scaling.AutoScalingService;
import ai.twodot.platform.agents.resourcemanager.cloud.MultiCloudManager;
import ai.twodot.platform.agents.resourcemanager.optimization.CostOptimizationService;
import ai.twodot.platform.agents.resourcemanager.monitoring.ResourceMonitoringService;
import ai.twodot.platform.agents.resourcemanager.ai.CapacityPredictionService;
import ai.twodot.platform.agents.resourcemanager.agent.ResourceManagerAgent.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ResourceManagerAgentTest {

    @Mock
    private AutoScalingService autoScalingService;

    @Mock
    private MultiCloudManager multiCloudManager;

    @Mock
    private CostOptimizationService costOptimizationService;

    @Mock
    private ResourceMonitoringService monitoringService;

    @Mock
    private CapacityPredictionService capacityPredictionService;

    @InjectMocks
    private ResourceManagerAgent resourceManagerAgent;

    private AllocationRequest testRequest;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(resourceManagerAgent, "agentId", "RMA-004-TEST");
        ReflectionTestUtils.setField(resourceManagerAgent, "agentName", "Test Resource Manager Agent");
        ReflectionTestUtils.setField(resourceManagerAgent, "agentVersion", "1.0.0-TEST");

        testRequest = createTestAllocationRequest();

        resourceManagerAgent.initialize();
    }

    @Test
    void testAgentInitialization() {
        AgentHealthStatus health = resourceManagerAgent.getHealthStatus();
        assertEquals("RMA-004-TEST", health.agentId());
        assertEquals("HEALTHY", health.status());
        assertTrue(health.uptimeSeconds() >= 0);
    }

    @Test
    void testProcessResourceAllocationSuccess() throws Exception {
        CapacityPrediction capacityPrediction = new CapacityPrediction("test-service", 
            Map.of("cpu_prediction", 70.0, "memory_prediction", 60.0));
        
        CostOptimizationRecommendation costOptimization = new CostOptimizationRecommendation("test-service", 
            Map.of("recommended_instance", "m5.large", "estimated_savings", 25.0));
        
        MultiCloudDeploymentPlan deploymentPlan = new MultiCloudDeploymentPlan("test-service", 
            Map.of("optimal_provider", "AWS", "region", "us-east-1"));
        
        AllocationResult allocationResult = new AllocationResult(
            testRequest.allocationId(),
            AllocationStatus.ALLOCATED,
            createTestAllocatedResources(),
            createTestCostEstimate(),
            createTestPerformancePrediction(),
            createTestCarbonFootprint(),
            150L,
            Instant.now(),
            null
        );

        when(capacityPredictionService.predictCapacity(anyString(), anyInt()))
            .thenReturn(CompletableFuture.completedFuture(capacityPrediction));
        
        when(costOptimizationService.optimizeAllocation(testRequest))
            .thenReturn(CompletableFuture.completedFuture(costOptimization));
        
        when(multiCloudManager.planOptimalDeployment(testRequest))
            .thenReturn(CompletableFuture.completedFuture(deploymentPlan));
        
        when(multiCloudManager.executeAllocation(eq(testRequest), eq(capacityPrediction), 
            eq(costOptimization), eq(deploymentPlan)))
            .thenReturn(allocationResult);

        CompletableFuture<ResourceAllocationResult> result = 
            resourceManagerAgent.processResourceAllocation(testRequest);
        
        ResourceAllocationResult allocationResponse = result.get(5, TimeUnit.SECONDS);

        assertNotNull(allocationResponse);
        assertTrue(allocationResponse.success());
        assertEquals(allocationResult, allocationResponse.allocationResult());
        assertEquals(capacityPrediction, allocationResponse.capacityPrediction());
        assertEquals(costOptimization, allocationResponse.costOptimization());
        assertEquals(deploymentPlan, allocationResponse.deploymentPlan());
        assertTrue(allocationResponse.processingTimeMs() > 0);

        verify(capacityPredictionService).predictCapacity(anyString(), anyInt());
        verify(costOptimizationService).optimizeAllocation(testRequest);
        verify(multiCloudManager).planOptimalDeployment(testRequest);
        verify(multiCloudManager).executeAllocation(testRequest, capacityPrediction, costOptimization, deploymentPlan);
        verify(autoScalingService).configureAutoScaling(allocationResult);
        verify(monitoringService).startMonitoring(allocationResult);
    }

    @Test
    void testProcessResourceAllocationWithInvalidRequest() throws Exception {
        AllocationRequest invalidRequest = new AllocationRequest(
            null,
            "test-service",
            null,
            null,
            AllocationPriority.NORMAL,
            Map.of(),
            Instant.now()
        );

        CompletableFuture<ResourceAllocationResult> result = 
            resourceManagerAgent.processResourceAllocation(invalidRequest);
        
        ResourceAllocationResult allocationResponse = result.get(5, TimeUnit.SECONDS);

        assertNotNull(allocationResponse);
        assertFalse(allocationResponse.success());
        assertTrue(allocationResponse.errorMessage().contains("Invalid request"));
        assertNull(allocationResponse.allocationResult());

        verifyNoInteractions(capacityPredictionService);
        verifyNoInteractions(costOptimizationService);
        verifyNoInteractions(multiCloudManager);
    }

    @Test
    void testProcessResourceAllocationWithServiceFailure() throws Exception {
        when(capacityPredictionService.predictCapacity(anyString(), anyInt()))
            .thenReturn(CompletableFuture.failedFuture(new RuntimeException("Capacity prediction failed")));

        CompletableFuture<ResourceAllocationResult> result = 
            resourceManagerAgent.processResourceAllocation(testRequest);
        
        ResourceAllocationResult allocationResponse = result.get(5, TimeUnit.SECONDS);

        assertNotNull(allocationResponse);
        assertFalse(allocationResponse.success());
        assertTrue(allocationResponse.errorMessage().contains("Allocation failed"));
        assertNull(allocationResponse.allocationResult());
    }

    @Test
    void testExecuteAutoScalingSuccess() throws Exception {
        String serviceId = "test-service";
        ScalingTrigger trigger = new ScalingTrigger("cpu_threshold", 
            Map.of("current_cpu", 85.0, "threshold", 80.0));
        
        ResourceHealth currentHealth = new ResourceHealth(
            serviceId, HealthStatus.WARNING, 85.0, 70.0, 50.0, 30.0, 2.0, 120.0, 
            Instant.now(), 75.0, List.of("High CPU usage")
        );
        
        ScalingRecommendation scalingRecommendation = new ScalingRecommendation(serviceId, 
            Map.of("recommended_action", "scale_up", "target_instances", 5));
        
        ScalingDecision scalingDecision = new ScalingDecision(
            serviceId, 3, 5, ScalingDirection.UP, "High CPU utilization", 0.9,
            Instant.now().plusSeconds(120), 50.0, "Improved performance"
        );

        when(monitoringService.getResourceHealth(serviceId)).thenReturn(currentHealth);
        when(autoScalingService.executeScaling(serviceId, scalingRecommendation, currentHealth))
            .thenReturn(scalingDecision);

        CompletableFuture<ScalingResult> result = 
            resourceManagerAgent.executeAutoScaling(serviceId, trigger);
        
        ScalingResult scalingResponse = result.get(5, TimeUnit.SECONDS);

        assertNotNull(scalingResponse);
        assertTrue(scalingResponse.success());
        assertEquals(scalingDecision, scalingResponse.scalingDecision());
        assertEquals(scalingRecommendation, scalingResponse.recommendation());

        verify(monitoringService).getResourceHealth(serviceId);
        verify(autoScalingService).executeScaling(serviceId, scalingRecommendation, currentHealth);
    }

    @Test
    void testExecuteAutoScalingFailure() throws Exception {
        String serviceId = "test-service";
        ScalingTrigger trigger = new ScalingTrigger("cpu_threshold", Map.of());
        
        when(monitoringService.getResourceHealth(serviceId))
            .thenThrow(new RuntimeException("Monitoring service unavailable"));

        CompletableFuture<ScalingResult> result = 
            resourceManagerAgent.executeAutoScaling(serviceId, trigger);
        
        ScalingResult scalingResponse = result.get(5, TimeUnit.SECONDS);

        assertNotNull(scalingResponse);
        assertFalse(scalingResponse.success());
        assertTrue(scalingResponse.errorMessage().contains("Scaling failed"));
        assertNull(scalingResponse.scalingDecision());
    }

    @Test
    void testOptimizeCostsSuccess() throws Exception {
        String serviceId = "test-service";
        
        CostOptimizationRecommendation recommendations = new CostOptimizationRecommendation(serviceId, 
            Map.of("strategy", "right_sizing", "potential_savings", 300.0));
        
        SavingsCalculation savings = new SavingsCalculation(serviceId, 30.0, 
            Map.of("right_sizing", 200.0, "reserved_instances", 100.0));

        CompletableFuture<CostOptimizationResult> result = 
            resourceManagerAgent.optimizeCosts(serviceId);
        
        CostOptimizationResult optimizationResponse = result.get(5, TimeUnit.SECONDS);

        assertNotNull(optimizationResponse);
        assertTrue(optimizationResponse.success());
    }

    @Test
    void testOptimizeCostsFailure() throws Exception {
        String serviceId = "test-service";
        
        when(costOptimizationService.analyzeCosts(serviceId))
            .thenThrow(new RuntimeException("Cost analysis service unavailable"));

        CompletableFuture<CostOptimizationResult> result = 
            resourceManagerAgent.optimizeCosts(serviceId);
        
        CostOptimizationResult optimizationResponse = result.get(5, TimeUnit.SECONDS);

        assertNotNull(optimizationResponse);
        assertFalse(optimizationResponse.success());
        assertTrue(optimizationResponse.errorMessage().contains("Cost optimization failed"));
        assertNull(optimizationResponse.costAnalysis());
    }

    @Test
    void testGetHealthStatusWhenRunning() {
        AgentHealthStatus health = resourceManagerAgent.getHealthStatus();

        assertNotNull(health);
        assertEquals("RMA-004-TEST", health.agentId());
        assertEquals("HEALTHY", health.status());
        assertTrue(health.uptimeSeconds() >= 0);
        assertEquals(0, health.allocationsProcessed());
        assertEquals(0, health.scalingDecisionsMade());
        assertEquals(0, health.costOptimizationsPerformed());
        assertEquals(0, health.errors());
        assertNotNull(health.serviceStatus());
    }

    @Test
    void testGetHealthStatusAfterShutdown() {
        resourceManagerAgent.shutdown();

        AgentHealthStatus health = resourceManagerAgent.getHealthStatus();

        assertNotNull(health);
        assertEquals("STOPPED", health.status());
    }

    @Test
    void testGetMetrics() {
        AgentMetrics metrics = resourceManagerAgent.getMetrics();

        assertNotNull(metrics);
        assertEquals("RMA-004-TEST", metrics.agentId());
        assertEquals(0, metrics.allocationsProcessed());
        assertEquals(0, metrics.scalingDecisionsMade());
        assertEquals(0, metrics.costOptimizationsPerformed());
        assertEquals(0, metrics.errors());
        assertNotNull(metrics.cloudMetrics());
        assertNotNull(metrics.scalingMetrics());
        assertNotNull(metrics.optimizationMetrics());
        assertNotNull(metrics.monitoringMetrics());
    }

    @Test
    void testProcessResourceAllocationWhenAgentStopped() throws Exception {
        resourceManagerAgent.shutdown();

        CompletableFuture<ResourceAllocationResult> result = 
            resourceManagerAgent.processResourceAllocation(testRequest);
        
        ResourceAllocationResult allocationResponse = result.get(5, TimeUnit.SECONDS);

        assertNotNull(allocationResponse);
        assertFalse(allocationResponse.success());
        assertEquals("Agent is not running", allocationResponse.errorMessage());
    }

    @Test
    void testExecuteAutoScalingWhenAgentStopped() throws Exception {
        resourceManagerAgent.shutdown();
        String serviceId = "test-service";
        ScalingTrigger trigger = new ScalingTrigger("cpu_threshold", Map.of());

        CompletableFuture<ScalingResult> result = 
            resourceManagerAgent.executeAutoScaling(serviceId, trigger);
        
        ScalingResult scalingResponse = result.get(5, TimeUnit.SECONDS);

        assertNotNull(scalingResponse);
        assertFalse(scalingResponse.success());
        assertEquals("Agent is not running", scalingResponse.errorMessage());
    }

    @Test
    void testOptimizeCostsWhenAgentStopped() throws Exception {
        resourceManagerAgent.shutdown();
        String serviceId = "test-service";

        CompletableFuture<CostOptimizationResult> result = 
            resourceManagerAgent.optimizeCosts(serviceId);
        
        CostOptimizationResult optimizationResponse = result.get(5, TimeUnit.SECONDS);

        assertNotNull(optimizationResponse);
        assertFalse(optimizationResponse.success());
        assertEquals("Agent is not running", optimizationResponse.errorMessage());
    }

    private AllocationRequest createTestAllocationRequest() {
        CpuRequirement cpu = new CpuRequirement(2.0, 8.0, 70.0, "x86_64", "x86_64");
        MemoryRequirement memory = new MemoryRequirement(4.0, 16.0, "DDR4", 80.0);
        StorageRequirement storage = new StorageRequirement(20.0, 100.0, StorageType.SSD, 1000, 100, true);
        NetworkRequirement network = new NetworkRequirement(100.0, 1000.0, 10.0, 2, true);
        GpuRequirement gpu = new GpuRequirement(0, "", 0.0, "");
        ScalingRequirement scaling = new ScalingRequirement(1, 10, 70.0, 80.0, 300, 600, true, false);
        
        ResourceRequirements requirements = new ResourceRequirements(cpu, memory, storage, network, gpu, scaling);
        
        BudgetConstraint budget = new BudgetConstraint(100.0, 2000.0, "USD", "development", 0.8, "alert");
        PerformanceSla sla = new PerformanceSla(99.5, 200.0, 1.0, 1000.0, 0.9);
        SustainabilityGoals sustainability = new SustainabilityGoals(500.0, true, false, true);
        
        AllocationConstraints constraints = new AllocationConstraints(
            List.of(CloudProvider.AWS), List.of(), List.of("us-east-1"), List.of(),
            budget, List.of(), sla, sustainability, List.of(), List.of()
        );
        
        return new AllocationRequest(
            UUID.randomUUID().toString(),
            "test-service",
            requirements,
            constraints,
            AllocationPriority.NORMAL,
            Map.of("test", "true"),
            Instant.now()
        );
    }

    private AllocatedResources createTestAllocatedResources() {
        return new AllocatedResources(
            CloudProvider.AWS, "us-east-1", "us-east-1a", "m5.large", 2,
            4.0, 16.0, 200.0, 2.0, 0, List.of("i-1234567890abcdef0", "i-abcdef1234567890")
        );
    }

    private CostEstimate createTestCostEstimate() {
        return new CostEstimate(
            0.20, 4.80, 144.0, "USD", 
            Map.of("compute", 0.16, "storage", 0.03, "network", 0.01),
            20.0, List.of("Consider reserved instances")
        );
    }

    private PerformancePrediction createTestPerformancePrediction() {
        return new PerformancePrediction(70.0, 75.0, 150.0, 1000.0, 0.85, "None predicted");
    }

    private CarbonFootprint createTestCarbonFootprint() {
        return new CarbonFootprint(0.5, 12.0, 360.0, 50.0, 400.0, 9.0);
    }
}
*/