/*
package ai.twodot.platform.agents.resourcemanager.cloud;

import ai.twodot.platform.agents.resourcemanager.resource.ResourceAllocation.*;
import ai.twodot.platform.agents.resourcemanager.agent.ResourceManagerAgent.*;
import ai.twodot.platform.agents.resourcemanager.cloud.MultiCloudManager.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MultiCloudManagerTest {

    @Mock
    private AwsCloudProvider awsProvider;

    @Mock
    private GcpCloudProvider gcpProvider;

    @Mock
    private AzureCloudProvider azureProvider;

    @Mock
    private KubernetesCloudProvider kubernetesProvider;

    @InjectMocks
    private MultiCloudManager multiCloudManager;

    private AllocationRequest testRequest;

    @BeforeEach
    void setUp() {
        testRequest = createTestAllocationRequest();
        
        when(awsProvider.testConnection()).thenReturn(true);
        when(gcpProvider.testConnection()).thenReturn(true);
        when(azureProvider.testConnection()).thenReturn(false); 
        when(kubernetesProvider.testConnection()).thenReturn(true);
        
        multiCloudManager.initializeProviders();
    }

    @Test
    void testInitializeProviders() {
        verify(awsProvider).testConnection();
        verify(gcpProvider).testConnection();
        verify(azureProvider).testConnection();
        verify(kubernetesProvider).testConnection();
        
        Map<String, Object> connectionStatus = multiCloudManager.getConnectionStatus();
        assertNotNull(connectionStatus);
        assertEquals(4, connectionStatus.size());
        
        Map<String, Object> awsStatus = (Map<String, Object>) connectionStatus.get("aws");
        assertTrue((Boolean) awsStatus.get("connected"));
        
        Map<String, Object> azureStatus = (Map<String, Object>) connectionStatus.get("azure");
        assertFalse((Boolean) azureStatus.get("connected"));
    }

    @Test
    void testPlanOptimalDeploymentSuccess() throws Exception {
        CloudDeploymentOption awsOption = createCloudDeploymentOption(CloudProvider.AWS, 100.0, 85.0);
        CloudDeploymentOption gcpOption = createCloudDeploymentOption(CloudProvider.GCP, 120.0, 90.0);
        
        when(awsProvider.estimateDeployment(testRequest)).thenReturn(awsOption);
        when(gcpProvider.estimateDeployment(testRequest)).thenReturn(gcpOption);
        when(kubernetesProvider.estimateDeployment(testRequest))
            .thenReturn(createCloudDeploymentOption(CloudProvider.KUBERNETES_ON_PREMISES, 80.0, 75.0));

        CompletableFuture<MultiCloudDeploymentPlan> result = multiCloudManager.planOptimalDeployment(testRequest);
        MultiCloudDeploymentPlan plan = result.get(5, TimeUnit.SECONDS);

        assertNotNull(plan);
        assertEquals(testRequest.serviceId(), plan.serviceId());
        assertNotNull(plan.plan());
        
        String optimalProvider = (String) plan.plan().get("optimal_provider");
        assertNotNull(optimalProvider);
        
        verify(awsProvider).estimateDeployment(testRequest);
        verify(gcpProvider).estimateDeployment(testRequest);
        verify(kubernetesProvider).estimateDeployment(testRequest);
        verify(azureProvider, never()).estimateDeployment(any());
    }

    @Test
    void testPlanOptimalDeploymentWithProviderFailure() throws Exception {
        CloudDeploymentOption awsOption = createCloudDeploymentOption(CloudProvider.AWS, 100.0, 85.0);
        
        when(awsProvider.estimateDeployment(testRequest)).thenReturn(awsOption);
        when(gcpProvider.estimateDeployment(testRequest))
            .thenThrow(new RuntimeException("GCP service unavailable"));
        when(kubernetesProvider.estimateDeployment(testRequest))
            .thenReturn(createCloudDeploymentOption(CloudProvider.KUBERNETES_ON_PREMISES, 80.0, 75.0));

        CompletableFuture<MultiCloudDeploymentPlan> result = multiCloudManager.planOptimalDeployment(testRequest);
        MultiCloudDeploymentPlan plan = result.get(5, TimeUnit.SECONDS);

        assertNotNull(plan);
        assertEquals(testRequest.serviceId(), plan.serviceId());
        
        String optimalProvider = (String) plan.plan().get("optimal_provider");
        assertNotNull(optimalProvider);
        assertTrue(optimalProvider.equals("AWS") || optimalProvider.equals("KUBERNETES_ON_PREMISES"));
    }

    @Test
    void testExecuteAllocationSuccess() {
        CapacityPrediction capacityPrediction = new CapacityPrediction("test-service", Map.of());
        CostOptimizationRecommendation costOptimization = new CostOptimizationRecommendation("test-service", Map.of());
        MultiCloudDeploymentPlan deploymentPlan = new MultiCloudDeploymentPlan("test-service", 
            Map.of("optimal_provider", "AWS"));
        
        CloudAllocationResult cloudResult = createCloudAllocationResult();
        when(awsProvider.allocateResources(testRequest, deploymentPlan)).thenReturn(cloudResult);

        AllocationResult result = multiCloudManager.executeAllocation(
            testRequest, capacityPrediction, costOptimization, deploymentPlan);

        assertNotNull(result);
        assertEquals(testRequest.allocationId(), result.allocationId());
        assertEquals(AllocationStatus.ALLOCATED, result.status());
        assertNotNull(result.allocatedResources());
        assertNotNull(result.costEstimate());
        assertNotNull(result.performancePrediction());
        assertNotNull(result.carbonFootprint());
        assertNull(result.errorMessage());
        
        verify(awsProvider).allocateResources(testRequest, deploymentPlan);
    }

    @Test
    void testExecuteAllocationWithProviderNotAvailable() {
        CapacityPrediction capacityPrediction = new CapacityPrediction("test-service", Map.of());
        CostOptimizationRecommendation costOptimization = new CostOptimizationRecommendation("test-service", Map.of());
        MultiCloudDeploymentPlan deploymentPlan = new MultiCloudDeploymentPlan("test-service", 
            Map.of("optimal_provider", "NONEXISTENT_PROVIDER"));

        AllocationResult result = multiCloudManager.executeAllocation(
            testRequest, capacityPrediction, costOptimization, deploymentPlan);

        assertNotNull(result);
        assertEquals(AllocationStatus.FAILED, result.status());
        assertNotNull(result.errorMessage());
        assertTrue(result.errorMessage().contains("Cloud provider not available"));
    }

    @Test
    void testExecuteAllocationWithProviderFailure() {
        CapacityPrediction capacityPrediction = new CapacityPrediction("test-service", Map.of());
        CostOptimizationRecommendation costOptimization = new CostOptimizationRecommendation("test-service", Map.of());
        MultiCloudDeploymentPlan deploymentPlan = new MultiCloudDeploymentPlan("test-service", 
            Map.of("optimal_provider", "AWS"));
        
        when(awsProvider.allocateResources(testRequest, deploymentPlan))
            .thenThrow(new RuntimeException("AWS allocation failed"));

        AllocationResult result = multiCloudManager.executeAllocation(
            testRequest, capacityPrediction, costOptimization, deploymentPlan);

        assertNotNull(result);
        assertEquals(AllocationStatus.FAILED, result.status());
        assertNotNull(result.errorMessage());
        assertTrue(result.errorMessage().contains("AWS allocation failed"));
    }

    @Test
    void testGetConnectionStatus() {
        Map<String, Object> status = multiCloudManager.getConnectionStatus();

        assertNotNull(status);
        assertEquals(4, status.size());
        
        Map<String, Object> awsStatus = (Map<String, Object>) status.get("aws");
        assertNotNull(awsStatus);
        assertTrue((Boolean) awsStatus.get("connected"));
        assertEquals("Connected", awsStatus.get("status"));
        
        Map<String, Object> azureStatus = (Map<String, Object>) status.get("azure");
        assertNotNull(azureStatus);
        assertFalse((Boolean) azureStatus.get("connected"));
    }

    @Test
    void testGetCloudMetrics() {
        Map<String, Object> awsMetrics = Map.of("instances", 5, "cost", 100.0);
        when(awsProvider.getProviderMetrics()).thenReturn(awsMetrics);
        when(gcpProvider.getProviderMetrics()).thenReturn(Map.of("instances", 3, "cost", 80.0));
        when(kubernetesProvider.getProviderMetrics()).thenReturn(Map.of("pods", 20, "nodes", 3));
        when(azureProvider.getProviderMetrics()).thenThrow(new RuntimeException("Azure unavailable"));

        Map<String, Object> metrics = multiCloudManager.getCloudMetrics();

        assertNotNull(metrics);
        assertTrue(metrics.containsKey("connected_providers"));
        assertTrue(metrics.containsKey("total_providers"));
        assertTrue(metrics.containsKey("connection_rate"));
        assertTrue(metrics.containsKey("aws_metrics"));
        assertTrue(metrics.containsKey("gcp_metrics"));
        assertTrue(metrics.containsKey("kubernetes_on_premises_metrics"));
        
        assertEquals(awsMetrics, metrics.get("aws_metrics"));
        
        Map<String, Object> azureMetrics = (Map<String, Object>) metrics.get("azure_metrics");
        assertTrue(azureMetrics.containsKey("error"));
    }

    @Test
    void testPlanOptimalDeploymentWithPreferredClouds() throws Exception {
        AllocationRequest requestWithPreferences = createTestAllocationRequestWithPreferences(
            List.of(CloudProvider.GCP), List.of(CloudProvider.AWS)
        );
        
        CloudDeploymentOption gcpOption = createCloudDeploymentOption(CloudProvider.GCP, 120.0, 90.0);
        when(gcpProvider.estimateDeployment(requestWithPreferences)).thenReturn(gcpOption);

        CompletableFuture<MultiCloudDeploymentPlan> result = 
            multiCloudManager.planOptimalDeployment(requestWithPreferences);
        MultiCloudDeploymentPlan plan = result.get(5, TimeUnit.SECONDS);

        assertNotNull(plan);
        String optimalProvider = (String) plan.plan().get("optimal_provider");
        assertEquals("GCP", optimalProvider);
        
        verify(gcpProvider).estimateDeployment(requestWithPreferences);
        verify(awsProvider, never()).estimateDeployment(any());
        verify(azureProvider, never()).estimateDeployment(any());
    }

    @Test
    void testPlanOptimalDeploymentWithNoViableProviders() {
        when(awsProvider.testConnection()).thenReturn(false);
        when(gcpProvider.testConnection()).thenReturn(false);
        when(kubernetesProvider.testConnection()).thenReturn(false);
        
        multiCloudManager.initializeProviders();

        assertThrows(Exception.class, () -> {
            CompletableFuture<MultiCloudDeploymentPlan> result = 
                multiCloudManager.planOptimalDeployment(testRequest);
            result.get(5, TimeUnit.SECONDS);
        });
    }

    private AllocationRequest createTestAllocationRequest() {
        CpuRequirement cpu = new CpuRequirement(2.0, 8.0, 70.0, "x86_64", "x86_64");
        MemoryRequirement memory = new MemoryRequirement(4.0, 16.0, "DDR4", 80.0);
        StorageRequirement storage = new StorageRequirement(20.0, 100.0, StorageType.SSD, 1000, 100, true);
        NetworkRequirement network = new NetworkRequirement(100.0, 1000.0, 10.0, 2, true);
        GpuRequirement gpu = new GpuRequirement(0, "", 0.0, "");
        ScalingRequirement scaling = new ScalingRequirement(1, 10, 70.0, 80.0, 300, 600, true, false);
        
        ResourceRequirements requirements = new ResourceRequirements(cpu, memory, storage, network, gpu, scaling);
        
        BudgetConstraint budget = new BudgetConstraint(100.0, 2000.0, "USD", "development", 0.8, "alert");
        PerformanceSla sla = new PerformanceSla(99.5, 200.0, 1.0, 1000.0, 0.9);
        SustainabilityGoals sustainability = new SustainabilityGoals(500.0, true, false, true);
        
        AllocationConstraints constraints = new AllocationConstraints(
            null, null, List.of("us-east-1"), List.of(),
            budget, List.of(), sla, sustainability, List.of(), List.of()
        );
        
        return new AllocationRequest(
            UUID.randomUUID().toString(),
            "test-service",
            requirements,
            constraints,
            AllocationPriority.NORMAL,
            Map.of("test", "true"),
            Instant.now()
        );
    }

    private AllocationRequest createTestAllocationRequestWithPreferences(
        List<CloudProvider> preferred, List<CloudProvider> excluded) {
        
        AllocationRequest baseRequest = createTestAllocationRequest();
        
        AllocationConstraints constraintsWithPreferences = new AllocationConstraints(
            preferred, excluded, List.of("us-east-1"), List.of(),
            baseRequest.constraints().budgetLimit(),
            baseRequest.constraints().complianceRequirements(),
            baseRequest.constraints().performanceSla(),
            baseRequest.constraints().sustainabilityGoals(),
            List.of(),
            List.of()
        );
        
        return new AllocationRequest(
            baseRequest.allocationId(),
            baseRequest.serviceId(),
            baseRequest.requirements(),
            constraintsWithPreferences,
            baseRequest.priority(),
            baseRequest.metadata(),
            baseRequest.requestedAt()
        );
    }

    private CloudDeploymentOption createCloudDeploymentOption(
        CloudProvider provider, double cost, double performanceScore) {
        
        return new CloudDeploymentOption(
            provider,
            "us-east-1",
            "m5.large",
            cost,
            performanceScore,
            95.0,
            70.0,
            "auto_scaling",
            Map.of("instance_count", 2)
        );
    }

    private CloudAllocationResult createCloudAllocationResult() {
        return new CloudAllocationResult(
            "us-east-1",
            "us-east-1a",
            "m5.large",
            2,
            4.0,
            16.0,
            200.0,
            2.0,
            0,
            List.of("i-1234567890abcdef0"),
            0.20,
            Map.of("compute", 0.16, "storage", 0.04),
            20.0,
            70.0,
            75.0,
            150.0,
            1000.0,
            0.85,
            "None predicted"
        );
    }
}
*/