# Knowledge Base Agent Configuration
server.port=8085

# PostgreSQL Database Configuration
spring.datasource.url=**************************************************
spring.datasource.driverClassName=org.postgresql.Driver
spring.datasource.username=${DB_USERNAME:koneti}
spring.datasource.password=${DB_PASSWORD:}
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=true

# Redis Configuration
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.timeout=2000ms

# Management and Actuator
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=always
management.metrics.export.prometheus.enabled=true

# Knowledge Base Specific
knowledge.base.vector.enabled=true
knowledge.base.graph.enabled=true
knowledge.base.semantic.search.enabled=true
knowledge.base.ai.enabled=true
knowledge.base.ai.service.url=http://localhost:9085

# Vector Database Configuration
vector.db.enabled=true
vector.db.type=chroma
vector.db.url=http://localhost:8000

# Logging
logging.level.ai.twodot.platform.agents.knowledgebase=INFO
logging.level.org.springframework.web=INFO
logging.level.root=INFO