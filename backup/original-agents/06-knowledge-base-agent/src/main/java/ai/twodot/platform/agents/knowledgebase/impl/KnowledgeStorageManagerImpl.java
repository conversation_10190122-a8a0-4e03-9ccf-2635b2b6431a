package ai.twodot.platform.agents.knowledgebase.impl;

import ai.twodot.platform.agents.knowledgebase.agent.KnowledgeBaseAgent;
import ai.twodot.platform.agents.knowledgebase.data.KnowledgeModels.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class KnowledgeStorageManagerImpl implements KnowledgeBaseAgent.KnowledgeStorageManager {
    
    private static final Logger logger = LoggerFactory.getLogger(KnowledgeStorageManagerImpl.class);
    
    private final Map<String, KnowledgeItem> knowledgeStore = new ConcurrentHashMap<>();

    @Override
    public void storeKnowledge(List<KnowledgeItem> knowledge) {
        logger.info("Storing {} knowledge items", knowledge.size());
        
        for (KnowledgeItem item : knowledge) {
            knowledgeStore.put(item.knowledgeId(), item);
        }
        
        logger.info("Successfully stored {} knowledge items. Total items: {}", 
            knowledge.size(), knowledgeStore.size());
    }

    @Override
    public List<KnowledgeItem> getKnowledgeItems(List<String> knowledgeIds) {
        logger.info("Retrieving {} knowledge items", knowledgeIds.size());
        
        List<KnowledgeItem> items = new ArrayList<>();
        for (String id : knowledgeIds) {
            KnowledgeItem item = knowledgeStore.get(id);
            if (item != null) {
                items.add(item);
            } else {
                logger.warn("Knowledge item not found: {}", id);
            }
        }
        
        logger.info("Retrieved {} out of {} requested knowledge items", items.size(), knowledgeIds.size());
        return items;
    }

    @Override
    public void updateKnowledgeQuality(String knowledgeId, Map<String, Object> qualityUpdate) {
        logger.info("Updating quality for knowledge item: {}", knowledgeId);
        
        KnowledgeItem item = knowledgeStore.get(knowledgeId);
        if (item != null) {
            // Extract quality scores from update
            double newQualityScore = item.qualityScore();
            double newConfidenceScore = item.confidenceScore();
            
            if (qualityUpdate.containsKey("quality_score")) {
                newQualityScore = ((Number) qualityUpdate.get("quality_score")).doubleValue();
            }
            if (qualityUpdate.containsKey("confidence_score")) {
                newConfidenceScore = ((Number) qualityUpdate.get("confidence_score")).doubleValue();
            }
            
            // Create updated item
            KnowledgeItem updatedItem = new KnowledgeItem(
                item.knowledgeId(),
                item.title(),
                item.content(),
                item.contentType(),
                item.source(),
                item.metadata(),
                item.classification(),
                item.vectorEmbedding(),
                item.entities(),
                item.relationships(),
                newQualityScore,
                newConfidenceScore,
                item.createdAt(),
                item.updatedAt()
            );
            
            knowledgeStore.put(knowledgeId, updatedItem);
            logger.info("Updated quality for knowledge item: {} (quality: {}, confidence: {})", 
                knowledgeId, newQualityScore, newConfidenceScore);
        } else {
            logger.warn("Cannot update quality - knowledge item not found: {}", knowledgeId);
        }
    }

    @Override
    public long getStorageUsageMb() {
        // Estimate storage usage based on number of items
        long estimatedSizeBytes = knowledgeStore.size() * 10240; // 10KB per item estimate
        return estimatedSizeBytes / (1024 * 1024); // Convert to MB
    }

    @Override
    public long getKnowledgeItemCount() {
        return knowledgeStore.size();
    }

    @Override
    public double getAverageQualityScore() {
        if (knowledgeStore.isEmpty()) {
            return 0.0;
        }
        
        double totalQuality = knowledgeStore.values().stream()
            .mapToDouble(KnowledgeItem::qualityScore)
            .sum();
            
        return totalQuality / knowledgeStore.size();
    }

    @Override
    public boolean isHealthy() {
        return true; // Simple health check - storage is always healthy in memory implementation
    }
}