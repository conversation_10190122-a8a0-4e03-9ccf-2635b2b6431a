package ai.twodot.platform.agents.orchestrator.data;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.Instant;
import java.util.List;
import java.util.Map;

/**
 * Task Orchestrator Agent Data Models
 * 
 * Comprehensive data structures for workflow orchestration, task management,
 * and performance optimization in the Task Orchestrator Agent.
 * 
 * These models support:
 * - Workflow definition and execution state
 * - Task distribution and scheduling
 * - Performance prediction and optimization
 * - Resource allocation and monitoring
 * - Cross-agent coordination and communication
 * 
 * <AUTHOR> Platform Team
 * @version 1.0.0
 * @since 2025-01-14
 */
public class OrchestrationModels {

    // ===== Core Workflow Models =====

    /**
     * Workflow definition and configuration
     */
    public record WorkflowDefinition(
        @JsonProperty("workflow_id") String workflowId,
        @JsonProperty("name") String name,
        @JsonProperty("description") String description,
        @JsonProperty("version") String version,
        @JsonProperty("workflow_type") WorkflowType workflowType,
        @JsonProperty("tasks") List<TaskDefinition> tasks,
        @JsonProperty("dependencies") List<TaskDependency> dependencies,
        @JsonProperty("triggers") List<WorkflowTrigger> triggers,
        @JsonProperty("configuration") WorkflowConfiguration configuration,
        @JsonProperty("metadata") WorkflowMetadata metadata,
        @JsonProperty("created_at") Instant createdAt,
        @JsonProperty("updated_at") Instant updatedAt
    ) {}

    /**
     * Task definition within a workflow
     */
    public record TaskDefinition(
        @JsonProperty("task_id") String taskId,
        @JsonProperty("name") String name,
        @JsonProperty("description") String description,
        @JsonProperty("task_type") TaskType taskType,
        @JsonProperty("executor") TaskExecutor executor,
        @JsonProperty("configuration") TaskConfiguration configuration,
        @JsonProperty("resources") ResourceRequirements resources,
        @JsonProperty("timeout") Duration timeout,
        @JsonProperty("retry_policy") RetryPolicy retryPolicy,
        @JsonProperty("error_handling") ErrorHandling errorHandling,
        @JsonProperty("metadata") TaskMetadata metadata
    ) {}

    /**
     * Workflow execution instance
     */
    public record WorkflowExecution(
        @JsonProperty("execution_id") String executionId,
        @JsonProperty("workflow_id") String workflowId,
        @JsonProperty("workflow_version") String workflowVersion,
        @JsonProperty("status") WorkflowStatus status,
        @JsonProperty("trigger_info") TriggerInfo triggerInfo,
        @JsonProperty("task_executions") List<TaskExecution> taskExecutions,
        @JsonProperty("execution_context") ExecutionContext executionContext,
        @JsonProperty("performance_metrics") PerformanceMetrics performanceMetrics,
        @JsonProperty("started_at") Instant startedAt,
        @JsonProperty("completed_at") Instant completedAt,
        @JsonProperty("duration_ms") long durationMs
    ) {}

    /**
     * Individual task execution instance
     */
    public record TaskExecution(
        @JsonProperty("execution_id") String executionId,
        @JsonProperty("task_id") String taskId,
        @JsonProperty("workflow_execution_id") String workflowExecutionId,
        @JsonProperty("status") TaskStatus status,
        @JsonProperty("assigned_agent") String assignedAgent,
        @JsonProperty("assigned_resource") String assignedResource,
        @JsonProperty("input_data") Map<String, Object> inputData,
        @JsonProperty("output_data") Map<String, Object> outputData,
        @JsonProperty("error_info") ErrorInfo errorInfo,
        @JsonProperty("performance_data") TaskPerformanceData performanceData,
        @JsonProperty("retry_count") int retryCount,
        @JsonProperty("started_at") Instant startedAt,
        @JsonProperty("completed_at") Instant completedAt,
        @JsonProperty("duration_ms") long durationMs
    ) {}

    // ===== Orchestration Models =====

    /**
     * Orchestration request for workflow execution
     */
    public record OrchestrationRequest(
        @JsonProperty("request_id") String requestId,
        @JsonProperty("workflow_id") String workflowId,
        @JsonProperty("trigger_type") TriggerType triggerType,
        @JsonProperty("input_parameters") Map<String, Object> inputParameters,
        @JsonProperty("execution_options") ExecutionOptions executionOptions,
        @JsonProperty("priority") TaskPriority priority,
        @JsonProperty("scheduling_constraints") SchedulingConstraints schedulingConstraints,
        @JsonProperty("requested_by") String requestedBy,
        @JsonProperty("requested_at") Instant requestedAt
    ) {}

    /**
     * Orchestration response with execution details
     */
    public record OrchestrationResponse(
        @JsonProperty("request_id") String requestId,
        @JsonProperty("execution_id") String executionId,
        @JsonProperty("status") OrchestrationStatus status,
        @JsonProperty("estimated_duration") Duration estimatedDuration,
        @JsonProperty("allocated_resources") List<ResourceAllocation> allocatedResources,
        @JsonProperty("task_schedule") List<TaskScheduleItem> taskSchedule,
        @JsonProperty("monitoring_urls") List<String> monitoringUrls,
        @JsonProperty("response_at") Instant responseAt
    ) {}

    /**
     * Task distribution strategy and configuration
     */
    public record TaskDistribution(
        @JsonProperty("distribution_id") String distributionId,
        @JsonProperty("workflow_execution_id") String workflowExecutionId,
        @JsonProperty("distribution_strategy") DistributionStrategy distributionStrategy,
        @JsonProperty("task_assignments") List<TaskAssignment> taskAssignments,
        @JsonProperty("load_balancing") LoadBalancingConfig loadBalancingConfig,
        @JsonProperty("resource_constraints") List<ResourceConstraint> resourceConstraints,
        @JsonProperty("performance_targets") PerformanceTargets performanceTargets,
        @JsonProperty("created_at") Instant createdAt
    ) {}

    /**
     * Task assignment to specific agent/resource
     */
    public record TaskAssignment(
        @JsonProperty("assignment_id") String assignmentId,
        @JsonProperty("task_execution_id") String taskExecutionId,
        @JsonProperty("assigned_agent_id") String assignedAgentId,
        @JsonProperty("assigned_resource_id") String assignedResourceId,
        @JsonProperty("assignment_score") double assignmentScore,
        @JsonProperty("expected_performance") ExpectedPerformance expectedPerformance,
        @JsonProperty("assignment_reason") AssignmentReason assignmentReason,
        @JsonProperty("assigned_at") Instant assignedAt
    ) {}

    // ===== Scheduling Models =====

    /**
     * Task scheduling configuration and strategy
     */
    public record TaskSchedule(
        @JsonProperty("schedule_id") String scheduleId,
        @JsonProperty("workflow_execution_id") String workflowExecutionId,
        @JsonProperty("scheduling_strategy") SchedulingStrategy schedulingStrategy,
        @JsonProperty("schedule_items") List<TaskScheduleItem> scheduleItems,
        @JsonProperty("optimization_objectives") List<OptimizationObjective> optimizationObjectives,
        @JsonProperty("constraints") List<SchedulingConstraint> constraints,
        @JsonProperty("predicted_completion") Instant predictedCompletion,
        @JsonProperty("created_at") Instant createdAt
    ) {}

    /**
     * Individual task schedule item
     */
    public record TaskScheduleItem(
        @JsonProperty("schedule_item_id") String scheduleItemId,
        @JsonProperty("task_execution_id") String taskExecutionId,
        @JsonProperty("scheduled_start") Instant scheduledStart,
        @JsonProperty("estimated_duration") Duration estimatedDuration,
        @JsonProperty("scheduling_priority") int schedulingPriority,
        @JsonProperty("dependencies") List<String> dependencies,
        @JsonProperty("resource_requirements") ResourceRequirements resourceRequirements,
        @JsonProperty("flexibility_window") Duration flexibilityWindow
    ) {}

    /**
     * Resource allocation for tasks
     */
    public record ResourceAllocation(
        @JsonProperty("allocation_id") String allocationId,
        @JsonProperty("resource_id") String resourceId,
        @JsonProperty("resource_type") ResourceType resourceType,
        @JsonProperty("allocated_capacity") ResourceCapacity allocatedCapacity,
        @JsonProperty("allocation_period") TimePeriod allocationPeriod,
        @JsonProperty("allocation_cost") double allocationCost,
        @JsonProperty("utilization_target") double utilizationTarget,
        @JsonProperty("allocated_at") Instant allocatedAt
    ) {}

    // ===== Performance Models =====

    /**
     * Performance prediction for workflows and tasks
     */
    public record PerformancePrediction(
        @JsonProperty("prediction_id") String predictionId,
        @JsonProperty("target_id") String targetId,
        @JsonProperty("target_type") PredictionTargetType targetType,
        @JsonProperty("predicted_duration") Duration predictedDuration,
        @JsonProperty("predicted_resource_usage") ResourceUsage predictedResourceUsage,
        @JsonProperty("confidence_score") double confidenceScore,
        @JsonProperty("prediction_factors") List<PredictionFactor> predictionFactors,
        @JsonProperty("historical_analysis") HistoricalAnalysis historicalAnalysis,
        @JsonProperty("predicted_at") Instant predictedAt
    ) {}

    /**
     * Performance metrics for executed workflows/tasks
     */
    public record PerformanceMetrics(
        @JsonProperty("metrics_id") String metricsId,
        @JsonProperty("target_id") String targetId,
        @JsonProperty("target_type") MetricsTargetType targetType,
        @JsonProperty("execution_time") Duration executionTime,
        @JsonProperty("resource_utilization") ResourceUtilization resourceUtilization,
        @JsonProperty("throughput") double throughput,
        @JsonProperty("error_rate") double errorRate,
        @JsonProperty("quality_score") double qualityScore,
        @JsonProperty("efficiency_score") double efficiencyScore,
        @JsonProperty("measured_at") Instant measuredAt
    ) {}

    /**
     * Workflow optimization recommendations
     */
    public record OptimizationRecommendation(
        @JsonProperty("recommendation_id") String recommendationId,
        @JsonProperty("workflow_id") String workflowId,
        @JsonProperty("optimization_type") OptimizationType optimizationType,
        @JsonProperty("recommendation_details") OptimizationDetails recommendationDetails,
        @JsonProperty("expected_improvement") ExpectedImprovement expectedImprovement,
        @JsonProperty("implementation_effort") ImplementationEffort implementationEffort,
        @JsonProperty("confidence_level") double confidenceLevel,
        @JsonProperty("recommended_at") Instant recommendedAt
    ) {}

    // ===== Agent Integration Models =====

    /**
     * Agent capability and availability information
     */
    public record AgentCapability(
        @JsonProperty("agent_id") String agentId,
        @JsonProperty("agent_type") String agentType,
        @JsonProperty("supported_task_types") List<TaskType> supportedTaskTypes,
        @JsonProperty("performance_characteristics") PerformanceCharacteristics performanceCharacteristics,
        @JsonProperty("resource_capacity") ResourceCapacity resourceCapacity,
        @JsonProperty("availability_status") AvailabilityStatus availabilityStatus,
        @JsonProperty("current_load") double currentLoad,
        @JsonProperty("last_updated") Instant lastUpdated
    ) {}

    /**
     * A2A communication request
     */
    public record A2ARequest(
        @JsonProperty("request_id") String requestId,
        @JsonProperty("source_agent") String sourceAgent,
        @JsonProperty("target_agent") String targetAgent,
        @JsonProperty("request_type") A2ARequestType requestType,
        @JsonProperty("payload") Map<String, Object> payload,
        @JsonProperty("execution_context") ExecutionContext executionContext,
        @JsonProperty("timeout") Duration timeout,
        @JsonProperty("priority") RequestPriority priority,
        @JsonProperty("created_at") Instant createdAt
    ) {}

    /**
     * A2A communication response
     */
    public record A2AResponse(
        @JsonProperty("request_id") String requestId,
        @JsonProperty("response_id") String responseId,
        @JsonProperty("status") A2AResponseStatus status,
        @JsonProperty("result") Map<String, Object> result,
        @JsonProperty("error_info") ErrorInfo errorInfo,
        @JsonProperty("execution_metrics") ExecutionMetrics executionMetrics,
        @JsonProperty("responded_at") Instant respondedAt
    ) {}

    // ===== Configuration Models =====

    /**
     * Workflow configuration settings
     */
    public record WorkflowConfiguration(
        @JsonProperty("execution_mode") ExecutionMode executionMode,
        @JsonProperty("concurrency_limit") int concurrencyLimit,
        @JsonProperty("timeout_policy") TimeoutPolicy timeoutPolicy,
        @JsonProperty("error_policy") ErrorPolicy errorPolicy,
        @JsonProperty("monitoring_config") MonitoringConfig monitoringConfig,
        @JsonProperty("notification_config") NotificationConfig notificationConfig,
        @JsonProperty("optimization_settings") OptimizationSettings optimizationSettings
    ) {}

    /**
     * Task configuration settings
     */
    public record TaskConfiguration(
        @JsonProperty("execution_environment") ExecutionEnvironment executionEnvironment,
        @JsonProperty("input_schema") Map<String, Object> inputSchema,
        @JsonProperty("output_schema") Map<String, Object> outputSchema,
        @JsonProperty("validation_rules") List<ValidationRule> validationRules,
        @JsonProperty("security_requirements") SecurityRequirements securityRequirements,
        @JsonProperty("performance_requirements") PerformanceRequirements performanceRequirements
    ) {}

    // ===== Supporting Data Structures =====

    /**
     * Resource requirements specification
     */
    public record ResourceRequirements(
        @JsonProperty("cpu_cores") double cpuCores,
        @JsonProperty("memory_mb") long memoryMb,
        @JsonProperty("storage_mb") long storageMb,
        @JsonProperty("network_bandwidth_mbps") double networkBandwidthMbps,
        @JsonProperty("gpu_count") int gpuCount,
        @JsonProperty("specialized_resources") List<SpecializedResource> specializedResources
    ) {}

    /**
     * Duration specification
     */
    public record Duration(
        @JsonProperty("value") long value,
        @JsonProperty("unit") TimeUnit unit
    ) {}

    /**
     * Time period specification
     */
    public record TimePeriod(
        @JsonProperty("start_time") Instant startTime,
        @JsonProperty("end_time") Instant endTime
    ) {}

    /**
     * Error information
     */
    public record ErrorInfo(
        @JsonProperty("error_code") String errorCode,
        @JsonProperty("error_message") String errorMessage,
        @JsonProperty("error_type") ErrorType errorType,
        @JsonProperty("stack_trace") String stackTrace,
        @JsonProperty("context") Map<String, Object> context,
        @JsonProperty("occurred_at") Instant occurredAt
    ) {}

    // ===== Enumeration Types =====

    public enum WorkflowType {
        SEQUENTIAL, PARALLEL, DAG, STATE_MACHINE, EVENT_DRIVEN, HYBRID
    }

    public enum TaskType {
        DATA_PROCESSING, ML_TRAINING, ML_INFERENCE, API_CALL, COMPUTATION, 
        INTEGRATION, VALIDATION, TRANSFORMATION, ANALYSIS, NOTIFICATION
    }

    public enum WorkflowStatus {
        PENDING, RUNNING, PAUSED, COMPLETED, FAILED, CANCELLED, TIMEOUT
    }

    public enum TaskStatus {
        PENDING, SCHEDULED, RUNNING, COMPLETED, FAILED, CANCELLED, 
        TIMEOUT, RETRYING, SKIPPED
    }

    public enum TaskPriority {
        LOW, NORMAL, HIGH, CRITICAL, URGENT
    }

    public enum OrchestrationStatus {
        ACCEPTED, SCHEDULED, RUNNING, COMPLETED, FAILED, REJECTED
    }

    public enum DistributionStrategy {
        ROUND_ROBIN, LOAD_BASED, CAPABILITY_BASED, PERFORMANCE_BASED, 
        COST_OPTIMIZED, HYBRID
    }

    public enum SchedulingStrategy {
        FIFO, PRIORITY_BASED, SHORTEST_JOB_FIRST, DEADLINE_FIRST, 
        RESOURCE_OPTIMIZED, AI_OPTIMIZED
    }

    public enum ResourceType {
        CPU, MEMORY, STORAGE, NETWORK, GPU, AGENT, EXTERNAL_SERVICE
    }

    public enum TriggerType {
        MANUAL, SCHEDULED, EVENT_DRIVEN, API_CALL, WEBHOOK, FILE_CHANGE
    }

    public enum OptimizationType {
        PERFORMANCE, COST, RESOURCE_UTILIZATION, ENERGY_EFFICIENCY, 
        RELIABILITY, SCALABILITY
    }

    public enum PredictionTargetType {
        WORKFLOW, TASK, RESOURCE_USAGE, SYSTEM_LOAD
    }

    public enum MetricsTargetType {
        WORKFLOW_EXECUTION, TASK_EXECUTION, AGENT_PERFORMANCE, SYSTEM_METRICS
    }

    public enum AvailabilityStatus {
        AVAILABLE, BUSY, MAINTENANCE, OFFLINE, DEGRADED
    }

    public enum A2ARequestType {
        TASK_EXECUTION, DATA_REQUEST, STATUS_CHECK, CAPABILITY_QUERY, 
        NOTIFICATION, COORDINATION
    }

    public enum A2AResponseStatus {
        SUCCESS, PARTIAL_SUCCESS, FAILURE, TIMEOUT, REJECTED
    }

    public enum ExecutionMode {
        SYNCHRONOUS, ASYNCHRONOUS, STREAMING, BATCH
    }

    public enum ErrorType {
        VALIDATION_ERROR, EXECUTION_ERROR, TIMEOUT_ERROR, RESOURCE_ERROR, 
        COMMUNICATION_ERROR, SYSTEM_ERROR
    }

    public enum TimeUnit {
        MILLISECONDS, SECONDS, MINUTES, HOURS, DAYS
    }

    public enum RequestPriority {
        LOW, NORMAL, HIGH, URGENT
    }

    // ===== Additional Supporting Records =====

    public record TaskExecutor(
        @JsonProperty("executor_type") String executorType,
        @JsonProperty("executor_config") Map<String, Object> executorConfig
    ) {}

    public record RetryPolicy(
        @JsonProperty("max_retries") int maxRetries,
        @JsonProperty("retry_delay") Duration retryDelay,
        @JsonProperty("backoff_strategy") String backoffStrategy
    ) {}

    public record ErrorHandling(
        @JsonProperty("on_error") String onError,
        @JsonProperty("error_handlers") List<String> errorHandlers
    ) {}

    public record TaskMetadata(
        @JsonProperty("tags") List<String> tags,
        @JsonProperty("labels") Map<String, String> labels,
        @JsonProperty("annotations") Map<String, String> annotations
    ) {}

    public record WorkflowMetadata(
        @JsonProperty("tags") List<String> tags,
        @JsonProperty("labels") Map<String, String> labels,
        @JsonProperty("annotations") Map<String, String> annotations,
        @JsonProperty("owner") String owner,
        @JsonProperty("team") String team
    ) {}

    public record TriggerInfo(
        @JsonProperty("trigger_id") String triggerId,
        @JsonProperty("trigger_type") TriggerType triggerType,
        @JsonProperty("trigger_source") String triggerSource,
        @JsonProperty("trigger_data") Map<String, Object> triggerData
    ) {}

    public record ExecutionContext(
        @JsonProperty("correlation_id") String correlationId,
        @JsonProperty("user_context") Map<String, Object> userContext,
        @JsonProperty("security_context") Map<String, Object> securityContext,
        @JsonProperty("execution_environment") String executionEnvironment
    ) {}

    public record TaskPerformanceData(
        @JsonProperty("cpu_usage") double cpuUsage,
        @JsonProperty("memory_usage") long memoryUsage,
        @JsonProperty("io_operations") long ioOperations,
        @JsonProperty("network_bytes") long networkBytes
    ) {}

    public record ExecutionOptions(
        @JsonProperty("async_execution") boolean asyncExecution,
        @JsonProperty("notification_webhooks") List<String> notificationWebhooks,
        @JsonProperty("custom_timeout") Duration customTimeout
    ) {}

    public record SchedulingConstraints(
        @JsonProperty("earliest_start") Instant earliestStart,
        @JsonProperty("latest_completion") Instant latestCompletion,
        @JsonProperty("resource_constraints") List<ResourceConstraint> resourceConstraints
    ) {}

    public record ResourceConstraint(
        @JsonProperty("resource_type") ResourceType resourceType,
        @JsonProperty("constraint_type") String constraintType,
        @JsonProperty("constraint_value") double constraintValue
    ) {}

    public record LoadBalancingConfig(
        @JsonProperty("strategy") String strategy,
        @JsonProperty("weights") Map<String, Double> weights,
        @JsonProperty("health_check_enabled") boolean healthCheckEnabled
    ) {}

    public record PerformanceTargets(
        @JsonProperty("max_duration") Duration maxDuration,
        @JsonProperty("min_throughput") double minThroughput,
        @JsonProperty("max_error_rate") double maxErrorRate
    ) {}

    public record ExpectedPerformance(
        @JsonProperty("estimated_duration") Duration estimatedDuration,
        @JsonProperty("confidence_score") double confidenceScore,
        @JsonProperty("resource_efficiency") double resourceEfficiency
    ) {}

    public record AssignmentReason(
        @JsonProperty("primary_factor") String primaryFactor,
        @JsonProperty("contributing_factors") List<String> contributingFactors,
        @JsonProperty("score_breakdown") Map<String, Double> scoreBreakdown
    ) {}

    public record OptimizationObjective(
        @JsonProperty("objective_type") String objectiveType,
        @JsonProperty("weight") double weight,
        @JsonProperty("target_value") double targetValue
    ) {}

    public record SchedulingConstraint(
        @JsonProperty("constraint_type") String constraintType,
        @JsonProperty("constraint_value") Object constraintValue,
        @JsonProperty("is_hard_constraint") boolean isHardConstraint
    ) {}

    public record ResourceCapacity(
        @JsonProperty("total_capacity") Map<String, Double> totalCapacity,
        @JsonProperty("available_capacity") Map<String, Double> availableCapacity,
        @JsonProperty("reserved_capacity") Map<String, Double> reservedCapacity
    ) {}

    public record ResourceUsage(
        @JsonProperty("cpu_usage_percent") double cpuUsagePercent,
        @JsonProperty("memory_usage_mb") long memoryUsageMb,
        @JsonProperty("storage_usage_mb") long storageUsageMb,
        @JsonProperty("network_usage_mbps") double networkUsageMbps
    ) {}

    public record PredictionFactor(
        @JsonProperty("factor_name") String factorName,
        @JsonProperty("factor_weight") double factorWeight,
        @JsonProperty("factor_value") double factorValue
    ) {}

    public record HistoricalAnalysis(
        @JsonProperty("sample_size") int sampleSize,
        @JsonProperty("average_duration") Duration averageDuration,
        @JsonProperty("success_rate") double successRate,
        @JsonProperty("trend_analysis") String trendAnalysis
    ) {}

    public record ResourceUtilization(
        @JsonProperty("cpu_utilization") double cpuUtilization,
        @JsonProperty("memory_utilization") double memoryUtilization,
        @JsonProperty("storage_utilization") double storageUtilization,
        @JsonProperty("network_utilization") double networkUtilization
    ) {}

    public record OptimizationDetails(
        @JsonProperty("optimization_steps") List<String> optimizationSteps,
        @JsonProperty("configuration_changes") Map<String, Object> configurationChanges,
        @JsonProperty("implementation_plan") String implementationPlan
    ) {}

    public record ExpectedImprovement(
        @JsonProperty("performance_improvement_percent") double performanceImprovementPercent,
        @JsonProperty("cost_reduction_percent") double costReductionPercent,
        @JsonProperty("reliability_improvement") double reliabilityImprovement
    ) {}

    public record ImplementationEffort(
        @JsonProperty("effort_level") String effortLevel,
        @JsonProperty("estimated_hours") int estimatedHours,
        @JsonProperty("required_skills") List<String> requiredSkills
    ) {}

    public record PerformanceCharacteristics(
        @JsonProperty("average_response_time") Duration averageResponseTime,
        @JsonProperty("throughput_capacity") double throughputCapacity,
        @JsonProperty("reliability_score") double reliabilityScore,
        @JsonProperty("scalability_factor") double scalabilityFactor
    ) {}

    public record ExecutionMetrics(
        @JsonProperty("execution_time") Duration executionTime,
        @JsonProperty("resource_consumption") ResourceUsage resourceConsumption,
        @JsonProperty("error_count") int errorCount,
        @JsonProperty("warnings_count") int warningsCount
    ) {}

    public record TimeoutPolicy(
        @JsonProperty("default_timeout") Duration defaultTimeout,
        @JsonProperty("max_timeout") Duration maxTimeout,
        @JsonProperty("timeout_action") String timeoutAction
    ) {}

    public record ErrorPolicy(
        @JsonProperty("error_threshold") int errorThreshold,
        @JsonProperty("error_action") String errorAction,
        @JsonProperty("notification_on_error") boolean notificationOnError
    ) {}

    public record MonitoringConfig(
        @JsonProperty("metrics_enabled") boolean metricsEnabled,
        @JsonProperty("tracing_enabled") boolean tracingEnabled,
        @JsonProperty("log_level") String logLevel
    ) {}

    public record NotificationConfig(
        @JsonProperty("notification_channels") List<String> notificationChannels,
        @JsonProperty("notification_events") List<String> notificationEvents,
        @JsonProperty("notification_templates") Map<String, String> notificationTemplates
    ) {}

    public record OptimizationSettings(
        @JsonProperty("optimization_enabled") boolean optimizationEnabled,
        @JsonProperty("optimization_objectives") List<String> optimizationObjectives,
        @JsonProperty("optimization_frequency") String optimizationFrequency
    ) {}

    public record ExecutionEnvironment(
        @JsonProperty("environment_type") String environmentType,
        @JsonProperty("container_image") String containerImage,
        @JsonProperty("environment_variables") Map<String, String> environmentVariables
    ) {}

    public record ValidationRule(
        @JsonProperty("rule_name") String ruleName,
        @JsonProperty("rule_expression") String ruleExpression,
        @JsonProperty("error_message") String errorMessage
    ) {}

    public record SecurityRequirements(
        @JsonProperty("authentication_required") boolean authenticationRequired,
        @JsonProperty("authorization_rules") List<String> authorizationRules,
        @JsonProperty("encryption_required") boolean encryptionRequired
    ) {}

    public record PerformanceRequirements(
        @JsonProperty("max_execution_time") Duration maxExecutionTime,
        @JsonProperty("min_success_rate") double minSuccessRate,
        @JsonProperty("max_resource_usage") ResourceUsage maxResourceUsage
    ) {}

    public record SpecializedResource(
        @JsonProperty("resource_name") String resourceName,
        @JsonProperty("resource_type") String resourceType,
        @JsonProperty("resource_amount") double resourceAmount
    ) {}

    public record WorkflowTrigger(
        @JsonProperty("trigger_id") String triggerId,
        @JsonProperty("trigger_type") TriggerType triggerType,
        @JsonProperty("trigger_condition") String triggerCondition,
        @JsonProperty("trigger_config") Map<String, Object> triggerConfig
    ) {}

    public record TaskDependency(
        @JsonProperty("dependent_task") String dependentTask,
        @JsonProperty("prerequisite_task") String prerequisiteTask,
        @JsonProperty("dependency_type") String dependencyType
    ) {}
}