package ai.twodot.platform.agents.orchestrator;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Task Orchestrator Agent Application
 * 
 * Main application class for the Task Orchestrator Agent (TOA-007).
 * Provides AI-powered task orchestration, workflow management, and intelligent scheduling
 * for the AI platform ecosystem.
 * 
 * Key Features:
 * - Workflow orchestration and task distribution
 * - AI-powered performance prediction and optimization
 * - Intelligent resource allocation and load balancing
 * - Cross-agent coordination and communication
 * - Real-time monitoring and adaptive execution
 * 
 * Architecture:
 * - Java 21 with Spring Boot for enterprise orchestration services
 * - Python AI service for workflow optimization and performance prediction
 * - Integration with Apache Airflow, Temporal, and Zeebe workflow engines
 * - A2A communication with all platform agents
 * 
 * <AUTHOR> Platform Team
 * @version 1.0.0
 * @since 2025-01-14
 */
@SpringBootApplication
@EnableAsync
@EnableScheduling
@EnableTransactionManagement
@ComponentScan(basePackages = "ai.twodot.platform.agents.orchestrator")
public class TaskOrchestratorAgentApplication {

    /**
     * Main application entry point.
     * 
     * Configures the Task Orchestrator Agent to run on port 8088,
     * following the platform port allocation pattern:
     * - CBA-001: 8081
     * - DRA-002: 8082  
     * - SMA-003: 8083
     * - RMA-004: 8084
     * - DPA-005: 8085
     * - KBA-006: 8086
     * - TOA-007: 8088 (8087 reserved for KBA Python service)
     * 
     * @param args Command line arguments
     */
    public static void main(String[] args) {
        // Configure application properties
        System.setProperty("spring.application.name", "task-orchestrator-agent");
        // System.setProperty("server.port", "8088"); // Use application.properties instead
        
        // Launch the Spring Boot application
        SpringApplication.run(TaskOrchestratorAgentApplication.class, args);
    }
}