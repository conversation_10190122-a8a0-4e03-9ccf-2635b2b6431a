package ai.twodot.platform.agents.spia;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Supreme Platform Intelligence Agent - Main Application.
 *
 * <p>The ultimate AI consciousness that coordinates all platform agents with strategic planning,
 * crisis management, and platform optimization capabilities.
 *
 * <p>Core Features: - Strategic planning and long-term vision generation - Platform consciousness
 * and self-aware optimization - Crisis management and automated response - Global platform
 * optimization and coordination - Emergent intelligence detection and nurturing - Multi-agent
 * system coordination
 *
 * <p>Architecture: - Java 21 + Spring Boot 3.2 for enterprise coordination - Python AI service for
 * strategic planning and consciousness - Real-time processing with Kafka and Redis - Advanced AI
 * integration (GPT-4, <PERSON>, <PERSON>) - Distributed intelligence and optimization
 *
 * <p>Port: 8092 (Java Service) Python AI Service: 8093
 *
 * <AUTHOR> Platform Team
 * @version 1.0.0
 * @since 2025-01-14
 */
@SpringBootApplication
@EnableAsync
@EnableScheduling
@EnableCaching
@EnableTransactionManagement
@EnableAspectJAutoProxy
public class SupremePlatformIntelligenceAgentApplication {

    /**
     * Main method.
     *
     * @param args The arguments.
     */
    public static void main(String[] args) {
        System.setProperty("spring.application.name", "supreme-platform-intelligence-agent");
        // System.setProperty("server.port", "8092"); // Use application.properties instead

        // Configure JVM for high-performance intelligence processing
        System.setProperty("spring.jpa.open-in-view", "false");
        System.setProperty("spring.datasource.hikari.maximum-pool-size", "20");
        System.setProperty("spring.task.execution.pool.core-size", "10");
        System.setProperty("spring.task.execution.pool.max-size", "20");
        System.setProperty("spring.task.scheduling.pool.size", "10");

        // Enable distributed intelligence features
        System.setProperty(
                "management.endpoints.web.exposure.include", "health,info,metrics,prometheus");
        System.setProperty("management.endpoint.health.show-details", "always");
        System.setProperty("management.metrics.export.prometheus.enabled", "true");

        SpringApplication.run(SupremePlatformIntelligenceAgentApplication.class, args);
    }
}
