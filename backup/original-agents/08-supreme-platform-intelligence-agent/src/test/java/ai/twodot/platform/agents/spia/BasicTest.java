package ai.twodot.platform.agents.spia;

import static org.junit.jupiter.api.Assertions.*;

import ai.twodot.platform.agents.spia.data.IntelligenceModels.*;
import java.time.Instant;
import java.util.List;
import org.junit.jupiter.api.Test;

/** Basic tests for SPIA data models and functionality */
public class BasicTest {

    @Test
    void testDataModelCreation() {
        // Test StrategicPlan creation
        StrategicPlan plan =
                new StrategicPlan(
                        "plan-001",
                        "Test Plan",
                        "Test Description",
                        List.of("Objective 1", "Objective 2"),
                        PlanPriority.HIGH,
                        0.95,
                        ApprovalStatus.DRAFT,
                        "SPIA-008",
                        Instant.now(),
                        Instant.now());

        assertNotNull(plan);
        assertEquals("plan-001", plan.planId());
        assertEquals("Test Plan", plan.title());
        assertEquals(PlanPriority.HIGH, plan.priority());
        assertEquals(0.95, plan.confidenceScore());
        assertEquals(2, plan.objectives().size());
    }

    @Test
    void testCrisisEventCreation() {
        CrisisEvent crisis =
                new CrisisEvent(
                        "crisis-001",
                        "Test Crisis",
                        "Test crisis description",
                        CrisisType.TECHNICAL,
                        CrisisLevel.HIGH,
                        CrisisScope.SERVICE,
                        List.of("api", "database"),
                        Instant.now(),
                        Instant.now(),
                        CrisisStatus.DETECTED,
                        Instant.now(),
                        Instant.now());

        assertNotNull(crisis);
        assertEquals("crisis-001", crisis.crisisId());
        assertEquals(CrisisType.TECHNICAL, crisis.type());
        assertEquals(CrisisLevel.HIGH, crisis.level());
        assertEquals(2, crisis.affectedSystems().size());
    }

    @Test
    void testPlatformConsciousnessCreation() {
        PlatformConsciousness consciousness =
                new PlatformConsciousness(
                        "consciousness-001",
                        ConsciousnessState.AWARE,
                        0.8,
                        0.75,
                        List.of(),
                        Instant.now(),
                        Instant.now());

        assertNotNull(consciousness);
        assertEquals("consciousness-001", consciousness.consciousnessId());
        assertEquals(ConsciousnessState.AWARE, consciousness.state());
        assertEquals(0.8, consciousness.awarenessLevel());
    }

    @Test
    void testEnumValues() {
        // Test PlanPriority enum
        assertEquals(5, PlanPriority.values().length);
        assertTrue(List.of(PlanPriority.values()).contains(PlanPriority.HIGH));

        // Test CrisisLevel enum
        assertEquals(5, CrisisLevel.values().length);
        assertTrue(List.of(CrisisLevel.values()).contains(CrisisLevel.CRITICAL));

        // Test ConsciousnessState enum
        assertEquals(5, ConsciousnessState.values().length);
        assertTrue(List.of(ConsciousnessState.values()).contains(ConsciousnessState.SELF_AWARE));

        // Test IntelligenceLevel enum
        assertEquals(5, IntelligenceLevel.values().length);
        assertTrue(List.of(IntelligenceLevel.values()).contains(IntelligenceLevel.SUPREME));
    }

    @Test
    void testStrategicObjectiveCreation() {
        StrategicObjective objective =
                new StrategicObjective(
                        "obj-001",
                        "Performance Improvement",
                        "Improve system performance by 30%",
                        100.0,
                        70.0,
                        "percentage",
                        70.0,
                        Instant.now(),
                        Instant.now());

        assertNotNull(objective);
        assertEquals("obj-001", objective.objectiveId());
        assertEquals("Performance Improvement", objective.title());
        assertEquals(100.0, objective.targetValue());
        assertEquals(70.0, objective.currentValue());
        assertEquals(70.0, objective.progressPercentage());
    }

    @Test
    void testCrisisResponseCreation() {
        CrisisResponse response =
                new CrisisResponse(
                        "response-001",
                        "crisis-001",
                        ResponseType.AUTOMATED,
                        ResponsePriority.HIGH,
                        "Automated action required",
                        0.9,
                        ExecutionStatus.EXECUTING,
                        Instant.now(),
                        Instant.now());

        assertNotNull(response);
        assertEquals("response-001", response.responseId());
        assertEquals("crisis-001", response.crisisId());
        assertEquals(ResponseType.AUTOMATED, response.responseType());
        assertEquals(0.9, response.effectivenessScore());
    }

    @Test
    void testApplicationClassExists() {
        // Verify the main application class exists
        assertDoesNotThrow(
                () -> {
                    Class.forName(
                            "ai.twodot.platform.agents.spia.SupremePlatformIntelligenceAgentApplication");
                });
    }

    @Test
    void testAgentClassExists() {
        // Verify the agent class exists
        assertDoesNotThrow(
                () -> {
                    Class.forName(
                            "ai.twodot.platform.agents.spia.agent.SupremePlatformIntelligenceAgent");
                });
    }

    @Test
    void testDataModelValidation() {
        // Test that all core enums have expected values

        // Intelligence Level
        IntelligenceLevel[] intelligenceLevels = IntelligenceLevel.values();
        assertEquals(5, intelligenceLevels.length);

        // Consciousness State
        ConsciousnessState[] consciousnessStates = ConsciousnessState.values();
        assertEquals(5, consciousnessStates.length);

        // Crisis Level
        CrisisLevel[] crisisLevels = CrisisLevel.values();
        assertEquals(5, crisisLevels.length);

        // Crisis Type
        CrisisType[] crisisTypes = CrisisType.values();
        assertEquals(5, crisisTypes.length);

        // Plan Priority
        PlanPriority[] planPriorities = PlanPriority.values();
        assertEquals(5, planPriorities.length);

        // Approval Status
        ApprovalStatus[] approvalStatuses = ApprovalStatus.values();
        assertEquals(5, approvalStatuses.length);
    }
}
