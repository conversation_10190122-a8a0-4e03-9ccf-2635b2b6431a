package ai.twodot.platform.agents.dataprocessing.streaming;

import ai.twodot.platform.agents.dataprocessing.data.DataModels.*;
import ai.twodot.platform.agents.dataprocessing.agent.DataProcessingAgent;
import ai.twodot.platform.agents.dataprocessing.agent.DataProcessingAgent.StreamProcessingConfig;

import org.apache.kafka.streams.KafkaStreams;
import org.apache.kafka.streams.StreamsBuilder;
import org.apache.kafka.streams.StreamsConfig;
import org.apache.kafka.streams.kstream.*;
import org.apache.kafka.streams.processor.api.Processor;
import org.apache.kafka.streams.processor.api.ProcessorContext;
import org.apache.kafka.streams.processor.api.Record;
import org.apache.kafka.common.serialization.Serdes;
import org.apache.kafka.common.utils.Bytes;
import org.apache.kafka.streams.state.KeyValueStore;
import org.apache.kafka.streams.state.WindowStore;
import ai.twodot.platform.agents.dataprocessing.data.DataModels.WindowConfig;
import org.apache.kafka.streams.KeyValue;
import org.apache.kafka.streams.errors.StreamsUncaughtExceptionHandler;
import org.apache.kafka.streams.kstream.SlidingWindows;
import org.apache.kafka.streams.kstream.TimeWindows;
import org.apache.kafka.streams.kstream.Windowed;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Real-time Stream Processing Engine for Data Processing Agent
 * 
 * Handles high-throughput stream processing with Kafka Streams,
 * supporting windowed aggregations, stateful processing, and
 * real-time transformations.
 */
@Service
public class StreamProcessingEngineImpl implements DataProcessingAgent.StreamProcessingEngine {

    private static final Logger logger = LoggerFactory.getLogger(StreamProcessingEngineImpl.class);

    @Value("${kafka.bootstrap.servers:localhost:9092}")
    private String bootstrapServers;

    @Value("${kafka.application.id:data-processing-agent}")
    private String applicationId;

    @Value("${stream.processing.threads:4}")
    private int streamThreads;

    @Value("${stream.processing.cache.max.bytes:10485760}")
    private long cacheMaxBytes;

    @Autowired
    private DataTransformationService transformationService;

    @Autowired
    private DataQualityService qualityService;

    // Active streams management
    private final Map<String, KafkaStreams> activeStreams = new ConcurrentHashMap<>();
    private final Map<String, StreamProcessingConfig> streamConfigs = new ConcurrentHashMap<>();
    private final Map<String, StreamMetrics> streamMetrics = new ConcurrentHashMap<>();
    
    private final AtomicBoolean isHealthy = new AtomicBoolean(true);
    private final AtomicLong totalRecordsProcessed = new AtomicLong(0);
    private final AtomicLong totalStreamsStarted = new AtomicLong(0);

    /**
     * Start a new stream processing pipeline
     */
    @Override
    public String startStream(StreamProcessingConfig config) {
        String streamId = generateStreamId(config);
        
        logger.info("Starting stream processing: {}", streamId);
        
        try {
            // Create Kafka Streams configuration
            Properties props = createStreamProperties(streamId, config);
            
            // Build the stream topology
            StreamsBuilder builder = new StreamsBuilder();
            buildStreamTopology(builder, config);
            
            // Create and start the Kafka Streams instance
            KafkaStreams streams = new KafkaStreams(builder.build(), props);
            
            // Set up state listener
            streams.setStateListener((newState, oldState) -> {
                logger.info("Stream {} state changed from {} to {}", streamId, oldState, newState);
                if (newState == KafkaStreams.State.ERROR) {
                    handleStreamError(streamId);
                }
            });
            
            // Set up exception handler
            streams.setUncaughtExceptionHandler(exception -> {
                logger.error("Uncaught exception in stream {}: {}", streamId, exception.getMessage(), exception);
                return StreamsUncaughtExceptionHandler.StreamThreadExceptionResponse.REPLACE_THREAD;
            });
            
            // Start the stream
            streams.start();
            
            // Store stream references
            activeStreams.put(streamId, streams);
            streamConfigs.put(streamId, config);
            streamMetrics.put(streamId, new StreamMetrics(streamId));
            
            totalStreamsStarted.incrementAndGet();
            
            logger.info("Stream {} started successfully", streamId);
            return streamId;
            
        } catch (Exception e) {
            logger.error("Failed to start stream {}: {}", streamId, e.getMessage(), e);
            throw new RuntimeException("Stream start failed: " + e.getMessage(), e);
        }
    }

    /**
     * Stop a running stream
     */
    @Override
    public void stopStream(String streamId) {
        logger.info("Stopping stream: {}", streamId);
        
        KafkaStreams streams = activeStreams.get(streamId);
        if (streams != null) {
            try {
                // Close the stream gracefully
                streams.close(Duration.ofSeconds(30));
                
                // Remove from active streams
                activeStreams.remove(streamId);
                streamConfigs.remove(streamId);
                
                // Log final metrics
                StreamMetrics metrics = streamMetrics.remove(streamId);
                if (metrics != null) {
                    logger.info("Stream {} final metrics: processed={}, errors={}, throughput={}/sec",
                        streamId, metrics.recordsProcessed, metrics.errors, 
                        metrics.getThroughput());
                }
                
                logger.info("Stream {} stopped successfully", streamId);
                
            } catch (Exception e) {
                logger.error("Error stopping stream {}: {}", streamId, e.getMessage(), e);
                throw new RuntimeException("Failed to stop stream: " + e.getMessage(), e);
            }
        } else {
            logger.warn("Stream {} not found or already stopped", streamId);
        }
    }

    /**
     * Get count of active streams
     */
    @Override
    public int getActiveStreamCount() {
        return activeStreams.size();
    }

    /**
     * Check if the stream processing engine is healthy
     */
    @Override
    public boolean isHealthy() {
        // Check if all active streams are running
        for (Map.Entry<String, KafkaStreams> entry : activeStreams.entrySet()) {
            if (entry.getValue().state() == KafkaStreams.State.ERROR) {
                logger.warn("Stream {} is in ERROR state", entry.getKey());
                return false;
            }
        }
        
        return isHealthy.get();
    }

    /**
     * Get stream status
     */
    public Map<String, Object> getStreamStatus(String streamId) {
        KafkaStreams streams = activeStreams.get(streamId);
        StreamMetrics metrics = streamMetrics.get(streamId);
        
        if (streams == null) {
            return Map.of("status", "NOT_FOUND", "stream_id", streamId);
        }
        
        Map<String, Object> status = new HashMap<>();
        status.put("stream_id", streamId);
        status.put("state", streams.state().toString());
        status.put("is_running", streams.state().isRunningOrRebalancing());
        
        if (metrics != null) {
            status.put("records_processed", metrics.recordsProcessed);
            status.put("errors", metrics.errors);
            status.put("throughput_per_sec", metrics.getThroughput());
            status.put("average_latency_ms", metrics.getAverageLatency());
        }
        
        return status;
    }

    /**
     * Get all active streams
     */
    public List<Map<String, Object>> listActiveStreams() {
        List<Map<String, Object>> streamList = new ArrayList<>();
        
        for (String streamId : activeStreams.keySet()) {
            streamList.add(getStreamStatus(streamId));
        }
        
        return streamList;
    }

    // Private helper methods

    private Properties createStreamProperties(String streamId, StreamProcessingConfig config) {
        Properties props = new Properties();
        
        props.put(StreamsConfig.APPLICATION_ID_CONFIG, applicationId + "-" + streamId);
        props.put(StreamsConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(StreamsConfig.DEFAULT_KEY_SERDE_CLASS_CONFIG, Serdes.String().getClass());
        props.put(StreamsConfig.DEFAULT_VALUE_SERDE_CLASS_CONFIG, Serdes.String().getClass());
        props.put(StreamsConfig.NUM_STREAM_THREADS_CONFIG, config.parallelism());
        props.put(StreamsConfig.CACHE_MAX_BYTES_BUFFERING_CONFIG, cacheMaxBytes);
        props.put(StreamsConfig.COMMIT_INTERVAL_MS_CONFIG, 1000);
        props.put(StreamsConfig.PROCESSING_GUARANTEE_CONFIG, StreamsConfig.EXACTLY_ONCE_V2);
        
        return props;
    }

    private void buildStreamTopology(StreamsBuilder builder, StreamProcessingConfig config) {
        // Create the source stream
        KStream<String, String> sourceStream = builder.stream(config.sourceTopic());
        
        // Apply transformations
        KStream<String, String> transformedStream = sourceStream;
        
        for (DataTransformation transformation : config.transformations()) {
            transformedStream = applyTransformation(transformedStream, transformation);
        }
        
        // Apply windowing if configured
        if (config.windowConfig() != null) {
            transformedStream = applyWindowing(transformedStream, config.windowConfig());
        }
        
        // Apply quality checks
        transformedStream = transformedStream.filter((key, value) -> {
            try {
                return qualityService.validateData(value);
            } catch (Exception e) {
                logger.warn("Quality check failed for record: {}", e.getMessage());
                return false;
            }
        });
        
        // Process and forward to destination
        transformedStream.process(() -> new StreamProcessor(config.streamId()));
        
        // Send to destination topic
        transformedStream.to(config.destinationTopic());
    }

    private KStream<String, String> applyTransformation(KStream<String, String> stream, 
                                                       DataTransformation transformation) {
        return stream.mapValues(value -> {
            try {
                return transformationService.transform(value, transformation);
            } catch (Exception e) {
                logger.error("Transformation {} failed: {}", 
                    transformation.transformationId(), e.getMessage());
                return value; // Return original on error
            }
        });
    }

    private KStream<String, String> applyWindowing(KStream<String, String> stream, 
                                                   WindowConfig windowConfig) {
        if ("tumbling".equals(windowConfig.type())) {
            return stream
                .groupByKey()
                .windowedBy(TimeWindows.ofSizeWithNoGrace(Duration.ofMillis(windowConfig.sizeMs())))
                .count(Materialized.as("windowed-counts"))
                .toStream()
                .map((windowedKey, count) -> 
                    KeyValue.pair(windowedKey.key(), 
                                  String.format("{\"key\":\"%s\",\"window_start\":%d,\"count\":%d}",
                                              windowedKey.key(), 
                                              windowedKey.window().start(),
                                              count)));
        } else if ("sliding".equals(windowConfig.type())) {
            return stream
                .groupByKey()
                .windowedBy(SlidingWindows.ofTimeDifferenceWithNoGrace(
                    Duration.ofMillis(windowConfig.sizeMs())))
                .count(Materialized.as("sliding-counts"))
                .toStream()
                .map((windowedKey, count) -> 
                    KeyValue.pair(windowedKey.key(), 
                                  String.format("{\"key\":\"%s\",\"window_start\":%d,\"count\":%d}",
                                              windowedKey.key(), 
                                              windowedKey.window().start(),
                                              count)));
        }
        
        return stream;
    }

    private String generateStreamId(StreamProcessingConfig config) {
        return String.format("%s-%s-%d", 
            config.streamId(), 
            config.sourceTopic().replaceAll("[^a-zA-Z0-9]", "-"),
            System.currentTimeMillis());
    }

    private void handleStreamError(String streamId) {
        logger.error("Stream {} encountered an error, attempting recovery", streamId);
        
        // Update metrics
        StreamMetrics metrics = streamMetrics.get(streamId);
        if (metrics != null) {
            metrics.errors++;
        }
        
        // Attempt to restart the stream
        StreamProcessingConfig config = streamConfigs.get(streamId);
        if (config != null) {
            stopStream(streamId);
            
            // Wait before restart
            try {
                Thread.sleep(5000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            
            // Restart
            startStream(config);
        }
    }

    /**
     * Stream processor for custom processing logic
     */
    private class StreamProcessor implements Processor<String, String, String, String> {
        private ProcessorContext<String, String> context;
        private final String streamId;
        private final StreamMetrics metrics;
        
        public StreamProcessor(String streamId) {
            this.streamId = streamId;
            this.metrics = streamMetrics.get(streamId);
        }
        
        @Override
        public void init(ProcessorContext<String, String> context) {
            this.context = context;
        }
        
        @Override
        public void process(Record<String, String> record) {
            long startTime = System.currentTimeMillis();
            
            try {
                // Update global metrics
                totalRecordsProcessed.incrementAndGet();
                
                // Update stream metrics
                if (metrics != null) {
                    metrics.recordsProcessed++;
                    metrics.lastProcessedTime = startTime;
                }
                
                // Log processing (sample)
                if (totalRecordsProcessed.get() % 1000 == 0) {
                    logger.debug("Stream {} processed {} records", 
                        streamId, metrics != null ? metrics.recordsProcessed : 0);
                }
                
                // Forward the record
                context.forward(record);
                
                // Update latency
                if (metrics != null) {
                    metrics.totalLatency += (System.currentTimeMillis() - startTime);
                }
                
            } catch (Exception e) {
                logger.error("Error processing record in stream {}: {}", 
                    streamId, e.getMessage(), e);
                if (metrics != null) {
                    metrics.errors++;
                }
            }
        }
        
        @Override
        public void close() {
            // Cleanup if needed
        }
    }

    /**
     * Stream metrics tracking
     */
    private static class StreamMetrics {
        private final String streamId;
        private volatile long recordsProcessed = 0;
        private volatile long errors = 0;
        private volatile long totalLatency = 0;
        private volatile long startTime = System.currentTimeMillis();
        private volatile long lastProcessedTime = System.currentTimeMillis();
        
        public StreamMetrics(String streamId) {
            this.streamId = streamId;
        }
        
        public double getThroughput() {
            long elapsed = System.currentTimeMillis() - startTime;
            if (elapsed > 0) {
                return (recordsProcessed * 1000.0) / elapsed;
            }
            return 0.0;
        }
        
        public double getAverageLatency() {
            if (recordsProcessed > 0) {
                return (double) totalLatency / recordsProcessed;
            }
            return 0.0;
        }
    }

    // Supporting services (interfaces to be implemented)
    
    public interface DataTransformationService {
        String transform(String data, DataTransformation transformation);
    }
    
    public interface DataQualityService {
        boolean validateData(String data);
    }
}