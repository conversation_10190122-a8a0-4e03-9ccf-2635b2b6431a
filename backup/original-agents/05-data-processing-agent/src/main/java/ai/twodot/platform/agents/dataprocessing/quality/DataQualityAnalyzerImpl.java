package ai.twodot.platform.agents.dataprocessing.quality;

import ai.twodot.platform.agents.dataprocessing.agent.DataProcessingAgent;
import ai.twodot.platform.agents.dataprocessing.data.DataModels;
import org.springframework.stereotype.Service;

@Service
public class DataQualityAnalyzerImpl implements DataProcessingAgent.DataQualityAnalyzer {
    @Override
    public DataModels.QualityMetrics analyzeDataset(DataProcessingAgent.DataQualityRequest request) {
        return null;
    }

    @Override
    public DataModels.QualityMetrics analyzePipelineOutput(DataModels.DataPipeline pipeline, DataModels.ProcessingResult result) {
        return null;
    }

    @Override
    public boolean isHealthy() {
        return true;
    }
}
