package ai.twodot.platform.agents.dataprocessing.a2a;

import ai.twodot.platform.agents.dataprocessing.data.DataModels.*;
import ai.twodot.platform.agents.dataprocessing.agent.DataProcessingAgent;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.http.MediaType;
import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;

import reactor.core.publisher.Mono;
import reactor.core.publisher.Flux;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Agent-to-Agent (A2A) Integration Service for Data Processing Agent
 * 
 * Handles communication and coordination with other agents in the platform:
 * - Communication Broker Agent (CBA-001)
 * - Discovery Registry Agent (DRA-002)
 * - Security Monitor Agent (SMA-003)
 * - Resource Manager Agent (RMA-004)
 */
@Service
public class A2AIntegrationService {

    private static final Logger logger = LoggerFactory.getLogger(A2AIntegrationService.class);

    @Value("${a2a.cba.url:http://localhost:8081}")
    private String cbaUrl;

    @Value("${a2a.dra.url:http://localhost:8082}")
    private String draUrl;

    @Value("${a2a.sma.url:http://localhost:8083}")
    private String smaUrl;

    @Value("${a2a.rma.url:http://localhost:8084}")
    private String rmaUrl;

    @Value("${a2a.timeout.seconds:30}")
    private int timeoutSeconds;

    @Value("${a2a.retry.attempts:3}")
    private int retryAttempts;

    @Autowired
    private WebClient.Builder webClientBuilder;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private DataProcessingAgent dataProcessingAgent;

    private final Map<String, WebClient> agentClients = new ConcurrentHashMap<>();
    private final Map<String, AgentStatus> agentStatuses = new ConcurrentHashMap<>();
    private final AtomicLong totalA2ARequests = new AtomicLong(0);
    private final AtomicLong successfulRequests = new AtomicLong(0);
    private final AtomicLong failedRequests = new AtomicLong(0);

    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);

    /**
     * Initialize A2A integration
     */
    public void initialize() {
        logger.info("Initializing A2A Integration Service...");
        
        // Create WebClient instances for each agent
        agentClients.put("CBA", createWebClient(cbaUrl));
        agentClients.put("DRA", createWebClient(draUrl));
        agentClients.put("SMA", createWebClient(smaUrl));
        agentClients.put("RMA", createWebClient(rmaUrl));

        // Register with Discovery Registry Agent
        registerWithDRA();

        // Start health check scheduling
        scheduler.scheduleAtFixedRate(this::performHealthChecks, 30, 60, TimeUnit.SECONDS);

        // Start metrics reporting
        scheduler.scheduleAtFixedRate(this::reportMetrics, 60, 300, TimeUnit.SECONDS);

        logger.info("✅ A2A Integration Service initialized successfully");
    }

    /**
     * Register this agent with Discovery Registry Agent (DRA)
     */
    public void registerWithDRA() {
        logger.info("Registering with Discovery Registry Agent...");
        
        AgentRegistration registration = new AgentRegistration(
            "DPA-005",
            "Data Processing Agent",
            "localhost", // Will be updated with actual hostname
            8085, // DPA port
            "ACTIVE",
            Map.of(
                "version", "1.0.0",
                "capabilities", List.of(
                    "data-processing", "stream-processing", "data-quality", 
                    "anomaly-detection", "transformation-optimization"
                ),
                "protocols", List.of("REST", "A2A"),
                "health_endpoint", "/actuator/health"
            ),
            Instant.now()
        );

        sendA2ARequest("DRA", "/agents/register", registration, AgentRegistrationResponse.class)
            .subscribe(
                response -> {
                    logger.info("Successfully registered with DRA: {}", response.agentId());
                    agentStatuses.put("DRA", new AgentStatus("DRA", "CONNECTED", Instant.now()));
                },
                error -> {
                    logger.error("Failed to register with DRA: {}", error.getMessage());
                    agentStatuses.put("DRA", new AgentStatus("DRA", "DISCONNECTED", Instant.now()));
                }
            );
    }

    /**
     * Request data processing workflow coordination from CBA
     */
    public CompletableFuture<WorkflowResponse> requestWorkflowCoordination(
            DataProcessingWorkflow workflow) {
        logger.info("Requesting workflow coordination from CBA: {}", workflow.workflowId());
        
        WorkflowRequest request = new WorkflowRequest(
            workflow.workflowId(),
            "DPA-005",
            workflow.steps(),
            workflow.dependencies(),
            workflow.priority(),
            Instant.now()
        );

        return sendA2ARequest("CBA", "/workflows/coordinate", request, WorkflowResponse.class)
            .doOnSuccess(response -> 
                logger.info("Workflow coordination successful: {}", response.coordinationId()))
            .doOnError(error -> 
                logger.error("Workflow coordination failed: {}", error.getMessage()))
            .toFuture();
    }

    /**
     * Request resource allocation from RMA
     */
    public CompletableFuture<ResourceAllocationResponse> requestResourceAllocation(
            ResourceRequest resourceRequest) {
        logger.info("Requesting resource allocation from RMA: {} cores, {} MB memory", 
            resourceRequest.cpuCores(), resourceRequest.memoryMb());
        
        A2AResourceRequest request = new A2AResourceRequest(
            "DPA-005",
            resourceRequest.resourceType(),
            resourceRequest.cpuCores(),
            resourceRequest.memoryMb(),
            resourceRequest.duration(),
            resourceRequest.priority(),
            Instant.now()
        );

        return sendA2ARequest("RMA", "/resources/allocate", request, ResourceAllocationResponse.class)
            .doOnSuccess(response -> 
                logger.info("Resource allocation successful: allocation ID = {}", 
                    response.allocationId()))
            .doOnError(error -> 
                logger.error("Resource allocation failed: {}", error.getMessage()))
            .toFuture();
    }

    /**
     * Report security event to SMA
     */
    public CompletableFuture<SecurityResponse> reportSecurityEvent(SecurityEvent event) {
        logger.info("Reporting security event to SMA: {} - {}", 
            event.eventType(), event.severity());
        
        SecurityEventReport report = new SecurityEventReport(
            event.eventId(),
            "DPA-005",
            event.eventType(),
            event.severity(),
            event.description(),
            event.metadata(),
            Instant.now()
        );

        return sendA2ARequest("SMA", "/security/events", report, SecurityResponse.class)
            .doOnSuccess(response -> 
                logger.info("Security event reported successfully: {}", response.eventId()))
            .doOnError(error -> 
                logger.error("Security event reporting failed: {}", error.getMessage()))
            .toFuture();
    }

    /**
     * Query agent capabilities from DRA
     */
    public CompletableFuture<List<AgentCapability>> queryAgentCapabilities(String capability) {
        logger.debug("Querying agents with capability: {}", capability);
        
        return sendA2ARequest("DRA", "/agents/query?capability=" + capability, 
                null, new TypeReference<List<AgentCapability>>() {})
            .doOnSuccess(capabilities -> 
                logger.debug("Found {} agents with capability: {}", capabilities.size(), capability))
            .toFuture();
    }

    /**
     * Send data quality metrics to monitoring
     */
    public CompletableFuture<Void> sendDataQualityMetrics(DataQualityMetricsReport metrics) {
        logger.debug("Sending data quality metrics to CBA for distribution");
        
        MetricsUpdate update = new MetricsUpdate(
            "DPA-005",
            "data_quality",
            metrics.overallScore(),
            metrics.details(),
            Instant.now()
        );

        return sendA2ARequest("CBA", "/metrics/update", update, Void.class)
            .doOnSuccess(v -> logger.debug("Data quality metrics sent successfully"))
            .doOnError(error -> logger.warn("Failed to send metrics: {}", error.getMessage()))
            .then()
            .toFuture();
    }

    /**
     * Request data processing collaboration
     */
    public CompletableFuture<CollaborationResponse> requestDataProcessingCollaboration(
            CollaborationRequest collaborationRequest) {
        logger.info("Requesting data processing collaboration for pipeline: {}", 
            collaborationRequest.pipelineId());
        
        A2ACollaborationRequest request = new A2ACollaborationRequest(
            collaborationRequest.collaborationId(),
            "DPA-005",
            collaborationRequest.targetAgents(),
            collaborationRequest.pipelineId(),
            collaborationRequest.dataRequirements(),
            collaborationRequest.expectedOutcome(),
            Instant.now()
        );

        return sendA2ARequest("CBA", "/collaboration/request", request, CollaborationResponse.class)
            .doOnSuccess(response -> 
                logger.info("Collaboration request successful: {}", response.collaborationId()))
            .toFuture();
    }

    // Private helper methods

    private WebClient createWebClient(String baseUrl) {
        return webClientBuilder
            .baseUrl(baseUrl)
            .defaultHeader("User-Agent", "DPA-005/1.0.0")
            .defaultHeader("X-Agent-ID", "DPA-005")
            .build();
    }

    private <T> Mono<T> sendA2ARequest(String agentCode, String endpoint, Object body, Class<T> responseType) {
        return sendA2ARequest(agentCode, endpoint, body, new TypeReference<T>() {
            @Override
            public Class<T> getType() {
                return responseType;
            }
        });
    }

    private <T> Mono<T> sendA2ARequest(String agentCode, String endpoint, Object body, 
                                      TypeReference<T> responseType) {
        WebClient client = agentClients.get(agentCode);
        if (client == null) {
            return Mono.error(new IllegalArgumentException("Unknown agent: " + agentCode));
        }

        totalA2ARequests.incrementAndGet();

        Mono<T> request;
        if (body != null) {
            request = client.post()
                .uri(endpoint)
                .contentType(MediaType.APPLICATION_JSON)
                .body(BodyInserters.fromValue(body))
                .retrieve()
                .onStatus(status -> status.is4xxClientError() || status.is5xxServerError(), response -> {
                    failedRequests.incrementAndGet();
                    return Mono.error(new RuntimeException("HTTP error: " + response.statusCode()));
                })
                .bodyToMono(String.class)
                .map(responseBody -> {
                    try {
                        return objectMapper.readValue(responseBody, responseType);
                    } catch (Exception e) {
                        throw new RuntimeException("Failed to parse response", e);
                    }
                });
        } else {
            request = client.get()
                .uri(endpoint)
                .retrieve()
                .onStatus(status -> status.is4xxClientError() || status.is5xxServerError(), response -> {
                    failedRequests.incrementAndGet();
                    return Mono.error(new RuntimeException("HTTP error: " + response.statusCode()));
                })
                .bodyToMono(String.class)
                .map(responseBody -> {
                    try {
                        return objectMapper.readValue(responseBody, responseType);
                    } catch (Exception e) {
                        throw new RuntimeException("Failed to parse response", e);
                    }
                });
        }

        return request
            .timeout(Duration.ofSeconds(timeoutSeconds))
            .retryWhen(Retry.backoff(retryAttempts, Duration.ofSeconds(1))
                .filter(throwable -> !(throwable instanceof IllegalArgumentException)))
            .doOnSuccess(response -> successfulRequests.incrementAndGet())
            .doOnError(error -> {
                failedRequests.incrementAndGet();
                updateAgentStatus(agentCode, "ERROR");
                logger.error("A2A request to {} failed: {}", agentCode, error.getMessage());
            })
            .doOnSuccess(response -> updateAgentStatus(agentCode, "CONNECTED"));
    }

    private void updateAgentStatus(String agentCode, String status) {
        agentStatuses.put(agentCode, new AgentStatus(agentCode, status, Instant.now()));
    }

    private void performHealthChecks() {
        logger.debug("Performing A2A health checks...");
        
        for (String agentCode : agentClients.keySet()) {
            sendA2ARequest(agentCode, "/actuator/health", null, Map.class)
                .subscribe(
                    health -> {
                        updateAgentStatus(agentCode, "HEALTHY");
                        logger.debug("Agent {} is healthy", agentCode);
                    },
                    error -> {
                        updateAgentStatus(agentCode, "UNHEALTHY");
                        logger.warn("Agent {} health check failed: {}", agentCode, error.getMessage());
                    }
                );
        }
    }

    private void reportMetrics() {
        logger.debug("Reporting A2A metrics...");
        
        A2AMetrics metrics = new A2AMetrics(
            "DPA-005",
            totalA2ARequests.get(),
            successfulRequests.get(),
            failedRequests.get(),
            calculateSuccessRate(),
            new HashMap<>(agentStatuses),
            Instant.now()
        );

        // Send to CBA for aggregation
        sendA2ARequest("CBA", "/metrics/a2a", metrics, Void.class)
            .subscribe(
                v -> logger.debug("A2A metrics reported successfully"),
                error -> logger.warn("Failed to report A2A metrics: {}", error.getMessage())
            );
    }

    private double calculateSuccessRate() {
        long total = totalA2ARequests.get();
        if (total == 0) return 100.0;
        return (successfulRequests.get() * 100.0) / total;
    }

    /**
     * Get A2A integration status
     */
    public A2AStatus getA2AStatus() {
        return new A2AStatus(
            "DPA-005",
            new HashMap<>(agentStatuses),
            totalA2ARequests.get(),
            successfulRequests.get(),
            failedRequests.get(),
            calculateSuccessRate(),
            Instant.now()
        );
    }

    /**
     * Shutdown A2A integration
     */
    public void shutdown() {
        logger.info("Shutting down A2A Integration Service...");
        
        // Deregister from DRA
        sendA2ARequest("DRA", "/agents/deregister", 
            Map.of("agentId", "DPA-005"), Void.class)
            .subscribe(
                v -> logger.info("Successfully deregistered from DRA"),
                error -> logger.warn("Failed to deregister from DRA: {}", error.getMessage())
            );
        
        // Shutdown scheduler
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(10, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        logger.info("A2A Integration Service shutdown completed");
    }

    // Supporting record classes

    public record AgentRegistration(
        String agentId,
        String agentName,
        String hostname,
        int port,
        String status,
        Map<String, Object> metadata,
        Instant registrationTime
    ) {}

    public record AgentRegistrationResponse(
        String agentId,
        String status,
        String message,
        Instant timestamp
    ) {}

    public record WorkflowRequest(
        String workflowId,
        String requestingAgent,
        List<String> steps,
        Map<String, String> dependencies,
        String priority,
        Instant requestTime
    ) {}

    public record WorkflowResponse(
        String coordinationId,
        String status,
        Map<String, String> stepAssignments,
        String estimatedCompletion,
        Instant responseTime
    ) {}

    public record A2AResourceRequest(
        String requestingAgent,
        String resourceType,
        int cpuCores,
        long memoryMb,
        Duration duration,
        String priority,
        Instant requestTime
    ) {}

    public record ResourceAllocationResponse(
        String allocationId,
        String status,
        Map<String, Object> allocatedResources,
        Instant allocationTime,
        Instant expirationTime
    ) {}

    public record SecurityEventReport(
        String eventId,
        String reportingAgent,
        String eventType,
        String severity,
        String description,
        Map<String, Object> metadata,
        Instant eventTime
    ) {}

    public record SecurityResponse(
        String eventId,
        String status,
        List<String> actions,
        Instant responseTime
    ) {}

    public record AgentCapability(
        String agentId,
        String agentName,
        List<String> capabilities,
        String status,
        Map<String, Object> metadata
    ) {}

    public record MetricsUpdate(
        String agentId,
        String metricType,
        double value,
        Map<String, Object> details,
        Instant timestamp
    ) {}

    public record A2ACollaborationRequest(
        String collaborationId,
        String requestingAgent,
        List<String> targetAgents,
        String pipelineId,
        Map<String, Object> dataRequirements,
        String expectedOutcome,
        Instant requestTime
    ) {}

    public record CollaborationResponse(
        String collaborationId,
        String status,
        List<String> participatingAgents,
        Map<String, String> roles,
        Instant responseTime
    ) {}

    public record AgentStatus(
        String agentCode,
        String status,
        Instant lastUpdate
    ) {}

    public record A2AMetrics(
        String agentId,
        long totalRequests,
        long successfulRequests,
        long failedRequests,
        double successRate,
        Map<String, AgentStatus> agentStatuses,
        Instant timestamp
    ) {}

    public record A2AStatus(
        String agentId,
        Map<String, AgentStatus> agentStatuses,
        long totalRequests,
        long successfulRequests,
        long failedRequests,
        double successRate,
        Instant timestamp
    ) {}

    // External request types
    public record ResourceRequest(
        String resourceType,
        int cpuCores,
        long memoryMb,
        Duration duration,
        String priority
    ) {}

    public record SecurityEvent(
        String eventId,
        String eventType,
        String severity,
        String description,
        Map<String, Object> metadata
    ) {}

    public record DataQualityMetricsReport(
        double overallScore,
        Map<String, Object> details
    ) {}

    public record CollaborationRequest(
        String collaborationId,
        List<String> targetAgents,
        String pipelineId,
        Map<String, Object> dataRequirements,
        String expectedOutcome
    ) {}

    public record DataProcessingWorkflow(
        String workflowId,
        List<String> steps,
        Map<String, String> dependencies,
        String priority
    ) {}
}