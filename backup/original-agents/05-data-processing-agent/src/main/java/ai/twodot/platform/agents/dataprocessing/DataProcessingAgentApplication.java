package ai.twodot.platform.agents.dataprocessing;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Data Processing Agent (DPA-005)
 * 
 * AI-powered data processing and pipeline orchestration agent.
 * Provides intelligent data transformation, quality analysis, and real-time stream processing.
 */
@SpringBootApplication
@EnableAsync
@EnableScheduling
public class DataProcessingAgentApplication {

    private static final Logger logger = LoggerFactory.getLogger(DataProcessingAgentApplication.class);

    public static void main(String[] args) {
        logger.info("Starting Data Processing Agent (DPA-005)...");
        
        // Set system properties for optimal performance
        System.setProperty("spring.jpa.open-in-view", "false");
        System.setProperty("spring.kafka.consumer.enable-auto-commit", "false");
        System.setProperty("server.shutdown", "graceful");
        
        SpringApplication app = new SpringApplication(DataProcessingAgentApplication.class);
        
        // Configure additional properties
        app.setAdditionalProfiles("dpa");
        
        try {
            app.run(args);
            logger.info("Data Processing Agent started successfully");
        } catch (Exception e) {
            logger.error("Failed to start Data Processing Agent: {}", e.getMessage(), e);
            System.exit(1);
        }
    }

    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        logger.info("=".repeat(80));
        logger.info("🔄 DATA PROCESSING AGENT (DPA-005) READY");
        logger.info("=".repeat(80));
        logger.info("📊 Agent ID: DPA-005");
        logger.info("🔧 Capabilities: Data Processing, Stream Analytics, AI Transformations");
        logger.info("🌐 Architecture: Java 21 + Python (Hybrid)");
        logger.info("📡 A2A Integration: CBA, DRA, SMA, RMA");
        logger.info("⚡ Performance: High-throughput data pipeline orchestration");
        logger.info("🔍 AI Features: Quality analysis, anomaly detection, intelligent transformations");
        logger.info("=".repeat(80));
        
        // Log available endpoints
        logger.info("📋 Available endpoints:");
        logger.info("  🏥 Health: /actuator/health");
        logger.info("  📊 Metrics: /actuator/metrics");
        logger.info("  📈 Prometheus: /actuator/prometheus");
        logger.info("  🔄 Data Pipeline: /api/v1/pipeline/**");
        logger.info("  📤 Stream Processing: /api/v1/stream/**");
        logger.info("  🔍 Data Quality: /api/v1/quality/**");
        logger.info("  🤖 AI Transformations: /api/v1/ai/**");
        logger.info("  🔗 A2A Communication: /api/v1/a2a/**");
        logger.info("=".repeat(80));
    }
}