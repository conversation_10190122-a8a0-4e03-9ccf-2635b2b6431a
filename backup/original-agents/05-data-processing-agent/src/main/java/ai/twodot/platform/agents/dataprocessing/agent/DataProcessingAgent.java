package ai.twodot.platform.agents.dataprocessing.agent;

import ai.twodot.platform.agents.dataprocessing.data.DataModels.*;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Data Processing Agent (DPA-005)
 * 
 * Core agent implementation for AI-powered data processing and pipeline orchestration.
 */
@Service
public class DataProcessingAgent {

    private static final Logger logger = LoggerFactory.getLogger(DataProcessingAgent.class);

    @Autowired
    private DataPipelineOrchestrator pipelineOrchestrator;

    @Autowired
    private StreamProcessingEngine streamEngine;

    @Autowired
    private DataQualityAnalyzer qualityAnalyzer;

    @Autowired
    private AiTransformationEngine aiEngine;

    // Agent state and metrics
    private final AtomicBoolean isHealthy = new AtomicBoolean(true);
    private final AtomicLong pipelinesExecuted = new AtomicLong(0);
    private final AtomicLong recordsProcessed = new AtomicLong(0);
    private final AtomicLong qualityChecksPerformed = new AtomicLong(0);
    private final AtomicLong anomaliesDetected = new AtomicLong(0);
    
    private final Map<String, DataPipeline> activePipelines = new ConcurrentHashMap<>();
    private final Map<String, ProcessingResult> recentResults = new ConcurrentHashMap<>();

    /**
     * Execute a data processing pipeline
     */
    public CompletableFuture<ProcessingResult> executePipeline(DataPipeline pipeline) {
        logger.info("Executing data pipeline: {}", pipeline.pipelineId());
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                pipelinesExecuted.incrementAndGet();
                activePipelines.put(pipeline.pipelineId(), pipeline);
                
                Instant startTime = Instant.now();
                
                // 1. Validate pipeline configuration
                validatePipeline(pipeline);
                
                // 2. Execute data processing steps
                ProcessingResult result = pipelineOrchestrator.executePipeline(pipeline);
                
                // 3. Perform quality analysis
                QualityMetrics qualityMetrics = qualityAnalyzer.analyzePipelineOutput(
                    pipeline, result
                );
                
                // 4. Update metrics
                updateMetrics(result, qualityMetrics);
                
                // 5. Create final result
                ProcessingResult finalResult = new ProcessingResult(
                    UUID.randomUUID().toString(),
                    pipeline.pipelineId(),
                    ProcessingStatus.COMPLETED,
                    result.recordsProcessed(),
                    result.recordsSucceeded(),
                    result.recordsFailed(),
                    System.currentTimeMillis() - startTime.toEpochMilli(),
                    qualityMetrics,
                    result.errorSummary(),
                    result.lineage(),
                    startTime,
                    Instant.now()
                );
                
                // Store result and clean up
                recentResults.put(pipeline.pipelineId(), finalResult);
                activePipelines.remove(pipeline.pipelineId());
                
                logger.info("Pipeline {} completed successfully: {} records processed", 
                    pipeline.pipelineId(), finalResult.recordsProcessed());
                
                return finalResult;
                
            } catch (Exception e) {
                activePipelines.remove(pipeline.pipelineId());
                logger.error("Pipeline {} execution failed: {}", pipeline.pipelineId(), e.getMessage(), e);
                
                return new ProcessingResult(
                    UUID.randomUUID().toString(),
                    pipeline.pipelineId(),
                    ProcessingStatus.FAILED,
                    0, 0, 0, 0,
                    null,
                    new ErrorSummary(1, Map.of(e.getClass().getSimpleName(), 1L), 
                        List.of(e.getMessage())),
                    null,
                    Instant.now(),
                    Instant.now()
                );
            }
        });
    }

    /**
     * Start real-time stream processing
     */
    public CompletableFuture<String> startStreamProcessing(StreamProcessingConfig config) {
        logger.info("Starting stream processing: {}", config.streamId());
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                String streamId = streamEngine.startStream(config);
                logger.info("Stream processing started successfully: {}", streamId);
                return streamId;
                
            } catch (Exception e) {
                logger.error("Failed to start stream processing: {}", e.getMessage(), e);
                throw new RuntimeException("Stream processing failed: " + e.getMessage());
            }
        });
    }

    /**
     * Stop stream processing
     */
    public CompletableFuture<Void> stopStreamProcessing(String streamId) {
        logger.info("Stopping stream processing: {}", streamId);
        
        return CompletableFuture.runAsync(() -> {
            try {
                streamEngine.stopStream(streamId);
                logger.info("Stream processing stopped: {}", streamId);
                
            } catch (Exception e) {
                logger.error("Failed to stop stream processing {}: {}", streamId, e.getMessage(), e);
                throw new RuntimeException("Failed to stop stream: " + e.getMessage());
            }
        });
    }

    /**
     * Analyze data quality for a dataset
     */
    public CompletableFuture<QualityMetrics> analyzeDataQuality(DataQualityRequest request) {
        logger.info("Analyzing data quality for dataset: {}", request.datasetId());
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                qualityChecksPerformed.incrementAndGet();
                
                QualityMetrics metrics = qualityAnalyzer.analyzeDataset(request);
                
                // Update anomaly count
                anomaliesDetected.addAndGet(metrics.anomaliesDetected().size());
                
                logger.info("Data quality analysis completed for {}: overall score = {}", 
                    request.datasetId(), metrics.overallQualityScore());
                
                return metrics;
                
            } catch (Exception e) {
                logger.error("Data quality analysis failed for {}: {}", 
                    request.datasetId(), e.getMessage(), e);
                throw new RuntimeException("Quality analysis failed: " + e.getMessage());
            }
        });
    }

    /**
     * Execute AI-powered data transformation
     */
    public CompletableFuture<AiTransformationResult> executeAiTransformation(
            AiTransformationRequest request) {
        logger.info("Executing AI transformation: {}", request.transformationId());
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                AiTransformationResult result = aiEngine.executeTransformation(request);
                
                logger.info("AI transformation {} completed: {} records transformed", 
                    request.transformationId(), result.recordsTransformed());
                
                return result;
                
            } catch (Exception e) {
                logger.error("AI transformation {} failed: {}", 
                    request.transformationId(), e.getMessage(), e);
                throw new RuntimeException("AI transformation failed: " + e.getMessage());
            }
        });
    }

    /**
     * Get agent metrics
     */
    public Map<String, Object> getMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        metrics.put("agent_status", isHealthy.get() ? "HEALTHY" : "UNHEALTHY");
        metrics.put("pipelines_executed", pipelinesExecuted.get());
        metrics.put("records_processed", recordsProcessed.get());
        metrics.put("quality_checks_performed", qualityChecksPerformed.get());
        metrics.put("anomalies_detected", anomaliesDetected.get());
        metrics.put("active_pipelines", activePipelines.size());
        metrics.put("active_streams", streamEngine.getActiveStreamCount());
        metrics.put("recent_results", recentResults.size());
        metrics.put("average_quality_score", calculateAverageQualityScore());
        metrics.put("processing_throughput_per_sec", calculateProcessingThroughput());
        metrics.put("error_rate", calculateErrorRate());
        metrics.put("last_updated", Instant.now());
        return metrics;
    }

    /**
     * Check agent health
     */
    public boolean isHealthy() {
        try {
            // Check component health
            boolean pipelineOrchestratorHealthy = pipelineOrchestrator.isHealthy();
            boolean streamEngineHealthy = streamEngine.isHealthy();
            boolean qualityAnalyzerHealthy = qualityAnalyzer.isHealthy();
            boolean aiEngineHealthy = aiEngine.isHealthy();
            
            boolean healthy = pipelineOrchestratorHealthy && streamEngineHealthy && 
                             qualityAnalyzerHealthy && aiEngineHealthy;
            
            isHealthy.set(healthy);
            return healthy;
            
        } catch (Exception e) {
            logger.warn("Health check failed: {}", e.getMessage());
            isHealthy.set(false);
            return false;
        }
    }

    /**
     * Get active pipeline status
     */
    public Map<String, Object> getPipelineStatus(String pipelineId) {
        DataPipeline pipeline = activePipelines.get(pipelineId);
        ProcessingResult result = recentResults.get(pipelineId);
        
        if (pipeline != null) {
            return Map.of(
                "pipeline_id", pipelineId,
                "status", "RUNNING",
                "pipeline", pipeline,
                "started_at", pipeline.createdAt()
            );
        } else if (result != null) {
            return Map.of(
                "pipeline_id", pipelineId,
                "status", result.status(),
                "result", result
            );
        } else {
            return Map.of(
                "pipeline_id", pipelineId,
                "status", "NOT_FOUND"
            );
        }
    }

    /**
     * List all pipelines
     */
    public List<Map<String, Object>> listPipelines() {
        List<Map<String, Object>> pipelines = new ArrayList<>();
        
        // Add active pipelines
        activePipelines.forEach((id, pipeline) -> {
            pipelines.add(Map.of(
                "pipeline_id", id,
                "name", pipeline.name(),
                "status", "RUNNING",
                "created_at", pipeline.createdAt()
            ));
        });
        
        // Add recent results
        recentResults.forEach((id, result) -> {
            if (!activePipelines.containsKey(id)) {
                pipelines.add(Map.of(
                    "pipeline_id", id,
                    "status", result.status(),
                    "records_processed", result.recordsProcessed(),
                    "completed_at", result.completedAt()
                ));
            }
        });
        
        return pipelines;
    }

    // Private helper methods

    private void validatePipeline(DataPipeline pipeline) {
        if (pipeline.source() == null) {
            throw new IllegalArgumentException("Pipeline source is required");
        }
        if (pipeline.destination() == null) {
            throw new IllegalArgumentException("Pipeline destination is required");
        }
        if (pipeline.transformations() == null || pipeline.transformations().isEmpty()) {
            logger.warn("Pipeline {} has no transformations defined", pipeline.pipelineId());
        }
    }

    private void updateMetrics(ProcessingResult result, QualityMetrics qualityMetrics) {
        recordsProcessed.addAndGet(result.recordsProcessed());
        
        if (qualityMetrics != null) {
            anomaliesDetected.addAndGet(qualityMetrics.anomaliesDetected().size());
        }
    }

    private double calculateAverageQualityScore() {
        return recentResults.values().stream()
            .filter(result -> result.qualityMetrics() != null)
            .mapToDouble(result -> result.qualityMetrics().overallQualityScore())
            .average()
            .orElse(0.0);
    }

    private double calculateProcessingThroughput() {
        // Calculate records per second over the last hour
        long totalRecords = recordsProcessed.get();
        return totalRecords / 3600.0; // Simplified calculation
    }

    private double calculateErrorRate() {
        long totalPipelines = pipelinesExecuted.get();
        if (totalPipelines == 0) {
            return 0.0;
        }
        
        long failedPipelines = recentResults.values().stream()
            .mapToLong(result -> result.status() == ProcessingStatus.FAILED ? 1 : 0)
            .sum();
        
        return (double) failedPipelines / totalPipelines;
    }

    // Supporting classes and interfaces

    public interface DataPipelineOrchestrator {
        ProcessingResult executePipeline(DataPipeline pipeline);
        boolean isHealthy();
    }

    public interface StreamProcessingEngine {
        String startStream(StreamProcessingConfig config);
        void stopStream(String streamId);
        int getActiveStreamCount();
        boolean isHealthy();
    }

    public interface DataQualityAnalyzer {
        QualityMetrics analyzeDataset(DataQualityRequest request);
        QualityMetrics analyzePipelineOutput(DataPipeline pipeline, ProcessingResult result);
        boolean isHealthy();
    }

    public interface AiTransformationEngine {
        AiTransformationResult executeTransformation(AiTransformationRequest request);
        boolean isHealthy();
    }

    // Request/Response models

    public record StreamProcessingConfig(
        @JsonProperty("stream_id") String streamId,
        @JsonProperty("source_topic") String sourceTopic,
        @JsonProperty("destination_topic") String destinationTopic,
        @JsonProperty("transformations") List<DataTransformation> transformations,
        @JsonProperty("window_config") WindowConfig windowConfig,
        @JsonProperty("parallelism") int parallelism
    ) {}

    public record DataQualityRequest(
        @JsonProperty("dataset_id") String datasetId,
        @JsonProperty("schema") DataSchema schema,
        @JsonProperty("sample_data") List<Map<String, Object>> sampleData,
        @JsonProperty("quality_checks") List<QualityCheck> qualityChecks
    ) {}

    public record AiTransformationRequest(
        @JsonProperty("transformation_id") String transformationId,
        @JsonProperty("model_config") AiModelConfig modelConfig,
        @JsonProperty("input_data") List<Map<String, Object>> inputData,
        @JsonProperty("transformation_rules") Map<String, Object> transformationRules
    ) {}

    public record AiTransformationResult(
        @JsonProperty("transformation_id") String transformationId,
        @JsonProperty("records_transformed") long recordsTransformed,
        @JsonProperty("confidence_score") double confidenceScore,
        @JsonProperty("output_data") List<Map<String, Object>> outputData,
        @JsonProperty("model_metrics") Map<String, Object> modelMetrics,
        @JsonProperty("processing_time_ms") long processingTimeMs,
        @JsonProperty("completed_at") Instant completedAt
    ) {}
}