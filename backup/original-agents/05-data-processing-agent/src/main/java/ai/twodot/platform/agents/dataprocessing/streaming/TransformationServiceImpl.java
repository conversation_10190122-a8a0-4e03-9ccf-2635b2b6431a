package ai.twodot.platform.agents.dataprocessing.streaming;

import ai.twodot.platform.agents.dataprocessing.data.DataModels;
import org.springframework.stereotype.Service;

@Service
public class TransformationServiceImpl implements StreamProcessingEngineImpl.DataTransformationService {
    @Override
    public String transform(String data, DataModels.DataTransformation transformation) {
        return data;
    }
}
