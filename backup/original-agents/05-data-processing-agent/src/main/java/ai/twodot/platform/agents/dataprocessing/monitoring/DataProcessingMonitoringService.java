package ai.twodot.platform.agents.dataprocessing.monitoring;

import ai.twodot.platform.agents.dataprocessing.data.DataModels.*;
import ai.twodot.platform.agents.dataprocessing.agent.DataProcessingAgent;

import io.micrometer.core.instrument.*;
import io.micrometer.core.instrument.Timer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
// Simplified health check implementation without Spring Boot Actuator dependency
import org.springframework.stereotype.Service;

import javax.management.MBeanServer;
import javax.management.ObjectName;
import java.lang.management.ManagementFactory;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Supplier;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.DoubleAdder;
import java.util.stream.Collectors;

/**
 * Comprehensive Monitoring and Observability Service for Data Processing Agent
 * 
 * Provides:
 * - Performance metrics collection and reporting
 * - Health monitoring and alerts
 * - Data processing pipeline observability
 * - Resource utilization tracking
 * - SLA monitoring and reporting
 * - Custom business metrics
 */
@Service
public class DataProcessingMonitoringService {

    private static final Logger logger = LoggerFactory.getLogger(DataProcessingMonitoringService.class);

    @Autowired
    private MeterRegistry meterRegistry;

    @Autowired
    private DataProcessingAgent dataProcessingAgent;

    // Core metrics
    private final Counter pipelinesExecutedCounter;
    private final Counter recordsProcessedCounter;
    private final Counter errorsCounter;
    private final Timer pipelineExecutionTimer;
    private final Timer dataQualityAnalysisTimer;
    private final Timer anomalyDetectionTimer;
    private final Gauge activePipelinesGauge;
    private final Gauge throughputGauge;
    private final Gauge errorRateGauge;
    private final Gauge dataQualityScoreGauge;

    // Business metrics
    private final DoubleAdder totalDataQualityScore = new DoubleAdder();
    private final AtomicLong qualityChecksPerformed = new AtomicLong(0);
    private final AtomicLong anomaliesDetected = new AtomicLong(0);
    private final AtomicLong pipelineFailures = new AtomicLong(0);
    private final AtomicLong currentThroughput = new AtomicLong(0);

    // Performance tracking
    private final Map<String, PipelineMetrics> pipelineMetrics = new ConcurrentHashMap<>();
    private final Map<String, ComponentHealth> componentHealth = new ConcurrentHashMap<>();
    private final Queue<PerformanceSnapshot> performanceHistory = new ConcurrentLinkedQueue<>();

    // Alerting
    private final Map<String, AlertRule> alertRules = new ConcurrentHashMap<>();
    private final List<Alert> activeAlerts = Collections.synchronizedList(new ArrayList<>());

    // Scheduling
    private final ScheduledExecutorService metricsScheduler = Executors.newScheduledThreadPool(3);

    public DataProcessingMonitoringService(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        
        // Initialize core metrics
        this.pipelinesExecutedCounter = Counter.builder("dpa.pipelines.executed")
            .description("Total number of pipelines executed")
            .register(meterRegistry);
            
        this.recordsProcessedCounter = Counter.builder("dpa.records.processed")
            .description("Total number of records processed")
            .register(meterRegistry);
            
        this.errorsCounter = Counter.builder("dpa.errors")
            .description("Total number of errors")
            .tag("type", "all")
            .register(meterRegistry);
            
        this.pipelineExecutionTimer = Timer.builder("dpa.pipeline.execution.time")
            .description("Pipeline execution time")
            .register(meterRegistry);
            
        this.dataQualityAnalysisTimer = Timer.builder("dpa.quality.analysis.time")
            .description("Data quality analysis time")
            .register(meterRegistry);
            
        this.anomalyDetectionTimer = Timer.builder("dpa.anomaly.detection.time")
            .description("Anomaly detection time")
            .register(meterRegistry);
            
        this.activePipelinesGauge = Gauge.builder("dpa.pipelines.active", this, DataProcessingMonitoringService::getActivePipelineCount)
            .description("Number of active pipelines")
            .register(meterRegistry);
            
        this.throughputGauge = Gauge.builder("dpa.throughput.current", this, self -> (double) self.currentThroughput.get())
            .description("Current processing throughput (records/sec)")
            .register(meterRegistry);
            
        this.errorRateGauge = Gauge.builder("dpa.error.rate", this, DataProcessingMonitoringService::calculateErrorRate)
            .description("Current error rate percentage")
            .register(meterRegistry);
            
        this.dataQualityScoreGauge = Gauge.builder("dpa.quality.score.average", this, DataProcessingMonitoringService::calculateAverageQualityScore)
            .description("Average data quality score")
            .register(meterRegistry);

        // Initialize monitoring
        initializeMonitoring();
    }

    private void initializeMonitoring() {
        logger.info("Initializing Data Processing Monitoring Service...");
        
        // Setup default alert rules
        setupDefaultAlertRules();
        
        // Start background monitoring tasks
        startMetricsCollection();
        startHealthChecks();
        startAlertingSystem();
        
        logger.info("✅ Data Processing Monitoring Service initialized successfully");
    }

    /**
     * Record pipeline execution
     */
    public void recordPipelineExecution(String pipelineId, Duration executionTime, 
                                       int recordsProcessed, int errors) {
        // Update counters
        pipelinesExecutedCounter.increment();
        recordsProcessedCounter.increment(recordsProcessed);
        if (errors > 0) {
            errorsCounter.increment(errors);
            pipelineFailures.incrementAndGet();
        }
        
        // Record timing
        pipelineExecutionTimer.record(executionTime);
        
        // Update pipeline-specific metrics
        PipelineMetrics metrics = pipelineMetrics.computeIfAbsent(pipelineId, 
            id -> new PipelineMetrics(id));
        metrics.recordExecution(executionTime, recordsProcessed, errors);
        
        // Update throughput
        updateThroughput(recordsProcessed, executionTime);
        
        logger.debug("Recorded pipeline execution: {} - {} records in {}ms", 
            pipelineId, recordsProcessed, executionTime.toMillis());
    }

    /**
     * Record data quality analysis
     */
    public void recordDataQualityAnalysis(String datasetId, double qualityScore, 
                                         Duration analysisTime, int anomaliesFound) {
        // Update quality metrics
        totalDataQualityScore.add(qualityScore);
        qualityChecksPerformed.incrementAndGet();
        anomaliesDetected.addAndGet(anomaliesFound);
        
        // Record timing
        dataQualityAnalysisTimer.record(analysisTime);
        
        // Create quality-specific metrics
        meterRegistry.counter("dpa.quality.checks", "dataset", datasetId).increment();
        meterRegistry.gauge("dpa.quality.score", Tags.of("dataset", datasetId), qualityScore);
        
        if (anomaliesFound > 0) {
            anomalyDetectionTimer.record(analysisTime);
            meterRegistry.counter("dpa.anomalies.detected", "dataset", datasetId)
                .increment(anomaliesFound);
        }
        
        logger.debug("Recorded quality analysis: {} - score {} with {} anomalies", 
            datasetId, qualityScore, anomaliesFound);
    }

    /**
     * Record stream processing metrics
     */
    public void recordStreamProcessing(String streamId, long recordsPerSecond, 
                                      double latencyMs, int errors) {
        // Create stream-specific metrics
        meterRegistry.gauge("dpa.stream.throughput", Tags.of("stream", streamId), recordsPerSecond);
        meterRegistry.gauge("dpa.stream.latency", Tags.of("stream", streamId), latencyMs);
        
        if (errors > 0) {
            meterRegistry.counter("dpa.stream.errors", "stream", streamId).increment(errors);
        }
        
        logger.debug("Recorded stream metrics: {} - {} rps, {}ms latency", 
            streamId, recordsPerSecond, latencyMs);
    }

    /**
     * Record resource utilization
     */
    public void recordResourceUtilization(double cpuUsage, double memoryUsage, 
                                         double diskUsage, double networkUsage) {
        meterRegistry.gauge("dpa.resource.cpu.usage", cpuUsage);
        meterRegistry.gauge("dpa.resource.memory.usage", memoryUsage);
        meterRegistry.gauge("dpa.resource.disk.usage", diskUsage);
        meterRegistry.gauge("dpa.resource.network.usage", networkUsage);
        
        // Check for resource alerts
        checkResourceAlerts(cpuUsage, memoryUsage, diskUsage);
    }

    /**
     * Health check implementation
     */
    public Map<String, Object> health() {
        Map<String, Object> healthData = new HashMap<>();
        boolean isHealthy = true;
        
        try {
            // Check overall agent health
            boolean agentHealthy = dataProcessingAgent.isHealthy();
            
            // Check component health
            Map<String, ComponentHealth> healthMap = new HashMap<>(componentHealth);
            
            // Check performance metrics
            double errorRate = calculateErrorRate();
            double avgQualityScore = calculateAverageQualityScore();
            
            // Determine overall health
            String reason = null;
            if (!agentHealthy) {
                isHealthy = false;
                reason = "Agent unhealthy";
            } else if (errorRate > 10.0) {
                isHealthy = false;
                reason = "High error rate: " + errorRate + "%";
            } else if (avgQualityScore < 70.0) {
                isHealthy = false;
                reason = "Low data quality score: " + avgQualityScore;
            } else if (hasActiveAlerts()) {
                isHealthy = false;
                reason = "Active critical alerts";
            }
            
            // Add health details
            healthData.put("status", isHealthy ? "UP" : "DOWN");
            healthData.put("agent_healthy", agentHealthy);
            healthData.put("error_rate", errorRate);
            healthData.put("avg_quality_score", avgQualityScore);
            healthData.put("active_pipelines", getActivePipelineCount());
            healthData.put("active_alerts", activeAlerts.size());
            healthData.put("component_health", healthMap);
            if (reason != null) {
                healthData.put("reason", reason);
            }
                
        } catch (Exception e) {
            logger.error("Health check failed: {}", e.getMessage(), e);
            healthData.put("status", "DOWN");
            healthData.put("error", e.getMessage());
            isHealthy = false;
        }
        
        return healthData;
    }

    /**
     * Get comprehensive monitoring dashboard
     */
    public MonitoringDashboard getDashboard() {
        return new MonitoringDashboard(
            generatePerformanceMetrics(),
            generateQualityMetrics(),
            generateResourceMetrics(),
            generateAlertsSummary(),
            new ArrayList<>(performanceHistory),
            Instant.now()
        );
    }

    /**
     * Get SLA compliance report
     */
    public SLAReport getSLAReport() {
        // Calculate SLA metrics
        double uptimePercentage = calculateUptimePercentage();
        double errorRateCompliance = calculateErrorRateCompliance();
        double performanceCompliance = calculatePerformanceCompliance();
        double qualityCompliance = calculateQualityCompliance();
        
        double overallCompliance = (uptimePercentage + errorRateCompliance + 
                                   performanceCompliance + qualityCompliance) / 4.0;
        
        return new SLAReport(
            overallCompliance,
            Map.of(
                "uptime", uptimePercentage,
                "error_rate", errorRateCompliance,
                "performance", performanceCompliance,
                "quality", qualityCompliance
            ),
            generateSLARecommendations(overallCompliance),
            Instant.now()
        );
    }

    // Private helper methods

    private void setupDefaultAlertRules() {
        alertRules.put("high_error_rate", new AlertRule(
            "high_error_rate", "Error rate > 5%", "CRITICAL",
            () -> calculateErrorRate() > 5.0
        ));
        
        alertRules.put("low_quality_score", new AlertRule(
            "low_quality_score", "Average quality score < 80", "WARNING",
            () -> calculateAverageQualityScore() < 80.0
        ));
        
        alertRules.put("pipeline_failures", new AlertRule(
            "pipeline_failures", "Multiple pipeline failures", "CRITICAL",
            () -> pipelineFailures.get() > 10
        ));
    }

    private void startMetricsCollection() {
        metricsScheduler.scheduleAtFixedRate(() -> {
            try {
                collectPerformanceMetrics();
                collectResourceMetrics();
                cleanupOldMetrics();
            } catch (Exception e) {
                logger.error("Metrics collection failed: {}", e.getMessage(), e);
            }
        }, 0, 30, TimeUnit.SECONDS);
    }

    private void startHealthChecks() {
        metricsScheduler.scheduleAtFixedRate(() -> {
            try {
                checkComponentHealth();
                updateHealthMetrics();
            } catch (Exception e) {
                logger.error("Health check failed: {}", e.getMessage(), e);
            }
        }, 0, 60, TimeUnit.SECONDS);
    }

    private void startAlertingSystem() {
        metricsScheduler.scheduleAtFixedRate(() -> {
            try {
                evaluateAlerts();
                cleanupResolvedAlerts();
            } catch (Exception e) {
                logger.error("Alert evaluation failed: {}", e.getMessage(), e);
            }
        }, 0, 10, TimeUnit.SECONDS);
    }

    private void collectPerformanceMetrics() {
        PerformanceSnapshot snapshot = new PerformanceSnapshot(
            Instant.now(),
            currentThroughput.get(),
            calculateErrorRate(),
            calculateAverageQualityScore(),
            getActivePipelineCount(),
            pipelinesExecutedCounter.count(),
            recordsProcessedCounter.count()
        );
        
        performanceHistory.offer(snapshot);
        
        // Keep only last 1000 snapshots
        while (performanceHistory.size() > 1000) {
            performanceHistory.poll();
        }
    }

    private void collectResourceMetrics() {
        // Collect JVM metrics
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        double memoryUsage = (double) usedMemory / maxMemory * 100.0;
        
        meterRegistry.gauge("dpa.jvm.memory.used", usedMemory);
        meterRegistry.gauge("dpa.jvm.memory.max", maxMemory);
        meterRegistry.gauge("dpa.jvm.memory.usage.percentage", memoryUsage);
        
        // Record resource utilization
        recordResourceUtilization(getCurrentCpuUsage(), memoryUsage, 0.0, 0.0);
    }

    private void checkComponentHealth() {
        // Check data processing agent health
        componentHealth.put("agent", new ComponentHealth(
            "agent", dataProcessingAgent.isHealthy(), Instant.now()
        ));
        
        // Check Python AI service health
        componentHealth.put("ai_service", new ComponentHealth(
            "ai_service", checkAIServiceHealth(), Instant.now()
        ));
        
        // Check database connections
        componentHealth.put("database", new ComponentHealth(
            "database", checkDatabaseHealth(), Instant.now()
        ));
    }

    private void updateThroughput(int recordsProcessed, Duration executionTime) {
        if (executionTime.toMillis() > 0) {
            long throughput = (recordsProcessed * 1000L) / executionTime.toMillis();
            currentThroughput.set(throughput);
        }
    }

    private void evaluateAlerts() {
        for (AlertRule rule : alertRules.values()) {
            try {
                if (rule.condition().get()) {
                    // Create alert if not already active
                    boolean alertExists = activeAlerts.stream()
                        .anyMatch(alert -> alert.ruleId().equals(rule.id()));
                    
                    if (!alertExists) {
                        Alert alert = new Alert(
                            UUID.randomUUID().toString(),
                            rule.id(),
                            rule.description(),
                            rule.severity(),
                            "ACTIVE",
                            Instant.now()
                        );
                        activeAlerts.add(alert);
                        logger.warn("🚨 Alert triggered: {} - {}", rule.id(), rule.description());
                    }
                }
            } catch (Exception e) {
                logger.error("Failed to evaluate alert rule {}: {}", rule.id(), e.getMessage());
            }
        }
    }

    private void cleanupResolvedAlerts() {
        activeAlerts.removeIf(alert -> {
            AlertRule rule = alertRules.get(alert.ruleId());
            if (rule != null) {
                try {
                    if (!rule.condition().get()) {
                        logger.info("✅ Alert resolved: {} - {}", alert.ruleId(), alert.description());
                        return true;
                    }
                } catch (Exception e) {
                    logger.error("Failed to check alert resolution for {}: {}", 
                        alert.ruleId(), e.getMessage());
                }
            }
            return false;
        });
    }

    private void checkResourceAlerts(double cpuUsage, double memoryUsage, double diskUsage) {
        if (cpuUsage > 80.0) {
            createResourceAlert("high_cpu", "High CPU usage: " + cpuUsage + "%", "WARNING");
        }
        if (memoryUsage > 85.0) {
            createResourceAlert("high_memory", "High memory usage: " + memoryUsage + "%", "CRITICAL");
        }
        if (diskUsage > 90.0) {
            createResourceAlert("high_disk", "High disk usage: " + diskUsage + "%", "CRITICAL");
        }
    }

    private void createResourceAlert(String alertId, String description, String severity) {
        boolean alertExists = activeAlerts.stream()
            .anyMatch(alert -> alert.ruleId().equals(alertId));
        
        if (!alertExists) {
            Alert alert = new Alert(
                UUID.randomUUID().toString(),
                alertId,
                description,
                severity,
                "ACTIVE",
                Instant.now()
            );
            activeAlerts.add(alert);
            logger.warn("🚨 Resource alert: {}", description);
        }
    }

    // Calculation methods

    private double calculateErrorRate() {
        long totalPipelines = (long) pipelinesExecutedCounter.count();
        if (totalPipelines == 0) return 0.0;
        return (pipelineFailures.get() * 100.0) / totalPipelines;
    }

    private double calculateAverageQualityScore() {
        long checks = qualityChecksPerformed.get();
        if (checks == 0) return 100.0;
        return totalDataQualityScore.sum() / checks;
    }

    private int getActivePipelineCount() {
        try {
            Map<String, Object> metrics = dataProcessingAgent.getMetrics();
            return (Integer) metrics.getOrDefault("active_pipelines", 0);
        } catch (Exception e) {
            return 0;
        }
    }

    private double getCurrentCpuUsage() {
        try {
            MBeanServer mbs = ManagementFactory.getPlatformMBeanServer();
            ObjectName name = ObjectName.getInstance("java.lang:type=OperatingSystem");
            Object cpuUsage = mbs.getAttribute(name, "ProcessCpuLoad");
            return cpuUsage instanceof Double ? (Double) cpuUsage * 100.0 : 0.0;
        } catch (Exception e) {
            return 0.0;
        }
    }

    private boolean checkAIServiceHealth() {
        // Implementation would check Python AI service health
        return true; // Simplified
    }

    private boolean checkDatabaseHealth() {
        // Implementation would check database connectivity
        return true; // Simplified
    }

    private boolean hasActiveAlerts() {
        return activeAlerts.stream().anyMatch(alert -> 
            "CRITICAL".equals(alert.severity()) && "ACTIVE".equals(alert.status()));
    }

    private void cleanupOldMetrics() {
        // Remove old pipeline metrics
        Instant cutoff = Instant.now().minus(Duration.ofHours(24));
        pipelineMetrics.entrySet().removeIf(entry -> 
            entry.getValue().getLastExecution().isBefore(cutoff));
    }

    private void updateHealthMetrics() {
        Map<String, Object> healthData = health();
        meterRegistry.gauge("dpa.health.overall", "UP".equals(healthData.get("status")) ? 1.0 : 0.0);
        meterRegistry.gauge("dpa.alerts.active", (double) activeAlerts.size());
    }

    // Generate report methods

    private Map<String, Object> generatePerformanceMetrics() {
        return Map.of(
            "pipelines_executed", pipelinesExecutedCounter.count(),
            "records_processed", recordsProcessedCounter.count(),
            "error_rate", calculateErrorRate(),
            "current_throughput", currentThroughput.get(),
            "active_pipelines", getActivePipelineCount()
        );
    }

    private Map<String, Object> generateQualityMetrics() {
        return Map.of(
            "average_quality_score", calculateAverageQualityScore(),
            "quality_checks_performed", qualityChecksPerformed.get(),
            "anomalies_detected", anomaliesDetected.get(),
            "quality_compliance", calculateQualityCompliance()
        );
    }

    private Map<String, Object> generateResourceMetrics() {
        Runtime runtime = Runtime.getRuntime();
        return Map.of(
            "memory_used", runtime.totalMemory() - runtime.freeMemory(),
            "memory_max", runtime.maxMemory(),
            "memory_usage_percentage", ((runtime.totalMemory() - runtime.freeMemory()) * 100.0) / runtime.maxMemory(),
            "cpu_usage", getCurrentCpuUsage()
        );
    }

    private Map<String, Object> generateAlertsSummary() {
        Map<String, Long> alertsBySeverity = activeAlerts.stream()
            .collect(Collectors.groupingBy(Alert::severity, Collectors.counting()));
        
        return Map.of(
            "total_active", activeAlerts.size(),
            "by_severity", alertsBySeverity,
            "recent_alerts", activeAlerts.stream()
                .filter(alert -> alert.timestamp().isAfter(Instant.now().minus(Duration.ofHours(1))))
                .collect(Collectors.toList())
        );
    }

    // SLA calculation methods

    private double calculateUptimePercentage() {
        // Calculate based on health check history
        return 99.5; // Simplified
    }

    private double calculateErrorRateCompliance() {
        double errorRate = calculateErrorRate();
        return errorRate <= 1.0 ? 100.0 : Math.max(0, 100.0 - (errorRate * 10));
    }

    private double calculatePerformanceCompliance() {
        // Based on throughput and latency targets
        return 95.0; // Simplified
    }

    private double calculateQualityCompliance() {
        double avgScore = calculateAverageQualityScore();
        return avgScore >= 90.0 ? 100.0 : avgScore;
    }

    private List<String> generateSLARecommendations(double overallCompliance) {
        List<String> recommendations = new ArrayList<>();
        
        if (overallCompliance < 95.0) {
            recommendations.add("Review and optimize pipeline performance");
        }
        if (calculateErrorRate() > 1.0) {
            recommendations.add("Investigate and reduce error rates");
        }
        if (calculateAverageQualityScore() < 90.0) {
            recommendations.add("Improve data quality validation processes");
        }
        
        return recommendations;
    }

    /**
     * Shutdown monitoring service
     */
    public void shutdown() {
        logger.info("Shutting down Data Processing Monitoring Service...");
        
        metricsScheduler.shutdown();
        try {
            if (!metricsScheduler.awaitTermination(10, TimeUnit.SECONDS)) {
                metricsScheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            metricsScheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        logger.info("Data Processing Monitoring Service shutdown completed");
    }

    // Supporting record classes

    public record PipelineMetrics(
        String pipelineId,
        AtomicLong executionCount,
        DoubleAdder totalExecutionTime,
        AtomicLong totalRecordsProcessed,
        AtomicLong totalErrors,
        Instant lastExecution
    ) {
        public PipelineMetrics(String pipelineId) {
            this(pipelineId, new AtomicLong(0), new DoubleAdder(), 
                 new AtomicLong(0), new AtomicLong(0), Instant.now());
        }
        
        public void recordExecution(Duration executionTime, int recordsProcessed, int errors) {
            executionCount.incrementAndGet();
            totalExecutionTime.add(executionTime.toMillis());
            totalRecordsProcessed.addAndGet(recordsProcessed);
            totalErrors.addAndGet(errors);
        }
        
        public Instant getLastExecution() {
            return lastExecution;
        }
    }

    public record ComponentHealth(
        String component,
        boolean healthy,
        Instant lastCheck
    ) {}

    public record AlertRule(
        String id,
        String description,
        String severity,
        Supplier<Boolean> condition
    ) {}

    public record Alert(
        String id,
        String ruleId,
        String description,
        String severity,
        String status,
        Instant timestamp
    ) {}

    public record PerformanceSnapshot(
        Instant timestamp,
        long throughput,
        double errorRate,
        double qualityScore,
        int activePipelines,
        double totalPipelinesExecuted,
        double totalRecordsProcessed
    ) {}

    public record MonitoringDashboard(
        Map<String, Object> performanceMetrics,
        Map<String, Object> qualityMetrics,
        Map<String, Object> resourceMetrics,
        Map<String, Object> alertsSummary,
        List<PerformanceSnapshot> performanceHistory,
        Instant generatedAt
    ) {}

    public record SLAReport(
        double overallCompliance,
        Map<String, Double> complianceByCategory,
        List<String> recommendations,
        Instant generatedAt
    ) {}
}