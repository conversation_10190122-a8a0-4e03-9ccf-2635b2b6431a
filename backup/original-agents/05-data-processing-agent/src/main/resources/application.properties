# Data Processing Agent Configuration
server.port=8084
spring.application.name=data-processing-agent

# PostgreSQL Database Configuration
spring.datasource.url=***************************************************
spring.datasource.driverClassName=org.postgresql.Driver
spring.datasource.username=${DB_USERNAME:koneti}
spring.datasource.password=${DB_PASSWORD:}
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=true

# Redis Configuration
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.timeout=2000ms

# Management and Actuator
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=always
management.metrics.export.prometheus.enabled=true

# Data Processing Specific
data.processing.stream.enabled=true
data.processing.quality.analysis.enabled=true
data.processing.anomaly.detection.enabled=true
data.processing.ai.enabled=true
data.processing.ai.service.url=http://localhost:9084

# Kafka Configuration (optional)
spring.kafka.bootstrap-servers=localhost:9092
spring.kafka.consumer.group-id=data-processing-group
spring.kafka.consumer.enable-auto-commit=false

# Logging
logging.level.ai.twodot.platform.agents.dataprocessing=INFO
logging.level.org.springframework.web=INFO
logging.level.root=INFO