package ai.twodot.platform.agents.dataprocessing.agent;

import ai.twodot.platform.agents.dataprocessing.data.DataModels.*;
import ai.twodot.platform.agents.dataprocessing.data.DataModels.WindowConfig;
import ai.twodot.platform.agents.dataprocessing.agent.DataProcessingAgent.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DataProcessingAgentTest {

    @Mock
    private DataPipelineOrchestrator pipelineOrchestrator;
    
    @Mock
    private StreamProcessingEngine streamEngine;
    
    @Mock
    private DataQualityAnalyzer qualityAnalyzer;
    
    @Mock
    private AiTransformationEngine aiEngine;
    
    private DataProcessingAgent agent;
    
    @BeforeEach
    void setUp() {
        agent = new DataProcessingAgent();
        // Use reflection to inject mocks
        setField(agent, "pipelineOrchestrator", pipelineOrchestrator);
        setField(agent, "streamEngine", streamEngine);
        setField(agent, "qualityAnalyzer", qualityAnalyzer);
        setField(agent, "aiEngine", aiEngine);
    }
    
    @Test
    void testExecutePipeline_Success() throws ExecutionException, InterruptedException {
        // Given
        DataPipeline pipeline = createTestDataPipeline();
        ProcessingResult mockResult = createTestProcessingResult();
        QualityMetrics mockQualityMetrics = createTestQualityMetrics();
        
        when(pipelineOrchestrator.executePipeline(pipeline)).thenReturn(mockResult);
        when(qualityAnalyzer.analyzePipelineOutput(pipeline, mockResult)).thenReturn(mockQualityMetrics);
        
        // When
        CompletableFuture<ProcessingResult> future = agent.executePipeline(pipeline);
        ProcessingResult result = future.get();
        
        // Then
        assertNotNull(result);
        assertEquals(ProcessingStatus.COMPLETED, result.status());
        assertEquals(1000, result.recordsProcessed());
        assertEquals(950, result.recordsSucceeded());
        assertEquals(50, result.recordsFailed());
        assertNotNull(result.qualityMetrics());
        
        verify(pipelineOrchestrator).executePipeline(pipeline);
        verify(qualityAnalyzer).analyzePipelineOutput(pipeline, mockResult);
    }
    
    @Test
    void testExecutePipeline_Failure() throws ExecutionException, InterruptedException {
        // Given
        DataPipeline pipeline = createTestDataPipeline();
        RuntimeException exception = new RuntimeException("Pipeline execution failed");
        
        when(pipelineOrchestrator.executePipeline(pipeline)).thenThrow(exception);
        
        // When
        CompletableFuture<ProcessingResult> future = agent.executePipeline(pipeline);
        ProcessingResult result = future.get();
        
        // Then
        assertNotNull(result);
        assertEquals(ProcessingStatus.FAILED, result.status());
        assertEquals(0, result.recordsProcessed());
        assertNotNull(result.errorSummary());
        assertEquals(1, result.errorSummary().totalErrors());
        
        verify(pipelineOrchestrator).executePipeline(pipeline);
        verify(qualityAnalyzer, never()).analyzePipelineOutput(any(), any());
    }
    
    @Test
    void testExecutePipeline_ValidationFailure() throws ExecutionException, InterruptedException {
        // Given
        DataPipeline invalidPipeline = new DataPipeline(
            "test-pipeline",
            "Test Pipeline",
            "Invalid pipeline with null source",
            null, // Invalid - null source
            createTestDataDestination(),
            List.of(createTestDataTransformation()),
            List.of(createTestQualityCheck()),
            createTestPipelineSchedule(),
            createTestPipelineConfiguration(),
            Map.of("test", "true"),
            Instant.now(),
            Instant.now()
        );
        
        // When
        CompletableFuture<ProcessingResult> future = agent.executePipeline(invalidPipeline);
        ProcessingResult result = future.get();
        
        // Then
        assertNotNull(result);
        assertEquals(ProcessingStatus.FAILED, result.status());
        assertNotNull(result.errorSummary());
        assertTrue(result.errorSummary().sampleErrors().get(0).contains("Pipeline source is required"));
        
        verify(pipelineOrchestrator, never()).executePipeline(any());
    }
    
    @Test
    void testStartStreamProcessing_Success() throws ExecutionException, InterruptedException {
        // Given
        StreamProcessingConfig config = new StreamProcessingConfig(
            "test-stream",
            "input-topic",
            "output-topic",
            List.of(createTestDataTransformation()),
            new WindowConfig("tumbling", 5000, 5000),
            4
        );
        
        when(streamEngine.startStream(config)).thenReturn("stream-id-123");
        
        // When
        CompletableFuture<String> future = agent.startStreamProcessing(config);
        String streamId = future.get();
        
        // Then
        assertEquals("stream-id-123", streamId);
        verify(streamEngine).startStream(config);
    }
    
    @Test
    void testStartStreamProcessing_Failure() throws ExecutionException, InterruptedException {
        // Given
        StreamProcessingConfig config = new StreamProcessingConfig(
            "test-stream",
            "input-topic",
            "output-topic",
            List.of(createTestDataTransformation()),
            new WindowConfig("tumbling", 5000, 5000),
            4
        );
        
        RuntimeException exception = new RuntimeException("Stream start failed");
        when(streamEngine.startStream(config)).thenThrow(exception);
        
        // When & Then
        CompletableFuture<String> future = agent.startStreamProcessing(config);
        
        assertThrows(ExecutionException.class, future::get);
        verify(streamEngine).startStream(config);
    }
    
    @Test
    void testStopStreamProcessing_Success() throws ExecutionException, InterruptedException {
        // Given
        String streamId = "stream-id-123";
        
        // When
        CompletableFuture<Void> future = agent.stopStreamProcessing(streamId);
        future.get();
        
        // Then
        verify(streamEngine).stopStream(streamId);
    }
    
    @Test
    void testAnalyzeDataQuality_Success() throws ExecutionException, InterruptedException {
        // Given
        DataQualityRequest request = new DataQualityRequest(
            "test-dataset",
            createTestDataSchema(),
            List.of(Map.of("field1", "value1", "field2", 123)),
            List.of(createTestQualityCheck())
        );
        
        QualityMetrics mockMetrics = createTestQualityMetrics();
        when(qualityAnalyzer.analyzeDataset(request)).thenReturn(mockMetrics);
        
        // When
        CompletableFuture<QualityMetrics> future = agent.analyzeDataQuality(request);
        QualityMetrics result = future.get();
        
        // Then
        assertNotNull(result);
        assertEquals(85.5, result.overallQualityScore());
        assertEquals(3, result.qualityIssues().size());
        
        verify(qualityAnalyzer).analyzeDataset(request);
    }
    
    @Test
    void testExecuteAiTransformation_Success() throws ExecutionException, InterruptedException {
        // Given
        AiTransformationRequest request = new AiTransformationRequest(
            "test-transformation",
            createTestAiModelConfig(),
            List.of(Map.of("input", "data")),
            Map.of("rule1", "transform value")
        );
        
        AiTransformationResult mockResult = new AiTransformationResult(
            "test-transformation",
            500,
            0.95,
            List.of(Map.of("output", "transformed data")),
            Map.of("accuracy", 0.92),
            2500,
            Instant.now()
        );
        
        when(aiEngine.executeTransformation(request)).thenReturn(mockResult);
        
        // When
        CompletableFuture<AiTransformationResult> future = agent.executeAiTransformation(request);
        AiTransformationResult result = future.get();
        
        // Then
        assertNotNull(result);
        assertEquals(500, result.recordsTransformed());
        assertEquals(0.95, result.confidenceScore());
        
        verify(aiEngine).executeTransformation(request);
    }
    
    @Test
    void testIsHealthy_AllComponentsHealthy() {
        // Given
        when(pipelineOrchestrator.isHealthy()).thenReturn(true);
        when(streamEngine.isHealthy()).thenReturn(true);
        when(qualityAnalyzer.isHealthy()).thenReturn(true);
        when(aiEngine.isHealthy()).thenReturn(true);
        
        // When
        boolean healthy = agent.isHealthy();
        
        // Then
        assertTrue(healthy);
    }
    
    @Test
    void testIsHealthy_OneComponentUnhealthy() {
        // Given
        when(pipelineOrchestrator.isHealthy()).thenReturn(true);
        when(streamEngine.isHealthy()).thenReturn(false);
        when(qualityAnalyzer.isHealthy()).thenReturn(true);
        when(aiEngine.isHealthy()).thenReturn(true);
        
        // When
        boolean healthy = agent.isHealthy();
        
        // Then
        assertFalse(healthy);
    }
    
    @Test
    void testGetMetrics() {
        // Given
        when(streamEngine.getActiveStreamCount()).thenReturn(3);
        
        // When
        Map<String, Object> metrics = agent.getMetrics();
        
        // Then
        assertNotNull(metrics);
        assertEquals("HEALTHY", metrics.get("agent_status"));
        assertEquals(0L, metrics.get("pipelines_executed"));
        assertEquals(0L, metrics.get("records_processed"));
        assertEquals(3, metrics.get("active_streams"));
    }
    
    @Test
    void testGetPipelineStatus_ActivePipeline() {
        // Given
        ProcessingResult mockResult = createTestProcessingResult();
        when(pipelineOrchestrator.executePipeline(any())).thenReturn(mockResult);
        when(qualityAnalyzer.analyzePipelineOutput(any(), any())).thenReturn(createTestQualityMetrics());

        DataPipeline pipeline = createTestDataPipeline();
        agent.executePipeline(pipeline).join(); // Wait for the async operation to complete
        
        // When
        Map<String, Object> status = agent.getPipelineStatus(pipeline.pipelineId());
        
        // Then
        assertNotNull(status);
        assertEquals("RUNNING", status.get("status"));
        assertEquals(pipeline.pipelineId(), status.get("pipeline_id"));
    }
    
    @Test
    void testGetPipelineStatus_NotFound() {
        // When
        Map<String, Object> status = agent.getPipelineStatus("non-existent-pipeline");
        
        // Then
        assertNotNull(status);
        assertEquals("NOT_FOUND", status.get("status"));
        assertEquals("non-existent-pipeline", status.get("pipeline_id"));
    }
    
    @Test
    void testListPipelines_Empty() {
        // When
        List<Map<String, Object>> pipelines = agent.listPipelines();
        
        // Then
        assertNotNull(pipelines);
        assertTrue(pipelines.isEmpty());
    }
    
    // Helper methods
    
    private DataPipeline createTestDataPipeline() {
        return new DataPipeline(
            "test-pipeline-" + UUID.randomUUID(),
            "Test Pipeline",
            "A test pipeline for validation",
            createTestDataSource(),
            createTestDataDestination(),
            List.of(createTestDataTransformation()),
            List.of(createTestQualityCheck()),
            createTestPipelineSchedule(),
            createTestPipelineConfiguration(),
            Map.of("owner", "test-team"),
            Instant.now(),
            Instant.now()
        );
    }
    
    private DataSource createTestDataSource() {
        return new DataSource(
            "test-source",
            SourceType.DATABASE,
            null,
            createTestDataSchema(),
            null,
            null,
            null
        );
    }
    
    private DataDestination createTestDataDestination() {
        return new DataDestination(
            "test-destination",
            DestinationType.FILE,
            null,
            createTestDataSchema(),
            null,
            null,
            null
        );
    }

    private PipelineSchedule createTestPipelineSchedule() {
        return new PipelineSchedule("0 0 * * * ?", "UTC", true);
    }

    private PipelineConfiguration createTestPipelineConfiguration() {
        return new PipelineConfiguration(4, 1000, 3600, 3, 10000, true, true, true);
    }
    
    private DataTransformation createTestDataTransformation() {
        return new DataTransformation(
            "test-transformation",
            "filter",
            TransformationType.FILTER,
            Map.of("condition", "age > 18"),
            null,
            null,
            List.of("age"),
            List.of("age"),
            1
        );
    }
    
    private QualityCheck createTestQualityCheck() {
        return new QualityCheck(
            "test-check",
            "completeness",
            QualityCheckType.COMPLETENESS,
            "age",
            new QualityRule("value is not null", null, null),
            0.95,
            Severity.CRITICAL,
            QualityAction.REJECT
        );
    }
    
    private DataSchema createTestDataSchema() {
        return new DataSchema(
            "test-schema",
            "Test Schema",
            "1.0",
            List.of(
                new SchemaField("id", FieldType.STRING, true, null, null, null, null, null),
                new SchemaField("name", FieldType.STRING, false, null, null, null, null, null),
                new SchemaField("age", FieldType.INTEGER, false, null, null, null, null, null)
            ),
            List.of("id"),
            List.of(),
            List.of()
        );
    }
    
    private ProcessingResult createTestProcessingResult() {
        return new ProcessingResult(
            "result-123",
            "test-pipeline",
            ProcessingStatus.COMPLETED,
            1000,
            950,
            50,
            5000,
            null,
            new ErrorSummary(50, Map.of("ValidationError", 30L, "TransformationError", 20L), 
                List.of("Some validation failed", "Some transformation failed")),
            new DataLineage(
                "lineage-123",
                List.of("input.csv"),
                List.of("filter -> transform -> validate"),
                List.of("output.csv"),
                null,
                Instant.now()
            ),
            Instant.now().minusSeconds(10),
            Instant.now()
        );
    }
    
    private QualityMetrics createTestQualityMetrics() {
        return new QualityMetrics(
            90.0,
            85.0,
            80.0,
            95.0,
            98.0,
            100.0,
            85.5,
            List.of(
                new QualityIssue("issue-1", QualityIssueType.NULL_VALUE, "name", "Missing values in name field", Severity.MEDIUM, 10L, List.of()),
                new QualityIssue("issue-2", QualityIssueType.INVALID_FORMAT, "email", "Invalid email format", Severity.LOW, 5L, List.of()),
                new QualityIssue("issue-3", QualityIssueType.MISSING_REFERENCE, "created_date", "Inconsistent date format", Severity.HIGH, 2L, List.of())
            ),
            List.of(
                new AnomalyDetection("anomaly-1", AnomalyType.STATISTICAL_OUTLIER, "age", 0.95, 0.9, "Age value 150 is suspicious", 1L, Instant.now()),
                new AnomalyDetection("anomaly-2", AnomalyType.PATTERN_DEVIATION, "email", 0.80, 0.7, "Unusual email pattern", 1L, Instant.now())
            )
        );
    }
    
    private AiModelConfig createTestAiModelConfig() {
        return new AiModelConfig(
            AiModelType.CLASSIFICATION,
            "random-forest",
            null,
            Map.of("n_estimators", 100, "max_depth", 10),
            0.9,
            FallbackStrategy.SKIP_RECORD
        );
    }
    
    private void setField(Object target, String fieldName, Object value) {
        try {
            var field = target.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(target, value);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set field: " + fieldName, e);
        }
    }
}