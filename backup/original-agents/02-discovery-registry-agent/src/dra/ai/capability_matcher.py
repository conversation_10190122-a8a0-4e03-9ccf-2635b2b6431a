"""
AI-powered capability matching for service discovery.

This module provides intelligent service matching based on capabilities,
performance requirements, and contextual analysis using embedding models
and large language model reasoning.
"""

import asyncio
import json
from typing import Any, Dict, List, Optional
from datetime import datetime, timedelta

import numpy as np
import structlog
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity

from dra.core.types import (
    ServiceInfo,
    ServiceRequirements,
    ServiceMatch,
    DetailedMatchAnalysis,
)
from dra.utils.ai_client import AIClient


logger = structlog.get_logger(__name__)


class CapabilityMatchingAI:
    """
    AI-powered capability matching system that uses semantic understanding
    and intelligent analysis to match services to requirements.
    """

    def __init__(self) -> None:
        self.ai_client: Optional[AIClient] = None
        self.embedding_model: Optional[SentenceTransformer] = None
        self.capability_embeddings: Dict[str, np.ndarray] = {}
        self.match_cache: Dict[str, List[ServiceMatch]] = {}
        self.cache_ttl = timedelta(minutes=15)

    async def initialize(self) -> None:
        """Initialize the capability matching system."""
        logger.info("Initializing Capability Matching AI")

        try:
            # Initialize AI client for detailed analysis
            self.ai_client = AIClient()
            await self.ai_client.initialize()

            # Initialize embedding model for semantic similarity
            await asyncio.to_thread(self._load_embedding_model)

            logger.info("Capability Matching AI initialized successfully")

        except Exception as e:
            logger.error("Failed to initialize Capability Matching AI", error=str(e))
            raise

    def _load_embedding_model(self) -> None:
        """Load sentence transformer model in a separate thread."""
        try:
            # Use a model optimized for semantic similarity
            self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
            logger.info("Embedding model loaded successfully")
        except Exception as e:
            logger.error("Failed to load embedding model", error=str(e))
            raise

    async def is_healthy(self) -> bool:
        """Check if the capability matcher is healthy."""
        try:
            return (
                self.ai_client is not None
                and await self.ai_client.is_healthy()
                and self.embedding_model is not None
            )
        except Exception:
            return False

    async def match_services_to_requirements(
        self, requirements: ServiceRequirements, available_services: List[ServiceInfo]
    ) -> List[ServiceMatch]:
        """
        Match services to requirements using AI-powered analysis.
        
        Args:
            requirements: Service requirements to match against
            available_services: List of available services
            
        Returns:
            List of service matches ranked by relevance
        """
        try:
            # Check cache first
            cache_key = self._get_cache_key(requirements, available_services)
            cached_matches = self._get_cached_matches(cache_key)
            if cached_matches:
                return cached_matches

            if not available_services:
                return []

            # Phase 1: Semantic similarity matching
            semantic_matches = await self._perform_semantic_matching(
                requirements, available_services
            )

            # Phase 2: AI-powered detailed analysis
            detailed_matches = await self._perform_detailed_analysis(
                requirements, semantic_matches
            )

            # Phase 3: Performance and constraint filtering
            filtered_matches = await self._apply_performance_filters(
                requirements, detailed_matches
            )

            # Sort by confidence and relevance
            final_matches = sorted(
                filtered_matches, key=lambda x: x.confidence, reverse=True
            )

            # Cache results
            self._cache_matches(cache_key, final_matches)

            logger.info(
                "Capability matching completed",
                initial_services=len(available_services),
                semantic_matches=len(semantic_matches),
                detailed_matches=len(detailed_matches),
                final_matches=len(final_matches),
            )

            return final_matches

        except Exception as e:
            logger.error("Capability matching failed", error=str(e))
            # Return basic fallback matches
            return await self._fallback_matching(requirements, available_services)

    async def _perform_semantic_matching(
        self, requirements: ServiceRequirements, services: List[ServiceInfo]
    ) -> List[ServiceMatch]:
        """Perform semantic similarity matching using embeddings."""
        try:
            if not self.embedding_model:
                raise RuntimeError("Embedding model not loaded")

            # Create requirement description for embedding
            req_description = self._create_requirement_description(requirements)

            # Get requirement embedding
            req_embedding = await asyncio.to_thread(
                self.embedding_model.encode, [req_description]
            )

            matches = []
            for service in services:
                # Create service description
                service_description = self._create_service_description(service)

                # Get service embedding (with caching)
                service_embedding = await self._get_service_embedding(
                    service.id, service_description
                )

                # Calculate similarity
                similarity = cosine_similarity(req_embedding, service_embedding.reshape(1, -1))[0][0]

                # Basic match object
                match = ServiceMatch(
                    service=service,
                    similarity_score=float(similarity),
                    confidence=float(similarity * 0.8),  # Initial confidence based on similarity
                    reasoning=f"Semantic similarity: {similarity:.3f}",
                    match_details={"semantic_similarity": float(similarity)},
                )

                matches.append(match)

            # Filter out very low similarity matches
            filtered_matches = [m for m in matches if m.similarity_score > 0.3]

            return filtered_matches

        except Exception as e:
            logger.error("Semantic matching failed", error=str(e))
            # Return all services with low confidence
            return [
                ServiceMatch(
                    service=service,
                    similarity_score=0.5,
                    confidence=0.3,
                    reasoning="Semantic matching unavailable",
                    match_details={},
                )
                for service in services
            ]

    async def _perform_detailed_analysis(
        self, requirements: ServiceRequirements, matches: List[ServiceMatch]
    ) -> List[ServiceMatch]:
        """Perform detailed AI analysis of matches."""
        try:
            if not self.ai_client or len(matches) == 0:
                return matches

            # Analyze top matches in detail (limit to top 10 for performance)
            top_matches = sorted(matches, key=lambda x: x.similarity_score, reverse=True)[:10]

            detailed_matches = []
            for match in top_matches:
                try:
                    # Get detailed analysis from AI
                    detailed_analysis = await self._analyze_detailed_match(
                        requirements, match.service, match.similarity_score
                    )

                    # Update match with detailed analysis
                    match.confidence = detailed_analysis.confidence
                    match.reasoning = detailed_analysis.reasoning
                    match.match_details.update({
                        "functional_alignment": detailed_analysis.functional_alignment,
                        "performance_compatibility": detailed_analysis.performance_compatibility,
                        "security_compliance": detailed_analysis.security_compliance,
                        "integration_complexity": detailed_analysis.integration_complexity,
                        "overall_score": detailed_analysis.overall_score,
                        "recommendations": detailed_analysis.recommendations,
                    })

                    detailed_matches.append(match)

                except Exception as e:
                    logger.warning(
                        "Detailed analysis failed for service",
                        service_id=match.service.id,
                        error=str(e),
                    )
                    # Keep original match with reduced confidence
                    match.confidence *= 0.7
                    detailed_matches.append(match)

            # Add remaining matches without detailed analysis
            remaining_matches = matches[10:]
            for match in remaining_matches:
                match.confidence *= 0.6  # Reduce confidence for non-analyzed matches

            return detailed_matches + remaining_matches

        except Exception as e:
            logger.error("Detailed analysis failed", error=str(e))
            return matches

    async def _analyze_detailed_match(
        self,
        requirements: ServiceRequirements,
        service: ServiceInfo,
        base_similarity: float,
    ) -> DetailedMatchAnalysis:
        """Perform detailed AI analysis of a service match."""
        try:
            if not self.ai_client:
                raise RuntimeError("AI client not available")

            # Create analysis prompt
            prompt = self._create_detailed_analysis_prompt(
                requirements, service, base_similarity
            )

            # Get AI analysis
            response = await self.ai_client.generate_response(
                prompt=prompt,
                temperature=0.2,
                max_tokens=1500,
                system_prompt="You are an expert in service architecture and capability analysis.",
            )

            # Parse AI response
            analysis = self._parse_detailed_analysis_response(response)

            return analysis

        except Exception as e:
            logger.error("AI detailed analysis failed", error=str(e))
            # Return fallback analysis
            return DetailedMatchAnalysis(
                functional_alignment=base_similarity,
                performance_compatibility=0.7,
                security_compliance=0.8,
                integration_complexity=0.6,
                overall_score=base_similarity * 0.7,
                confidence=0.5,
                reasoning="AI analysis unavailable - using fallback scoring",
                recommendations=["Manual review recommended"],
            )

    async def _apply_performance_filters(
        self, requirements: ServiceRequirements, matches: List[ServiceMatch]
    ) -> List[ServiceMatch]:
        """Apply performance and constraint filters to matches."""
        try:
            filtered_matches = []

            for match in matches:
                service = match.service
                passes_filters = True
                filter_reasons = []

                # Check availability requirement
                if service.metrics:
                    if service.metrics.availability < requirements.min_availability:
                        passes_filters = False
                        filter_reasons.append(
                            f"Availability {service.metrics.availability:.2f} < {requirements.min_availability:.2f}"
                        )

                    # Check response time requirement
                    if service.metrics.response_time_p95 > requirements.max_response_time:
                        passes_filters = False
                        filter_reasons.append(
                            f"Response time {service.metrics.response_time_p95:.1f}ms > {requirements.max_response_time:.1f}ms"
                        )

                    # Check error rate requirement
                    if service.metrics.error_rate > requirements.max_error_rate:
                        passes_filters = False
                        filter_reasons.append(
                            f"Error rate {service.metrics.error_rate:.3f} > {requirements.max_error_rate:.3f}"
                        )

                # Check namespace requirement
                if service.namespace != requirements.namespace:
                    # Reduce confidence for cross-namespace matches but don't filter out
                    match.confidence *= 0.8
                    match.match_details["namespace_mismatch"] = True

                # Check protocol compatibility
                required_protocols = set(requirements.protocols)
                service_protocols = set(endpoint.protocol for endpoint in service.endpoints)
                
                if required_protocols and not required_protocols.intersection(service_protocols):
                    passes_filters = False
                    filter_reasons.append("No compatible protocols")

                if passes_filters:
                    filtered_matches.append(match)
                else:
                    # Log why service was filtered out
                    logger.debug(
                        "Service filtered out",
                        service_id=service.id,
                        reasons=filter_reasons,
                    )

            return filtered_matches

        except Exception as e:
            logger.error("Performance filtering failed", error=str(e))
            return matches

    async def _fallback_matching(
        self, requirements: ServiceRequirements, services: List[ServiceInfo]
    ) -> List[ServiceMatch]:
        """Provide basic fallback matching when AI systems fail."""
        try:
            matches = []

            for service in services:
                # Basic capability matching
                capability_score = self._calculate_basic_capability_score(
                    requirements, service
                )

                # Basic performance score
                performance_score = self._calculate_basic_performance_score(
                    requirements, service
                )

                # Combined score
                overall_score = (capability_score + performance_score) / 2

                match = ServiceMatch(
                    service=service,
                    similarity_score=capability_score,
                    confidence=overall_score * 0.6,  # Lower confidence for fallback
                    reasoning="Basic fallback matching (AI unavailable)",
                    match_details={
                        "capability_score": capability_score,
                        "performance_score": performance_score,
                        "fallback_mode": True,
                    },
                )

                matches.append(match)

            return sorted(matches, key=lambda x: x.confidence, reverse=True)

        except Exception as e:
            logger.error("Fallback matching failed", error=str(e))
            return []

    # Helper methods

    def _create_requirement_description(self, requirements: ServiceRequirements) -> str:
        """Create a textual description of requirements for embedding."""
        parts = []
        
        if requirements.description:
            parts.append(requirements.description)
        
        if requirements.capabilities:
            parts.append(f"Required capabilities: {', '.join(requirements.capabilities)}")
        
        if requirements.protocols:
            parts.append(f"Supported protocols: {', '.join(p.value for p in requirements.protocols)}")
        
        if requirements.tags:
            parts.append(f"Tags: {', '.join(requirements.tags)}")

        performance_reqs = []
        if requirements.min_availability < 1.0:
            performance_reqs.append(f"availability >= {requirements.min_availability:.2f}")
        if requirements.max_response_time < float('inf'):
            performance_reqs.append(f"response time <= {requirements.max_response_time}ms")
        if requirements.max_error_rate < 1.0:
            performance_reqs.append(f"error rate <= {requirements.max_error_rate:.3f}")
        
        if performance_reqs:
            parts.append(f"Performance requirements: {', '.join(performance_reqs)}")

        return " ".join(parts)

    def _create_service_description(self, service: ServiceInfo) -> str:
        """Create a textual description of a service for embedding."""
        parts = []
        
        parts.append(f"Service: {service.name}")
        
        if service.description:
            parts.append(service.description)
        
        if service.capabilities:
            caps = [f"{cap.name}: {cap.description}" for cap in service.capabilities]
            parts.append(f"Capabilities: {'; '.join(caps)}")
        
        if service.endpoints:
            protocols = [ep.protocol.value for ep in service.endpoints]
            parts.append(f"Protocols: {', '.join(set(protocols))}")
        
        if service.tags:
            parts.append(f"Tags: {', '.join(service.tags)}")

        return " ".join(parts)

    async def _get_service_embedding(self, service_id: str, description: str) -> np.ndarray:
        """Get or compute embedding for a service description."""
        if service_id in self.capability_embeddings:
            return self.capability_embeddings[service_id]

        if not self.embedding_model:
            raise RuntimeError("Embedding model not loaded")

        # Compute embedding
        embedding = await asyncio.to_thread(
            self.embedding_model.encode, [description]
        )
        
        # Cache embedding
        self.capability_embeddings[service_id] = embedding[0]
        
        # Limit cache size
        if len(self.capability_embeddings) > 1000:
            # Remove oldest entries (simple FIFO)
            keys_to_remove = list(self.capability_embeddings.keys())[:200]
            for key in keys_to_remove:
                del self.capability_embeddings[key]

        return embedding[0]

    def _create_detailed_analysis_prompt(
        self,
        requirements: ServiceRequirements,
        service: ServiceInfo,
        base_similarity: float,
    ) -> str:
        """Create prompt for detailed AI analysis."""
        return f"""
Analyze the compatibility between service requirements and available service:

REQUIREMENTS:
{json.dumps(requirements.to_dict(), indent=2)}

SERVICE:
{json.dumps(service.to_dict(), indent=2)}

BASE SIMILARITY: {base_similarity:.3f}

Provide detailed compatibility analysis considering:
1. Functional capability alignment - How well do the service capabilities match requirements?
2. Performance requirements vs service capabilities - Can it meet SLA requirements?
3. Security and compliance requirements - Does it meet security standards?
4. Integration complexity - How difficult would integration be?
5. Overall recommendation - Should this service be used?

Respond with a JSON object containing scores (0.0-1.0) and detailed reasoning:

{{
    "functional_alignment": 0.85,
    "performance_compatibility": 0.90,
    "security_compliance": 0.95,
    "integration_complexity": 0.70,
    "overall_score": 0.85,
    "confidence": 0.88,
    "reasoning": "Detailed explanation of the analysis...",
    "recommendations": ["Specific recommendations for usage or improvements"]
}}

Focus on providing accurate scores and actionable insights.
"""

    def _parse_detailed_analysis_response(self, response: str) -> DetailedMatchAnalysis:
        """Parse AI response for detailed analysis."""
        try:
            # Extract JSON from response
            start_idx = response.find("{")
            end_idx = response.rfind("}") + 1
            
            if start_idx != -1 and end_idx > start_idx:
                json_str = response[start_idx:end_idx]
                parsed = json.loads(json_str)
                
                return DetailedMatchAnalysis(
                    functional_alignment=float(parsed.get("functional_alignment", 0.5)),
                    performance_compatibility=float(parsed.get("performance_compatibility", 0.5)),
                    security_compliance=float(parsed.get("security_compliance", 0.8)),
                    integration_complexity=float(parsed.get("integration_complexity", 0.5)),
                    overall_score=float(parsed.get("overall_score", 0.5)),
                    confidence=float(parsed.get("confidence", 0.5)),
                    reasoning=parsed.get("reasoning", "AI analysis completed"),
                    recommendations=parsed.get("recommendations", []),
                )

        except Exception as e:
            logger.error("Failed to parse AI analysis response", error=str(e))

        # Return fallback analysis
        return DetailedMatchAnalysis(
            functional_alignment=0.5,
            performance_compatibility=0.5,
            security_compliance=0.8,
            integration_complexity=0.5,
            overall_score=0.5,
            confidence=0.4,
            reasoning="Failed to parse AI analysis",
            recommendations=["Manual review required"],
        )

    def _calculate_basic_capability_score(
        self, requirements: ServiceRequirements, service: ServiceInfo
    ) -> float:
        """Calculate basic capability matching score without AI."""
        score = 0.0
        total_weight = 0.0

        # Check capability matches
        if requirements.capabilities and service.capabilities:
            req_caps = set(requirements.capabilities)
            service_caps = set(cap.name for cap in service.capabilities)
            matches = len(req_caps.intersection(service_caps))
            total = len(req_caps)
            if total > 0:
                score += (matches / total) * 0.6
                total_weight += 0.6

        # Check tag matches
        if requirements.tags and service.tags:
            req_tags = set(requirements.tags)
            service_tags = set(service.tags)
            matches = len(req_tags.intersection(service_tags))
            total = len(req_tags)
            if total > 0:
                score += (matches / total) * 0.2
                total_weight += 0.2

        # Protocol compatibility
        if requirements.protocols and service.endpoints:
            req_protocols = set(requirements.protocols)
            service_protocols = set(ep.protocol for ep in service.endpoints)
            if req_protocols.intersection(service_protocols):
                score += 0.2
            total_weight += 0.2

        return score / total_weight if total_weight > 0 else 0.5

    def _calculate_basic_performance_score(
        self, requirements: ServiceRequirements, service: ServiceInfo
    ) -> float:
        """Calculate basic performance score without AI."""
        if not service.metrics:
            return 0.5  # Unknown performance

        score = 0.0
        checks = 0

        # Availability check
        if service.metrics.availability >= requirements.min_availability:
            score += 1.0
        checks += 1

        # Response time check
        if service.metrics.response_time_p95 <= requirements.max_response_time:
            score += 1.0
        checks += 1

        # Error rate check
        if service.metrics.error_rate <= requirements.max_error_rate:
            score += 1.0
        checks += 1

        return score / checks if checks > 0 else 0.5

    def _get_cache_key(
        self, requirements: ServiceRequirements, services: List[ServiceInfo]
    ) -> str:
        """Generate cache key for matching request."""
        import hashlib
        
        # Create deterministic key from requirements and service IDs
        req_str = json.dumps(requirements.to_dict(), sort_keys=True)
        service_ids = sorted([s.id for s in services])
        content = f"{req_str}|{','.join(service_ids)}"
        
        return hashlib.md5(content.encode()).hexdigest()

    def _get_cached_matches(self, cache_key: str) -> Optional[List[ServiceMatch]]:
        """Get cached matches if still valid."""
        cached = self.match_cache.get(cache_key)
        if cached:
            # Check if cache is still valid (based on cache_ttl)
            # For simplicity, we'll implement basic TTL later
            return cached
        return None

    def _cache_matches(self, cache_key: str, matches: List[ServiceMatch]) -> None:
        """Cache matching results."""
        self.match_cache[cache_key] = matches
        
        # Simple cache size management
        if len(self.match_cache) > 100:
            # Remove oldest entries
            keys_to_remove = list(self.match_cache.keys())[:20]
            for key in keys_to_remove:
                del self.match_cache[key]