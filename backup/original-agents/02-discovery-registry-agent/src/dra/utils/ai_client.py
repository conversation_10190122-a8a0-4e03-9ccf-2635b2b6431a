"""
AI Client utility for interacting with various AI/LLM providers.

Provides a unified interface for AI model interactions with fallback support
and intelligent routing based on model capabilities and availability.
"""

import asyncio
import os
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta

import httpx
import structlog
from anthropic import Async<PERSON><PERSON>hropic
from openai import AsyncOpenAI
import google.generativeai as genai


logger = structlog.get_logger(__name__)


class AIClient:
    """
    Unified AI client supporting multiple providers with automatic fallback.
    Supports Anthropic Claude, OpenAI GPT, and Google Gemini models.
    """

    def __init__(self) -> None:
        self.anthropic_client: Optional[AsyncAnthropic] = None
        self.openai_client: Optional[AsyncOpenAI] = None
        self.gemini_client: Optional[genai.GenerativeModel] = None
        
        # Configuration
        self.primary_provider = "claude"  # Primary AI provider
        self.fallback_providers = ["openai", "gemini"]  # Fallback order
        
        # Rate limiting and caching
        self.rate_limits: Dict[str, Dict[str, Any]] = {}
        self.response_cache: Dict[str, Dict[str, Any]] = {}
        self.cache_ttl = timedelta(minutes=10)
        
        # Health tracking
        self.provider_health: Dict[str, bool] = {}
        self.last_health_check: Dict[str, datetime] = {}

    async def initialize(self) -> None:
        """Initialize AI clients for all available providers."""
        logger.info("Initializing AI clients")
        
        try:
            # Initialize Anthropic Claude
            claude_api_key = os.getenv("ANTHROPIC_API_KEY")
            if claude_api_key:
                self.anthropic_client = AsyncAnthropic(api_key=claude_api_key)
                await self._test_provider_health("claude")
                logger.info("Anthropic Claude client initialized")

            # Initialize OpenAI
            openai_api_key = os.getenv("OPENAI_API_KEY")
            if openai_api_key:
                self.openai_client = AsyncOpenAI(api_key=openai_api_key)
                await self._test_provider_health("openai")
                logger.info("OpenAI client initialized")

            # Initialize Google Gemini
            gemini_api_key = os.getenv("GOOGLE_AI_API_KEY")
            if gemini_api_key:
                genai.configure(api_key=gemini_api_key)
                self.gemini_client = genai.GenerativeModel('gemini-pro')
                await self._test_provider_health("gemini")
                logger.info("Google Gemini client initialized")

            if not any([self.anthropic_client, self.openai_client, self.gemini_client]):
                logger.warning("No AI providers configured - using fallback mode")

        except Exception as e:
            logger.error("Failed to initialize AI clients", error=str(e))
            raise

    async def is_healthy(self) -> bool:
        """Check if at least one AI provider is healthy."""
        return any(self.provider_health.values())

    async def generate_response(
        self,
        prompt: str,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        system_prompt: Optional[str] = None,
        provider: Optional[str] = None,
    ) -> str:
        """
        Generate a response using the best available AI provider.
        
        Args:
            prompt: The user prompt
            temperature: Sampling temperature (0.0-1.0)
            max_tokens: Maximum tokens in response
            system_prompt: Optional system prompt
            provider: Specific provider to use (optional)
            
        Returns:
            Generated response text
        """
        try:
            # Check cache first
            cache_key = self._get_cache_key(prompt, temperature, max_tokens, system_prompt)
            cached_response = self._get_cached_response(cache_key)
            if cached_response:
                return cached_response

            # Determine provider to use
            if provider:
                providers_to_try = [provider]
            else:
                providers_to_try = [self.primary_provider] + self.fallback_providers

            # Try providers in order until one succeeds
            for provider_name in providers_to_try:
                if not self._is_provider_available(provider_name):
                    continue

                try:
                    response = await self._generate_with_provider(
                        provider_name, prompt, temperature, max_tokens, system_prompt
                    )
                    
                    # Cache successful response
                    self._cache_response(cache_key, response)
                    
                    logger.debug(
                        "AI response generated successfully",
                        provider=provider_name,
                        prompt_length=len(prompt),
                        response_length=len(response),
                    )
                    
                    return response

                except Exception as e:
                    logger.warning(
                        "Provider failed, trying next",
                        provider=provider_name,
                        error=str(e),
                    )
                    # Mark provider as unhealthy temporarily
                    self.provider_health[provider_name] = False
                    continue

            # All providers failed
            logger.error("All AI providers failed")
            return self._get_fallback_response(prompt)

        except Exception as e:
            logger.error("AI response generation failed", error=str(e))
            return self._get_fallback_response(prompt)

    async def _generate_with_provider(
        self,
        provider: str,
        prompt: str,
        temperature: float,
        max_tokens: int,
        system_prompt: Optional[str] = None,
    ) -> str:
        """Generate response with a specific provider."""
        if provider == "claude" and self.anthropic_client:
            return await self._generate_with_claude(
                prompt, temperature, max_tokens, system_prompt
            )
        elif provider == "openai" and self.openai_client:
            return await self._generate_with_openai(
                prompt, temperature, max_tokens, system_prompt
            )
        elif provider == "gemini" and self.gemini_client:
            return await self._generate_with_gemini(
                prompt, temperature, max_tokens, system_prompt
            )
        else:
            raise ValueError(f"Provider {provider} not available")

    async def _generate_with_claude(
        self,
        prompt: str,
        temperature: float,
        max_tokens: int,
        system_prompt: Optional[str] = None,
    ) -> str:
        """Generate response using Anthropic Claude."""
        if not self.anthropic_client:
            raise RuntimeError("Claude client not initialized")

        messages = [{"role": "user", "content": prompt}]
        
        kwargs = {
            "model": "claude-3-opus-20240229",
            "max_tokens": max_tokens,
            "temperature": temperature,
            "messages": messages,
        }
        
        if system_prompt:
            kwargs["system"] = system_prompt

        response = await self.anthropic_client.messages.create(**kwargs)
        return response.content[0].text

    async def _generate_with_openai(
        self,
        prompt: str,
        temperature: float,
        max_tokens: int,
        system_prompt: Optional[str] = None,
    ) -> str:
        """Generate response using OpenAI GPT."""
        if not self.openai_client:
            raise RuntimeError("OpenAI client not initialized")

        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})

        response = await self.openai_client.chat.completions.create(
            model="gpt-4-turbo-preview",
            messages=messages,
            temperature=temperature,
            max_tokens=max_tokens,
        )
        
        return response.choices[0].message.content or ""

    async def _generate_with_gemini(
        self,
        prompt: str,
        temperature: float,
        max_tokens: int,
        system_prompt: Optional[str] = None,
    ) -> str:
        """Generate response using Google Gemini."""
        if not self.gemini_client:
            raise RuntimeError("Gemini client not initialized")

        # Combine system prompt and user prompt for Gemini
        full_prompt = prompt
        if system_prompt:
            full_prompt = f"{system_prompt}\n\n{prompt}"

        # Gemini uses generate_content_async for async calls
        response = await asyncio.to_thread(
            self.gemini_client.generate_content,
            full_prompt,
            generation_config=genai.types.GenerationConfig(
                temperature=temperature,
                max_output_tokens=max_tokens,
            ),
        )
        
        return response.text

    async def _test_provider_health(self, provider: str) -> bool:
        """Test if a provider is healthy and responsive."""
        try:
            test_prompt = "Test prompt for health check"
            response = await self._generate_with_provider(
                provider, test_prompt, 0.1, 10
            )
            
            is_healthy = len(response) > 0
            self.provider_health[provider] = is_healthy
            self.last_health_check[provider] = datetime.utcnow()
            
            return is_healthy

        except Exception as e:
            logger.warning(f"Health check failed for {provider}", error=str(e))
            self.provider_health[provider] = False
            self.last_health_check[provider] = datetime.utcnow()
            return False

    def _is_provider_available(self, provider: str) -> bool:
        """Check if a provider is available and healthy."""
        # Check if provider is configured
        if provider == "claude" and not self.anthropic_client:
            return False
        elif provider == "openai" and not self.openai_client:
            return False
        elif provider == "gemini" and not self.gemini_client:
            return False

        # Check health status (with grace period for temporary failures)
        health = self.provider_health.get(provider, False)
        last_check = self.last_health_check.get(provider, datetime.min)
        
        # If unhealthy for more than 5 minutes, retry health check
        if not health and (datetime.utcnow() - last_check) > timedelta(minutes=5):
            # Schedule health check (don't await to avoid blocking)
            asyncio.create_task(self._test_provider_health(provider))

        return health

    def _get_cache_key(
        self,
        prompt: str,
        temperature: float,
        max_tokens: int,
        system_prompt: Optional[str],
    ) -> str:
        """Generate cache key for a request."""
        import hashlib
        
        content = f"{prompt}|{temperature}|{max_tokens}|{system_prompt or ''}"
        return hashlib.md5(content.encode()).hexdigest()

    def _get_cached_response(self, cache_key: str) -> Optional[str]:
        """Get cached response if still valid."""
        cached = self.response_cache.get(cache_key)
        if cached and (datetime.utcnow() - cached["timestamp"]) < self.cache_ttl:
            return cached["response"]
        return None

    def _cache_response(self, cache_key: str, response: str) -> None:
        """Cache a response."""
        self.response_cache[cache_key] = {
            "response": response,
            "timestamp": datetime.utcnow(),
        }
        
        # Clean old cache entries to prevent memory growth
        if len(self.response_cache) > 1000:
            # Remove oldest 200 entries
            sorted_keys = sorted(
                self.response_cache.keys(),
                key=lambda k: self.response_cache[k]["timestamp"],
            )
            for key in sorted_keys[:200]:
                del self.response_cache[key]

    def _get_fallback_response(self, prompt: str) -> str:
        """Generate a fallback response when all AI providers fail."""
        return """
I apologize, but I'm currently experiencing issues connecting to AI services. 
This is a fallback response. The system is designed to continue operating 
with reduced functionality until AI services are restored.

For service health predictions, please refer to traditional monitoring metrics 
and established health check procedures.
"""

    async def get_embeddings(
        self, texts: List[str], provider: str = "openai"
    ) -> List[List[float]]:
        """
        Get text embeddings from AI providers.
        
        Args:
            texts: List of texts to embed
            provider: Provider to use for embeddings
            
        Returns:
            List of embedding vectors
        """
        try:
            if provider == "openai" and self.openai_client:
                response = await self.openai_client.embeddings.create(
                    model="text-embedding-ada-002", input=texts
                )
                return [item.embedding for item in response.data]
            else:
                # Fallback to local sentence transformers
                logger.warning("Using local embeddings as fallback")
                return await self._get_local_embeddings(texts)

        except Exception as e:
            logger.error("Embedding generation failed", error=str(e))
            return await self._get_local_embeddings(texts)

    async def _get_local_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Get embeddings using local sentence transformers as fallback."""
        try:
            from sentence_transformers import SentenceTransformer
            
            # Use a lightweight model for fallback
            model = SentenceTransformer('all-MiniLM-L6-v2')
            embeddings = model.encode(texts, convert_to_tensor=False)
            return embeddings.tolist()
            
        except Exception as e:
            logger.error("Local embedding generation failed", error=str(e))
            # Return zero vectors as last resort
            return [[0.0] * 384 for _ in texts]  # MiniLM embedding size