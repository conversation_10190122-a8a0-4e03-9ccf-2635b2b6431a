"""
Core type definitions for the Discovery Registry Agent.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union
from uuid import UUID, uuid4

from pydantic import BaseModel, Field


class AgentIntelligenceLevel(str, Enum):
    """Agent intelligence level enumeration."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    MEDIUM_HIGH = "medium-high"
    VERY_HIGH = "very-high"
    SUPREME = "supreme"


class AgentStatus(str, Enum):
    """Agent status enumeration."""
    INITIALIZING = "initializing"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    STOPPED = "stopped"
    ERROR = "error"
    MAINTENANCE = "maintenance"


class ServiceStatus(str, Enum):
    """Service status enumeration."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"
    STARTING = "starting"
    STOPPING = "stopping"
    RUNNING = "running"


class ServiceProtocol(str, Enum):
    """Service protocol enumeration."""
    HTTP = "http"
    HTTPS = "https"
    GRPC = "grpc"
    TCP = "tcp"
    UDP = "udp"
    WEBSOCKET = "websocket"


class HealthStatus(BaseModel):
    """Health status information."""
    status: str
    timestamp: datetime
    details: Dict[str, Any] = Field(default_factory=dict)
    checks: Dict[str, bool] = Field(default_factory=dict)
    message: Optional[str] = None


class ServiceEndpoint(BaseModel):
    """Service endpoint configuration."""
    protocol: ServiceProtocol
    host: str
    port: int
    path: Optional[str] = None
    
    @property
    def url(self) -> str:
        """Get the full URL for this endpoint."""
        base = f"{self.protocol.value}://{self.host}:{self.port}"
        if self.path:
            return f"{base}{self.path}"
        return base


class ServiceCapability(BaseModel):
    """Service capability definition."""
    name: str
    version: str
    description: str
    parameters: Dict[str, Any] = Field(default_factory=dict)
    dependencies: List[str] = Field(default_factory=list)


class ServiceMetrics(BaseModel):
    """Service performance metrics."""
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    request_rate: float = 0.0
    error_rate: float = 0.0
    response_time_p50: float = 0.0
    response_time_p95: float = 0.0
    response_time_p99: float = 0.0
    availability: float = 1.0
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class ServiceInfo(BaseModel):
    """Comprehensive service information."""
    id: str = Field(default_factory=lambda: str(uuid4()))
    name: str
    version: str
    description: str = ""
    
    # Service endpoint information
    endpoints: List[ServiceEndpoint]
    capabilities: List[ServiceCapability] = Field(default_factory=list)
    
    # Health and status
    status: ServiceStatus = ServiceStatus.UNKNOWN
    health: Optional[HealthStatus] = None
    last_heartbeat: Optional[datetime] = None
    
    # Metadata and tags
    tags: List[str] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    # Performance metrics
    metrics: Optional[ServiceMetrics] = None
    
    # Registry information
    registered_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    namespace: str = "default"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return self.model_dump()


class ServiceRequirements(BaseModel):
    """Service discovery requirements."""
    capabilities: List[str] = Field(default_factory=list)
    protocols: List[ServiceProtocol] = Field(default_factory=list)
    tags: List[str] = Field(default_factory=list)
    description: str = ""
    
    # Performance requirements
    min_availability: float = 0.95
    max_response_time: float = 1000.0  # milliseconds
    max_error_rate: float = 0.05
    
    # Location and resource constraints
    namespace: str = "default"
    region: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return self.model_dump()


class ServiceMatch(BaseModel):
    """Result of service capability matching."""
    service: ServiceInfo
    similarity_score: float = Field(ge=0.0, le=1.0)
    confidence: float = Field(ge=0.0, le=1.0)
    reasoning: str = ""
    match_details: Dict[str, Any] = Field(default_factory=dict)


class DetailedMatchAnalysis(BaseModel):
    """Detailed analysis of service capability matching."""
    functional_alignment: float = Field(ge=0.0, le=1.0)
    performance_compatibility: float = Field(ge=0.0, le=1.0)
    security_compliance: float = Field(ge=0.0, le=1.0)
    integration_complexity: float = Field(ge=0.0, le=1.0)
    overall_score: float = Field(ge=0.0, le=1.0)
    confidence: float = Field(ge=0.0, le=1.0)
    reasoning: str
    recommendations: List[str] = Field(default_factory=list)


class HealthPrediction(BaseModel):
    """Health prediction for a service."""
    service_id: str
    predictions: Dict[str, ServiceStatus]  # time_horizon -> predicted_status
    confidence_scores: Dict[str, float]
    risk_factors: List[str] = Field(default_factory=list)
    recommended_actions: List[str] = Field(default_factory=list)
    prediction_timestamp: datetime = Field(default_factory=datetime.utcnow)


class OptimizationRecommendation(BaseModel):
    """Service mesh optimization recommendation."""
    recommendation_id: str = Field(default_factory=lambda: str(uuid4()))
    category: str  # routing, load_balancing, circuit_breaker, etc.
    title: str
    description: str
    impact_score: float = Field(ge=0.0, le=10.0)
    implementation_effort: str  # low, medium, high
    priority: str  # low, medium, high, critical
    configuration_changes: Dict[str, Any] = Field(default_factory=dict)
    expected_benefits: List[str] = Field(default_factory=list)
    created_at: datetime = Field(default_factory=datetime.utcnow)


class AgentMessage(BaseModel):
    """Message structure for agent communication."""
    id: str = Field(default_factory=lambda: str(uuid4()))
    from_agent_id: str
    to_agent_id: str
    message_type: str
    payload: Dict[str, Any]
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    correlation_id: Optional[str] = None


class AgentMetrics(BaseModel):
    """Agent performance metrics."""
    agent_id: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    messages_processed: int = 0
    services_registered: int = 0
    health_predictions_made: int = 0
    capability_matches_performed: int = 0
    avg_response_time: float = 0.0
    error_rate: float = 0.0
    cpu_usage: float = 0.0
    memory_usage: float = 0.0


class ServiceRegistration(BaseModel):
    """Service registration request."""
    service: ServiceInfo
    ttl: Optional[int] = None  # Time-to-live in seconds
    auto_health_check: bool = True
    registration_time: datetime = Field(default_factory=datetime.utcnow)