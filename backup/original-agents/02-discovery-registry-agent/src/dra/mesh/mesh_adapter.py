"""
Base service mesh adapter for DRA mesh integrations.

This module provides the base interface for service mesh integrations
and common functionality shared across different mesh platforms.
"""

import asyncio
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from datetime import datetime
from enum import Enum

import structlog

from dra.core.types import ServiceInfo, OptimizationRecommendation


logger = structlog.get_logger(__name__)


class MeshPlatform(str, Enum):
    """Supported service mesh platforms."""
    ISTIO = "istio"
    LINKERD = "linkerd"
    CONSUL_CONNECT = "consul_connect"
    ENVOY = "envoy"
    NGINX_MESH = "nginx_mesh"


class ServiceMeshAdapter(ABC):
    """
    Base adapter for service mesh integrations.
    Provides common interface for different mesh platforms.
    """

    def __init__(self, platform: MeshPlatform, config: Dict[str, Any]) -> None:
        self.platform = platform
        self.config = config
        self.initialized = False
        
        # Statistics
        self.stats = {
            "services_synchronized": 0,
            "configurations_applied": 0,
            "health_checks_performed": 0,
            "errors": 0,
        }

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the mesh adapter."""
        pass

    @abstractmethod
    async def is_healthy(self) -> bool:
        """Check if the mesh adapter is healthy."""
        pass

    @abstractmethod
    async def sync_service(self, service: ServiceInfo) -> bool:
        """Synchronize a service with the mesh."""
        pass

    @abstractmethod
    async def remove_service(self, service_id: str) -> bool:
        """Remove a service from the mesh."""
        pass

    @abstractmethod
    async def get_service_metrics(self, service_id: str) -> Optional[Dict[str, Any]]:
        """Get service metrics from the mesh."""
        pass

    @abstractmethod
    async def apply_optimization(self, recommendation: OptimizationRecommendation) -> bool:
        """Apply an optimization recommendation to the mesh."""
        pass

    @abstractmethod
    async def get_mesh_configuration(self) -> Dict[str, Any]:
        """Get current mesh configuration."""
        pass

    async def get_statistics(self) -> Dict[str, Any]:
        """Get adapter statistics."""
        return {
            "platform": self.platform.value,
            "initialized": self.initialized,
            "statistics": self.stats,
            "configuration": self._get_safe_config(),
        }

    def _get_safe_config(self) -> Dict[str, Any]:
        """Get configuration without sensitive data."""
        safe_config = self.config.copy()
        
        # Remove sensitive fields
        sensitive_fields = ["api_key", "password", "token", "secret"]
        for field in sensitive_fields:
            if field in safe_config:
                safe_config[field] = "***"
        
        return safe_config

    async def _record_error(self, operation: str, error: Exception) -> None:
        """Record an error for statistics."""
        self.stats["errors"] += 1
        logger.error(
            "Mesh adapter operation failed",
            platform=self.platform.value,
            operation=operation,
            error=str(error),
        )