"""
Istio service mesh adapter for DRA.

This module provides integration with Istio service mesh platform
for service registration, configuration, and optimization.
"""

import asyncio
import json
from typing import Any, Dict, List, Optional
from datetime import datetime

import structlog
import httpx
import yaml
from kubernetes import client, config
from kubernetes.client.rest import ApiException

from dra.core.types import ServiceInfo, OptimizationRecommendation
from dra.mesh.mesh_adapter import ServiceMeshAdapter, MeshPlatform


logger = structlog.get_logger(__name__)


class IstioAdapter(ServiceMeshAdapter):
    """
    Istio service mesh adapter.
    Integrates with Istio for service mesh management and optimization.
    """

    def __init__(self, config: Dict[str, Any]) -> None:
        super().__init__(MeshPlatform.ISTIO, config)
        
        # Kubernetes clients
        self.k8s_apps_v1: Optional[client.AppsV1Api] = None
        self.k8s_core_v1: Optional[client.CoreV1Api] = None
        self.k8s_custom: Optional[client.CustomObjectsApi] = None
        
        # Istio configuration
        self.namespace = config.get("namespace", "default")
        self.istio_namespace = config.get("istio_namespace", "istio-system")
        self.gateway_name = config.get("gateway_name", "dra-gateway")
        
        # Metrics configuration
        self.prometheus_url = config.get("prometheus_url", "http://prometheus:9090")
        self.grafana_url = config.get("grafana_url", "http://grafana:3000")

    async def initialize(self) -> None:
        """Initialize the Istio adapter."""
        logger.info("Initializing Istio adapter")
        
        try:
            # Load Kubernetes configuration
            try:
                config.load_incluster_config()
                logger.info("Using in-cluster Kubernetes configuration")
            except config.ConfigException:
                config.load_kube_config()
                logger.info("Using local Kubernetes configuration")
            
            # Initialize Kubernetes clients
            self.k8s_apps_v1 = client.AppsV1Api()
            self.k8s_core_v1 = client.CoreV1Api()
            self.k8s_custom = client.CustomObjectsApi()
            
            # Verify Istio installation
            await self._verify_istio_installation()
            
            # Initialize default gateway
            await self._ensure_default_gateway()
            
            self.initialized = True
            logger.info("Istio adapter initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize Istio adapter", error=str(e))
            await self._record_error("initialize", e)
            raise

    async def is_healthy(self) -> bool:
        """Check if the Istio adapter is healthy."""
        try:
            if not self.initialized:
                return False
            
            # Check Kubernetes API connectivity
            await asyncio.to_thread(
                self.k8s_core_v1.list_namespace, limit=1
            )
            
            # Check Istio components
            istio_pods = await asyncio.to_thread(
                self.k8s_core_v1.list_namespaced_pod,
                namespace=self.istio_namespace,
                label_selector="app=istiod",
            )
            
            return len(istio_pods.items) > 0
            
        except Exception as e:
            logger.error("Istio adapter health check failed", error=str(e))
            return False

    async def sync_service(self, service: ServiceInfo) -> bool:
        """Synchronize a service with Istio."""
        try:
            logger.info("Syncing service with Istio", service_id=service.id)
            
            # Create Kubernetes service
            k8s_service_created = await self._create_k8s_service(service)
            
            # Create Istio VirtualService
            virtual_service_created = await self._create_virtual_service(service)
            
            # Create Istio DestinationRule
            destination_rule_created = await self._create_destination_rule(service)
            
            success = k8s_service_created and virtual_service_created and destination_rule_created
            
            if success:
                self.stats["services_synchronized"] += 1
                logger.info("Service synced with Istio successfully", service_id=service.id)
            else:
                logger.error("Failed to sync service with Istio", service_id=service.id)
            
            return success
            
        except Exception as e:
            logger.error("Service sync failed", service_id=service.id, error=str(e))
            await self._record_error("sync_service", e)
            return False

    async def remove_service(self, service_id: str) -> bool:
        """Remove a service from Istio."""
        try:
            logger.info("Removing service from Istio", service_id=service_id)
            
            # Remove Kubernetes service
            k8s_service_removed = await self._delete_k8s_service(service_id)
            
            # Remove Istio VirtualService
            virtual_service_removed = await self._delete_virtual_service(service_id)
            
            # Remove Istio DestinationRule
            destination_rule_removed = await self._delete_destination_rule(service_id)
            
            success = k8s_service_removed and virtual_service_removed and destination_rule_removed
            
            if success:
                logger.info("Service removed from Istio successfully", service_id=service_id)
            else:
                logger.error("Failed to remove service from Istio", service_id=service_id)
            
            return success
            
        except Exception as e:
            logger.error("Service removal failed", service_id=service_id, error=str(e))
            await self._record_error("remove_service", e)
            return False

    async def get_service_metrics(self, service_id: str) -> Optional[Dict[str, Any]]:
        """Get service metrics from Istio/Prometheus."""
        try:
            if not self.prometheus_url:
                return None
            
            metrics = {}
            
            # Query Prometheus for service metrics
            async with httpx.AsyncClient() as client:
                # Request rate
                rate_query = f'sum(rate(istio_requests_total{{destination_service_name="{service_id}"}}[5m]))'
                rate_response = await client.get(
                    f"{self.prometheus_url}/api/v1/query",
                    params={"query": rate_query},
                )
                
                if rate_response.status_code == 200:
                    rate_data = rate_response.json()
                    if rate_data.get("data", {}).get("result"):
                        metrics["request_rate"] = float(rate_data["data"]["result"][0]["value"][1])
                
                # Error rate
                error_query = f'sum(rate(istio_requests_total{{destination_service_name="{service_id}",response_code!~"2.*"}}[5m])) / sum(rate(istio_requests_total{{destination_service_name="{service_id}"}}[5m]))'
                error_response = await client.get(
                    f"{self.prometheus_url}/api/v1/query",
                    params={"query": error_query},
                )
                
                if error_response.status_code == 200:
                    error_data = error_response.json()
                    if error_data.get("data", {}).get("result"):
                        metrics["error_rate"] = float(error_data["data"]["result"][0]["value"][1])
                
                # Response time (P95)
                latency_query = f'histogram_quantile(0.95, sum(rate(istio_request_duration_milliseconds_bucket{{destination_service_name="{service_id}"}}[5m])) by (le))'
                latency_response = await client.get(
                    f"{self.prometheus_url}/api/v1/query",
                    params={"query": latency_query},
                )
                
                if latency_response.status_code == 200:
                    latency_data = latency_response.json()
                    if latency_data.get("data", {}).get("result"):
                        metrics["response_time_p95"] = float(latency_data["data"]["result"][0]["value"][1])
            
            return metrics if metrics else None
            
        except Exception as e:
            logger.error("Failed to get service metrics", service_id=service_id, error=str(e))
            await self._record_error("get_service_metrics", e)
            return None

    async def apply_optimization(self, recommendation: OptimizationRecommendation) -> bool:
        """Apply an optimization recommendation to Istio."""
        try:
            logger.info(
                "Applying optimization to Istio",
                category=recommendation.category,
                title=recommendation.title,
            )
            
            success = False
            
            if recommendation.category == "routing":
                success = await self._apply_routing_optimization(recommendation)
            elif recommendation.category == "load_balancing":
                success = await self._apply_load_balancing_optimization(recommendation)
            elif recommendation.category == "circuit_breaker":
                success = await self._apply_circuit_breaker_optimization(recommendation)
            elif recommendation.category == "timeout_retry":
                success = await self._apply_timeout_retry_optimization(recommendation)
            elif recommendation.category == "security":
                success = await self._apply_security_optimization(recommendation)
            else:
                logger.warning(
                    "Unknown optimization category",
                    category=recommendation.category,
                )
            
            if success:
                self.stats["configurations_applied"] += 1
                logger.info("Optimization applied successfully", title=recommendation.title)
            
            return success
            
        except Exception as e:
            logger.error("Optimization application failed", error=str(e))
            await self._record_error("apply_optimization", e)
            return False

    async def get_mesh_configuration(self) -> Dict[str, Any]:
        """Get current Istio mesh configuration."""
        try:
            config_data = {
                "platform": "istio",
                "namespace": self.namespace,
                "istio_namespace": self.istio_namespace,
                "services": [],
                "virtual_services": [],
                "destination_rules": [],
                "gateways": [],
            }
            
            # Get Istio resources
            try:
                # VirtualServices
                virtual_services = await asyncio.to_thread(
                    self.k8s_custom.list_namespaced_custom_object,
                    group="networking.istio.io",
                    version="v1beta1",
                    namespace=self.namespace,
                    plural="virtualservices",
                )
                config_data["virtual_services"] = [
                    vs["metadata"]["name"] for vs in virtual_services.get("items", [])
                ]
                
                # DestinationRules
                destination_rules = await asyncio.to_thread(
                    self.k8s_custom.list_namespaced_custom_object,
                    group="networking.istio.io",
                    version="v1beta1",
                    namespace=self.namespace,
                    plural="destinationrules",
                )
                config_data["destination_rules"] = [
                    dr["metadata"]["name"] for dr in destination_rules.get("items", [])
                ]
                
                # Gateways
                gateways = await asyncio.to_thread(
                    self.k8s_custom.list_namespaced_custom_object,
                    group="networking.istio.io",
                    version="v1beta1",
                    namespace=self.namespace,
                    plural="gateways",
                )
                config_data["gateways"] = [
                    gw["metadata"]["name"] for gw in gateways.get("items", [])
                ]
                
            except ApiException as e:
                logger.warning("Failed to get some Istio resources", error=str(e))
            
            return config_data
            
        except Exception as e:
            logger.error("Failed to get mesh configuration", error=str(e))
            await self._record_error("get_mesh_configuration", e)
            return {"error": str(e)}

    # Private implementation methods

    async def _verify_istio_installation(self) -> None:
        """Verify that Istio is installed and accessible."""
        try:
            # Check for Istio namespace
            await asyncio.to_thread(
                self.k8s_core_v1.read_namespace,
                name=self.istio_namespace,
            )
            
            # Check for Istio CRDs
            crd_names = [
                "virtualservices.networking.istio.io",
                "destinationrules.networking.istio.io",
                "gateways.networking.istio.io",
            ]
            
            for crd_name in crd_names:
                try:
                    await asyncio.to_thread(
                        self.k8s_custom.get_cluster_custom_object,
                        group="apiextensions.k8s.io",
                        version="v1",
                        plural="customresourcedefinitions",
                        name=crd_name,
                    )
                except ApiException:
                    raise RuntimeError(f"Istio CRD {crd_name} not found")
            
            logger.info("Istio installation verified")
            
        except Exception as e:
            logger.error("Istio verification failed", error=str(e))
            raise RuntimeError(f"Istio not properly installed: {str(e)}")

    async def _ensure_default_gateway(self) -> None:
        """Ensure a default gateway exists."""
        try:
            gateway_spec = {
                "apiVersion": "networking.istio.io/v1beta1",
                "kind": "Gateway",
                "metadata": {
                    "name": self.gateway_name,
                    "namespace": self.namespace,
                },
                "spec": {
                    "selector": {"istio": "ingressgateway"},
                    "servers": [
                        {
                            "port": {"number": 80, "name": "http", "protocol": "HTTP"},
                            "hosts": ["*"],
                        }
                    ],
                },
            }
            
            try:
                await asyncio.to_thread(
                    self.k8s_custom.create_namespaced_custom_object,
                    group="networking.istio.io",
                    version="v1beta1",
                    namespace=self.namespace,
                    plural="gateways",
                    body=gateway_spec,
                )
                logger.info("Default gateway created", gateway_name=self.gateway_name)
            except ApiException as e:
                if e.status == 409:  # Already exists
                    logger.debug("Default gateway already exists", gateway_name=self.gateway_name)
                else:
                    raise
            
        except Exception as e:
            logger.error("Failed to ensure default gateway", error=str(e))
            raise

    async def _create_k8s_service(self, service: ServiceInfo) -> bool:
        """Create Kubernetes service for the DRA service."""
        try:
            # Create service spec
            service_spec = {
                "apiVersion": "v1",
                "kind": "Service",
                "metadata": {
                    "name": service.id,
                    "namespace": self.namespace,
                    "labels": {
                        "app": service.id,
                        "managed-by": "dra",
                    },
                },
                "spec": {
                    "selector": {"app": service.id},
                    "ports": [
                        {
                            "name": f"{endpoint.protocol.value}-{endpoint.port}",
                            "port": endpoint.port,
                            "targetPort": endpoint.port,
                            "protocol": "TCP",
                        }
                        for endpoint in service.endpoints
                    ],
                    "type": "ClusterIP",
                },
            }
            
            await asyncio.to_thread(
                self.k8s_core_v1.create_namespaced_service,
                namespace=self.namespace,
                body=service_spec,
            )
            
            return True
            
        except ApiException as e:
            if e.status == 409:  # Already exists
                return True
            logger.error("Failed to create Kubernetes service", error=str(e))
            return False

    async def _create_virtual_service(self, service: ServiceInfo) -> bool:
        """Create Istio VirtualService for the service."""
        try:
            virtual_service_spec = {
                "apiVersion": "networking.istio.io/v1beta1",
                "kind": "VirtualService",
                "metadata": {
                    "name": f"{service.id}-vs",
                    "namespace": self.namespace,
                },
                "spec": {
                    "hosts": [service.id],
                    "http": [
                        {
                            "route": [
                                {
                                    "destination": {
                                        "host": service.id,
                                        "port": {"number": service.endpoints[0].port},
                                    }
                                }
                            ]
                        }
                    ],
                },
            }
            
            await asyncio.to_thread(
                self.k8s_custom.create_namespaced_custom_object,
                group="networking.istio.io",
                version="v1beta1",
                namespace=self.namespace,
                plural="virtualservices",
                body=virtual_service_spec,
            )
            
            return True
            
        except ApiException as e:
            if e.status == 409:  # Already exists
                return True
            logger.error("Failed to create VirtualService", error=str(e))
            return False

    async def _create_destination_rule(self, service: ServiceInfo) -> bool:
        """Create Istio DestinationRule for the service."""
        try:
            destination_rule_spec = {
                "apiVersion": "networking.istio.io/v1beta1",
                "kind": "DestinationRule",
                "metadata": {
                    "name": f"{service.id}-dr",
                    "namespace": self.namespace,
                },
                "spec": {
                    "host": service.id,
                    "trafficPolicy": {
                        "loadBalancer": {"simple": "ROUND_ROBIN"},
                        "connectionPool": {
                            "tcp": {"maxConnections": 100},
                            "http": {"http1MaxPendingRequests": 10, "maxRequestsPerConnection": 2},
                        },
                        "outlierDetection": {
                            "consecutiveErrors": 3,
                            "interval": "30s",
                            "baseEjectionTime": "30s",
                        },
                    },
                },
            }
            
            await asyncio.to_thread(
                self.k8s_custom.create_namespaced_custom_object,
                group="networking.istio.io",
                version="v1beta1",
                namespace=self.namespace,
                plural="destinationrules",
                body=destination_rule_spec,
            )
            
            return True
            
        except ApiException as e:
            if e.status == 409:  # Already exists
                return True
            logger.error("Failed to create DestinationRule", error=str(e))
            return False

    async def _delete_k8s_service(self, service_id: str) -> bool:
        """Delete Kubernetes service."""
        try:
            await asyncio.to_thread(
                self.k8s_core_v1.delete_namespaced_service,
                name=service_id,
                namespace=self.namespace,
            )
            return True
        except ApiException as e:
            if e.status == 404:  # Not found
                return True
            logger.error("Failed to delete Kubernetes service", error=str(e))
            return False

    async def _delete_virtual_service(self, service_id: str) -> bool:
        """Delete Istio VirtualService."""
        try:
            await asyncio.to_thread(
                self.k8s_custom.delete_namespaced_custom_object,
                group="networking.istio.io",
                version="v1beta1",
                namespace=self.namespace,
                plural="virtualservices",
                name=f"{service_id}-vs",
            )
            return True
        except ApiException as e:
            if e.status == 404:  # Not found
                return True
            logger.error("Failed to delete VirtualService", error=str(e))
            return False

    async def _delete_destination_rule(self, service_id: str) -> bool:
        """Delete Istio DestinationRule."""
        try:
            await asyncio.to_thread(
                self.k8s_custom.delete_namespaced_custom_object,
                group="networking.istio.io",
                version="v1beta1",
                namespace=self.namespace,
                plural="destinationrules",
                name=f"{service_id}-dr",
            )
            return True
        except ApiException as e:
            if e.status == 404:  # Not found
                return True
            logger.error("Failed to delete DestinationRule", error=str(e))
            return False

    async def _apply_routing_optimization(self, recommendation: OptimizationRecommendation) -> bool:
        """Apply routing optimization recommendations."""
        # Implementation would depend on specific routing optimizations
        logger.info("Applying routing optimization", title=recommendation.title)
        return True

    async def _apply_load_balancing_optimization(self, recommendation: OptimizationRecommendation) -> bool:
        """Apply load balancing optimization recommendations."""
        # Implementation would depend on specific load balancing optimizations
        logger.info("Applying load balancing optimization", title=recommendation.title)
        return True

    async def _apply_circuit_breaker_optimization(self, recommendation: OptimizationRecommendation) -> bool:
        """Apply circuit breaker optimization recommendations."""
        # Implementation would depend on specific circuit breaker optimizations
        logger.info("Applying circuit breaker optimization", title=recommendation.title)
        return True

    async def _apply_timeout_retry_optimization(self, recommendation: OptimizationRecommendation) -> bool:
        """Apply timeout and retry optimization recommendations."""
        # Implementation would depend on specific timeout/retry optimizations
        logger.info("Applying timeout/retry optimization", title=recommendation.title)
        return True

    async def _apply_security_optimization(self, recommendation: OptimizationRecommendation) -> bool:
        """Apply security optimization recommendations."""
        # Implementation would depend on specific security optimizations
        logger.info("Applying security optimization", title=recommendation.title)
        return True