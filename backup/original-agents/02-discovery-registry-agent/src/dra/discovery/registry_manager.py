"""
Registry manager for coordinating service discovery operations.

This module coordinates between the service catalog, health monitor,
and capability matcher to provide a unified service registry interface.
"""

import asyncio
import json
from typing import Any, Dict, List, Optional
from datetime import datetime, timedelta

import structlog

from dra.core.types import (
    ServiceInfo,
    ServiceRequirements,
    ServiceMatch,
    ServiceStatus,
    ServiceRegistration,
)
from dra.discovery.service_catalog import IntelligentServiceCatalog, CatalogBackend
from dra.discovery.health_monitor import PredictiveHealthMonitor, HealthCheckResult
from dra.ai.capability_matcher import CapabilityMatchingAI
from dra.ai.service_optimizer import ServiceOptimizationAI


logger = structlog.get_logger(__name__)


class RegistryManager:
    """
    Central registry manager that coordinates all service discovery operations.
    Provides a unified interface for service registration, discovery, and health management.
    """

    def __init__(
        self,
        catalog_backend: CatalogBackend = CatalogBackend.MEMORY,
        redis_url: str = "redis://localhost:6379",
        enable_health_monitoring: bool = True,
        enable_ai_capabilities: bool = True,
    ) -> None:
        self.catalog_backend = catalog_backend
        self.redis_url = redis_url
        self.enable_health_monitoring = enable_health_monitoring
        self.enable_ai_capabilities = enable_ai_capabilities
        
        # Core components
        self.service_catalog: Optional[IntelligentServiceCatalog] = None
        self.health_monitor: Optional[PredictiveHealthMonitor] = None
        self.capability_matcher: Optional[CapabilityMatchingAI] = None
        self.service_optimizer: Optional[ServiceOptimizationAI] = None
        
        # Registry state
        self.initialized = False
        self.startup_time = datetime.utcnow()
        
        # Configuration
        self.auto_cleanup_interval = 300  # seconds
        self.unhealthy_service_timeout = 600  # seconds
        
        # Background tasks
        self.cleanup_task: Optional[asyncio.Task] = None
        self.running = False
        
        # Statistics
        self.stats = {
            "services_registered": 0,
            "services_deregistered": 0,
            "discovery_requests": 0,
            "health_checks": 0,
            "ai_recommendations": 0,
        }

    async def initialize(self) -> None:
        """Initialize the registry manager and all components."""
        logger.info("Initializing Registry Manager")

        try:
            # Initialize service catalog
            self.service_catalog = IntelligentServiceCatalog(
                backend=self.catalog_backend,
                redis_url=self.redis_url,
            )
            await self.service_catalog.initialize()
            
            # Initialize health monitor if enabled
            if self.enable_health_monitoring:
                self.health_monitor = PredictiveHealthMonitor(
                    redis_url=self.redis_url,
                )
                await self.health_monitor.initialize()
                await self.health_monitor.start_monitoring()
            
            # Initialize AI components if enabled
            if self.enable_ai_capabilities:
                self.capability_matcher = CapabilityMatchingAI()
                await self.capability_matcher.initialize()
                
                self.service_optimizer = ServiceOptimizationAI()
                await self.service_optimizer.initialize()
            
            self.initialized = True
            self.running = True
            
            # Start background tasks
            self.cleanup_task = asyncio.create_task(self._cleanup_loop())
            
            logger.info(
                "Registry Manager initialized successfully",
                catalog_backend=self.catalog_backend.value,
                health_monitoring=self.enable_health_monitoring,
                ai_capabilities=self.enable_ai_capabilities,
            )

        except Exception as e:
            logger.error("Failed to initialize Registry Manager", error=str(e))
            raise

    async def shutdown(self) -> None:
        """Shutdown the registry manager and all components."""
        logger.info("Shutting down Registry Manager")

        self.running = False
        
        # Stop background tasks
        if self.cleanup_task:
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass
        
        # Stop health monitoring
        if self.health_monitor:
            await self.health_monitor.stop_monitoring()
        
        logger.info("Registry Manager shutdown complete")

    async def is_healthy(self) -> bool:
        """Check if the registry manager is healthy."""
        try:
            if not self.initialized:
                return False
            
            # Check service catalog
            if not self.service_catalog or not await self.service_catalog.is_healthy():
                return False
            
            # Check health monitor if enabled
            if self.enable_health_monitoring:
                if not self.health_monitor or not await self.health_monitor.is_healthy():
                    return False
            
            # Check AI components if enabled
            if self.enable_ai_capabilities:
                if self.capability_matcher and not await self.capability_matcher.is_healthy():
                    return False
                if self.service_optimizer and not await self.service_optimizer.is_healthy():
                    return False
            
            return True
            
        except Exception:
            return False

    # Service registration operations

    async def register_service(self, registration: ServiceRegistration) -> bool:
        """
        Register a new service.
        
        Args:
            registration: Service registration information
            
        Returns:
            True if registration successful
        """
        try:
            logger.info(
                "Registering service",
                service_id=registration.service.id,
                service_name=registration.service.name,
            )
            
            if not self.service_catalog:
                logger.error("Service catalog not initialized")
                return False
            
            # Register in catalog
            success = await self.service_catalog.register_service(registration.service)
            if not success:
                return False
            
            # Add to health monitoring
            if self.health_monitor:
                await self.health_monitor.add_service(registration.service)
            
            # Update statistics
            self.stats["services_registered"] += 1
            
            logger.info(
                "Service registered successfully",
                service_id=registration.service.id,
            )
            
            return True

        except Exception as e:
            logger.error(
                "Service registration failed",
                service_id=registration.service.id,
                error=str(e),
            )
            return False

    async def deregister_service(self, service_id: str) -> bool:
        """
        Deregister a service.
        
        Args:
            service_id: ID of service to deregister
            
        Returns:
            True if deregistration successful
        """
        try:
            logger.info("Deregistering service", service_id=service_id)
            
            if not self.service_catalog:
                logger.error("Service catalog not initialized")
                return False
            
            # Remove from catalog
            success = await self.service_catalog.deregister_service(service_id)
            if not success:
                return False
            
            # Remove from health monitoring
            if self.health_monitor:
                await self.health_monitor.remove_service(service_id)
            
            # Update statistics
            self.stats["services_deregistered"] += 1
            
            logger.info("Service deregistered successfully", service_id=service_id)
            
            return True

        except Exception as e:
            logger.error(
                "Service deregistration failed",
                service_id=service_id,
                error=str(e),
            )
            return False

    async def update_service(self, service: ServiceInfo) -> bool:
        """
        Update service information.
        
        Args:
            service: Updated service information
            
        Returns:
            True if update successful
        """
        try:
            if not self.service_catalog:
                logger.error("Service catalog not initialized")
                return False
            
            # Update in catalog
            success = await self.service_catalog.update_service(service)
            if not success:
                return False
            
            # Update in health monitoring
            if self.health_monitor:
                await self.health_monitor.remove_service(service.id)
                await self.health_monitor.add_service(service)
            
            logger.info("Service updated successfully", service_id=service.id)
            
            return True

        except Exception as e:
            logger.error(
                "Service update failed",
                service_id=service.id,
                error=str(e),
            )
            return False

    # Service discovery operations

    async def discover_services(
        self,
        requirements: ServiceRequirements,
        include_unhealthy: bool = False,
    ) -> List[ServiceMatch]:
        """
        Discover services matching requirements.
        
        Args:
            requirements: Service requirements
            include_unhealthy: Whether to include unhealthy services
            
        Returns:
            List of matching services
        """
        try:
            self.stats["discovery_requests"] += 1
            
            if not self.service_catalog:
                logger.error("Service catalog not initialized")
                return []
            
            # Get all services in namespace
            all_services = await self.service_catalog.list_services(
                namespace=requirements.namespace,
                status=None if include_unhealthy else ServiceStatus.RUNNING,
            )
            
            if not all_services:
                return []
            
            # Filter by health status if health monitoring is enabled
            if self.health_monitor and not include_unhealthy:
                healthy_services = []
                for service in all_services:
                    health_status = await self.health_monitor.get_service_health_status(
                        service.id
                    )
                    if not health_status or health_status in [
                        HealthCheckResult.HEALTHY,
                        HealthCheckResult.DEGRADED,
                    ]:
                        healthy_services.append(service)
                all_services = healthy_services
            
            # Use AI capability matcher if available
            if self.capability_matcher:
                matches = await self.capability_matcher.match_services_to_requirements(
                    requirements, all_services
                )
            else:
                # Fallback to basic matching
                matches = self._basic_service_matching(requirements, all_services)
            
            logger.info(
                "Service discovery completed",
                requirements_namespace=requirements.namespace,
                total_services=len(all_services),
                matches_found=len(matches),
            )
            
            return matches

        except Exception as e:
            logger.error("Service discovery failed", error=str(e))
            return []

    async def search_services(
        self,
        query: str,
        namespace: Optional[str] = None,
        limit: int = 20,
    ) -> List[ServiceInfo]:
        """
        Search services using natural language query.
        
        Args:
            query: Search query
            namespace: Optional namespace filter
            limit: Maximum results
            
        Returns:
            List of matching services
        """
        try:
            if not self.service_catalog:
                logger.error("Service catalog not initialized")
                return []
            
            return await self.service_catalog.search_services(
                query=query,
                namespace=namespace,
                limit=limit,
            )

        except Exception as e:
            logger.error("Service search failed", error=str(e))
            return []

    async def get_service(self, service_id: str) -> Optional[ServiceInfo]:
        """Get service information by ID."""
        try:
            if not self.service_catalog:
                return None
            
            return await self.service_catalog.get_service(service_id)

        except Exception as e:
            logger.error("Failed to get service", service_id=service_id, error=str(e))
            return None

    async def list_services(
        self,
        namespace: Optional[str] = None,
        status: Optional[ServiceStatus] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> List[ServiceInfo]:
        """List services with filtering."""
        try:
            if not self.service_catalog:
                return []
            
            return await self.service_catalog.list_services(
                namespace=namespace,
                status=status,
                limit=limit,
                offset=offset,
            )

        except Exception as e:
            logger.error("Failed to list services", error=str(e))
            return []

    # Health and monitoring operations

    async def get_service_health(self, service_id: str) -> Optional[Dict[str, Any]]:
        """Get comprehensive health information for a service."""
        try:
            if not self.health_monitor:
                return None
            
            # Get current status
            current_status = await self.health_monitor.get_service_health_status(
                service_id
            )
            
            # Get recent history
            history = await self.health_monitor.get_health_history(
                service_id,
                since=datetime.utcnow() - timedelta(hours=1),
                limit=20,
            )
            
            # Get predictions if available
            prediction = await self.health_monitor.predict_health_issues(
                service_id, time_horizon=300
            )
            
            return {
                "service_id": service_id,
                "current_status": current_status.value if current_status else None,
                "recent_events": len(history),
                "prediction": prediction,
                "last_check": history[-1].timestamp.isoformat() if history else None,
            }

        except Exception as e:
            logger.error(
                "Failed to get service health",
                service_id=service_id,
                error=str(e),
            )
            return None

    async def trigger_health_check(self, service_id: str) -> bool:
        """Trigger immediate health check for a service."""
        try:
            if not self.health_monitor:
                return False
            
            self.stats["health_checks"] += 1
            
            result = await self.health_monitor.check_service_health(
                service_id, force=True
            )
            
            return result is not None

        except Exception as e:
            logger.error(
                "Health check trigger failed",
                service_id=service_id,
                error=str(e),
            )
            return False

    # AI and optimization operations

    async def get_optimization_recommendations(
        self, context: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Get AI-powered optimization recommendations."""
        try:
            if not self.service_optimizer:
                return []
            
            self.stats["ai_recommendations"] += 1
            
            recommendations = await self.service_optimizer.generate_recommendations(
                context
            )
            
            return [rec.to_dict() for rec in recommendations]

        except Exception as e:
            logger.error("Failed to get optimization recommendations", error=str(e))
            return []

    # Statistics and monitoring

    async def get_registry_statistics(self) -> Dict[str, Any]:
        """Get comprehensive registry statistics."""
        try:
            stats = {
                "registry_info": {
                    "startup_time": self.startup_time.isoformat(),
                    "uptime_seconds": (datetime.utcnow() - self.startup_time).total_seconds(),
                    "initialized": self.initialized,
                    "running": self.running,
                    "features": {
                        "health_monitoring": self.enable_health_monitoring,
                        "ai_capabilities": self.enable_ai_capabilities,
                    },
                },
                "operations": self.stats,
            }
            
            # Add component statistics
            if self.service_catalog:
                catalog_stats = await self.service_catalog.get_service_statistics()
                stats["service_catalog"] = catalog_stats
            
            if self.health_monitor:
                health_stats = await self.health_monitor.get_monitoring_statistics()
                stats["health_monitoring"] = health_stats
            
            return stats

        except Exception as e:
            logger.error("Failed to get registry statistics", error=str(e))
            return {}

    # Background operations

    async def _cleanup_loop(self) -> None:
        """Background cleanup task."""
        logger.info("Starting registry cleanup loop")
        
        while self.running:
            try:
                await self._perform_cleanup()
                await asyncio.sleep(self.auto_cleanup_interval)
                
            except Exception as e:
                logger.error("Error in cleanup loop", error=str(e))
                await asyncio.sleep(self.auto_cleanup_interval)

    async def _perform_cleanup(self) -> None:
        """Perform registry cleanup operations."""
        try:
            if not self.service_catalog or not self.health_monitor:
                return
            
            # Get all services
            all_services = await self.service_catalog.list_services()
            
            services_to_cleanup = []
            
            # Check for persistently unhealthy services
            for service in all_services:
                health_status = await self.health_monitor.get_service_health_status(
                    service.id
                )
                
                if health_status in [HealthCheckResult.CRITICAL, HealthCheckResult.UNHEALTHY]:
                    # Check how long it's been unhealthy
                    history = await self.health_monitor.get_health_history(
                        service.id,
                        since=datetime.utcnow() - timedelta(seconds=self.unhealthy_service_timeout),
                    )
                    
                    # If consistently unhealthy, mark for cleanup
                    if all(
                        event.status in [HealthCheckResult.CRITICAL, HealthCheckResult.UNHEALTHY]
                        for event in history[-5:]  # Last 5 checks
                    ):
                        services_to_cleanup.append(service.id)
            
            # Cleanup unhealthy services
            for service_id in services_to_cleanup:
                logger.warning(
                    "Auto-cleaning up persistently unhealthy service",
                    service_id=service_id,
                )
                await self.deregister_service(service_id)
            
            if services_to_cleanup:
                logger.info(
                    "Cleanup completed",
                    services_removed=len(services_to_cleanup),
                )

        except Exception as e:
            logger.error("Cleanup operation failed", error=str(e))

    # Helper methods

    def _basic_service_matching(
        self, requirements: ServiceRequirements, services: List[ServiceInfo]
    ) -> List[ServiceMatch]:
        """Basic service matching fallback when AI is unavailable."""
        from dra.core.types import ServiceMatch
        
        matches = []
        
        for service in services:
            score = 0.0
            
            # Check capability matches
            if requirements.capabilities and service.capabilities:
                req_caps = set(requirements.capabilities)
                service_caps = set(cap.name for cap in service.capabilities)
                matches_count = len(req_caps.intersection(service_caps))
                score += (matches_count / len(req_caps)) * 0.6
            
            # Check protocol compatibility
            if requirements.protocols and service.endpoints:
                req_protocols = set(requirements.protocols)
                service_protocols = set(ep.protocol for ep in service.endpoints)
                if req_protocols.intersection(service_protocols):
                    score += 0.2
            
            # Check tag matches
            if requirements.tags and service.tags:
                req_tags = set(requirements.tags)
                service_tags = set(service.tags)
                tag_matches = len(req_tags.intersection(service_tags))
                score += (tag_matches / len(req_tags)) * 0.2
            
            if score > 0:
                match = ServiceMatch(
                    service=service,
                    similarity_score=score,
                    confidence=score * 0.8,
                    reasoning="Basic rule-based matching",
                    match_details={"basic_score": score},
                )
                matches.append(match)
        
        # Sort by score
        matches.sort(key=lambda x: x.confidence, reverse=True)
        
        return matches