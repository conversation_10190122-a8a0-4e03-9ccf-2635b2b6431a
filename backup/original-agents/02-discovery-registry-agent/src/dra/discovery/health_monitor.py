"""
Predictive health monitoring for service discovery.

This module provides real-time health monitoring with AI-powered predictive
capabilities to detect service issues before they impact users.
"""

import asyncio
import json
from typing import Any, Dict, List, Optional, Set
from datetime import datetime, timed<PERSON>ta
from enum import Enum

import structlog
import numpy as np
from redis import asyncio as aioredis

from dra.core.types import ServiceInfo, ServiceStatus, ServiceMetrics, HealthStatus
from dra.ai.health_predictor import HealthPredictionAI


logger = structlog.get_logger(__name__)


class HealthCheckResult(Enum):
    """Health check result types."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


class HealthEvent:
    """Health event data structure."""
    
    def __init__(
        self,
        service_id: str,
        timestamp: datetime,
        status: HealthCheckResult,
        metrics: Optional[Dict[str, float]] = None,
        message: str = "",
        predicted: bool = False,
    ) -> None:
        self.service_id = service_id
        self.timestamp = timestamp
        self.status = status
        self.metrics = metrics or {}
        self.message = message
        self.predicted = predicted
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "service_id": self.service_id,
            "timestamp": self.timestamp.isoformat(),
            "status": self.status.value,
            "metrics": self.metrics,
            "message": self.message,
            "predicted": self.predicted,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "HealthEvent":
        """Create from dictionary."""
        return cls(
            service_id=data["service_id"],
            timestamp=datetime.fromisoformat(data["timestamp"]),
            status=HealthCheckResult(data["status"]),
            metrics=data.get("metrics", {}),
            message=data.get("message", ""),
            predicted=data.get("predicted", False),
        )


class PredictiveHealthMonitor:
    """
    AI-powered health monitor that provides real-time monitoring
    and predictive health analysis for services.
    """

    def __init__(
        self,
        redis_url: str = "redis://localhost:6379",
        check_interval: int = 30,
        prediction_window: int = 300,
    ) -> None:
        self.redis_url = redis_url
        self.check_interval = check_interval  # seconds
        self.prediction_window = prediction_window  # seconds
        
        # Backend connections
        self.redis_client: Optional[aioredis.Redis] = None
        
        # AI components
        self.health_predictor: Optional[HealthPredictionAI] = None
        
        # Monitoring state
        self.monitored_services: Dict[str, ServiceInfo] = {}
        self.health_history: Dict[str, List[HealthEvent]] = {}
        self.active_checks: Set[str] = set()
        
        # Background tasks
        self.monitor_task: Optional[asyncio.Task] = None
        self.prediction_task: Optional[asyncio.Task] = None
        self.running = False
        
        # Configuration
        self.max_history_per_service = 1000
        self.health_thresholds = {
            "response_time": 1000.0,  # ms
            "error_rate": 0.05,  # 5%
            "cpu_usage": 0.8,  # 80%
            "memory_usage": 0.8,  # 80%
            "availability": 0.95,  # 95%
        }
        
        # Statistics
        self.stats = {
            "health_checks_performed": 0,
            "predictions_made": 0,
            "issues_detected": 0,
            "early_warnings": 0,
        }

    async def initialize(self) -> None:
        """Initialize the health monitor."""
        logger.info("Initializing Predictive Health Monitor")

        try:
            # Initialize Redis connection
            self.redis_client = await aioredis.from_url(
                self.redis_url,
                encoding="utf-8",
                decode_responses=True,
            )
            await self.redis_client.ping()
            
            # Initialize AI health predictor
            self.health_predictor = HealthPredictionAI()
            await self.health_predictor.initialize()
            
            # Load existing health history
            await self._load_health_history()
            
            logger.info("Health monitor initialized successfully")

        except Exception as e:
            logger.error("Failed to initialize health monitor", error=str(e))
            raise

    async def is_healthy(self) -> bool:
        """Check if the health monitor is healthy."""
        try:
            # Check Redis connection
            if not self.redis_client:
                return False
            await self.redis_client.ping()
            
            # Check AI predictor
            if not self.health_predictor or not await self.health_predictor.is_healthy():
                return False
            
            return True
            
        except Exception:
            return False

    async def start_monitoring(self) -> None:
        """Start background monitoring tasks."""
        if self.running:
            logger.warning("Health monitor already running")
            return
        
        logger.info("Starting health monitoring")
        
        self.running = True
        
        # Start monitoring task
        self.monitor_task = asyncio.create_task(self._monitoring_loop())
        
        # Start prediction task
        self.prediction_task = asyncio.create_task(self._prediction_loop())
        
        logger.info("Health monitoring started")

    async def stop_monitoring(self) -> None:
        """Stop background monitoring tasks."""
        if not self.running:
            return
        
        logger.info("Stopping health monitoring")
        
        self.running = False
        
        # Cancel tasks
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        
        if self.prediction_task:
            self.prediction_task.cancel()
            try:
                await self.prediction_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Health monitoring stopped")

    async def add_service(self, service: ServiceInfo) -> None:
        """Add a service to monitor."""
        logger.info(
            "Adding service to health monitoring",
            service_id=service.id,
            service_name=service.name,
        )
        
        self.monitored_services[service.id] = service
        
        # Initialize health history if not exists
        if service.id not in self.health_history:
            self.health_history[service.id] = []

    async def remove_service(self, service_id: str) -> None:
        """Remove a service from monitoring."""
        logger.info("Removing service from health monitoring", service_id=service_id)
        
        self.monitored_services.pop(service_id, None)
        
        # Cancel any active checks
        self.active_checks.discard(service_id)

    async def check_service_health(
        self, service_id: str, force: bool = False
    ) -> Optional[HealthEvent]:
        """
        Perform immediate health check for a service.
        
        Args:
            service_id: Service to check
            force: Force check even if already in progress
            
        Returns:
            Health event result
        """
        try:
            # Check if already being checked
            if not force and service_id in self.active_checks:
                logger.debug("Health check already in progress", service_id=service_id)
                return None
            
            service = self.monitored_services.get(service_id)
            if not service:
                logger.warning("Service not found for health check", service_id=service_id)
                return None
            
            # Mark as being checked
            self.active_checks.add(service_id)
            
            try:
                # Perform health check
                health_event = await self._perform_health_check(service)
                
                # Store event
                await self._store_health_event(health_event)
                
                # Update statistics
                self.stats["health_checks_performed"] += 1
                if health_event.status in [HealthCheckResult.DEGRADED, HealthCheckResult.UNHEALTHY, HealthCheckResult.CRITICAL]:
                    self.stats["issues_detected"] += 1
                
                return health_event
                
            finally:
                self.active_checks.discard(service_id)

        except Exception as e:
            logger.error(
                "Health check failed",
                service_id=service_id,
                error=str(e),
            )
            return None

    async def get_service_health_status(self, service_id: str) -> Optional[HealthCheckResult]:
        """Get current health status for a service."""
        try:
            history = self.health_history.get(service_id, [])
            if not history:
                return None
            
            # Return most recent status
            return history[-1].status
            
        except Exception as e:
            logger.error(
                "Failed to get health status",
                service_id=service_id,
                error=str(e),
            )
            return None

    async def get_health_history(
        self,
        service_id: str,
        since: Optional[datetime] = None,
        limit: int = 100,
    ) -> List[HealthEvent]:
        """
        Get health history for a service.
        
        Args:
            service_id: Service ID
            since: Only return events after this time
            limit: Maximum events to return
            
        Returns:
            List of health events
        """
        try:
            history = self.health_history.get(service_id, [])
            
            # Filter by time if specified
            if since:
                history = [event for event in history if event.timestamp >= since]
            
            # Apply limit
            return history[-limit:]
            
        except Exception as e:
            logger.error(
                "Failed to get health history",
                service_id=service_id,
                error=str(e),
            )
            return []

    async def predict_health_issues(
        self, service_id: str, time_horizon: int = 300
    ) -> Optional[Dict[str, Any]]:
        """
        Predict potential health issues for a service.
        
        Args:
            service_id: Service to analyze
            time_horizon: Prediction time horizon in seconds
            
        Returns:
            Prediction results
        """
        try:
            if not self.health_predictor:
                return None
            
            service = self.monitored_services.get(service_id)
            if not service:
                return None
            
            # Get recent health data
            recent_events = await self.get_health_history(
                service_id,
                since=datetime.utcnow() - timedelta(hours=24),
            )
            
            if len(recent_events) < 5:
                return {"status": "insufficient_data"}
            
            # Prepare data for prediction
            metrics_history = []
            for event in recent_events:
                if event.metrics:
                    metrics_history.append({
                        "timestamp": event.timestamp,
                        "metrics": event.metrics,
                        "status": event.status.value,
                    })
            
            # Make prediction
            prediction = await self.health_predictor.predict_health_issues(
                service, metrics_history, time_horizon
            )
            
            self.stats["predictions_made"] += 1
            
            return prediction

        except Exception as e:
            logger.error(
                "Health prediction failed",
                service_id=service_id,
                error=str(e),
            )
            return None

    async def get_monitoring_statistics(self) -> Dict[str, Any]:
        """Get monitoring statistics."""
        try:
            active_services = len(self.monitored_services)
            total_events = sum(len(history) for history in self.health_history.values())
            
            # Calculate health distribution
            health_distribution = {}
            for service_id in self.monitored_services:
                status = await self.get_service_health_status(service_id)
                if status:
                    health_distribution[status.value] = (
                        health_distribution.get(status.value, 0) + 1
                    )
            
            return {
                "active_services": active_services,
                "total_health_events": total_events,
                "health_distribution": health_distribution,
                "operations": self.stats,
                "monitoring_running": self.running,
                "active_checks": len(self.active_checks),
            }

        except Exception as e:
            logger.error("Failed to get monitoring statistics", error=str(e))
            return {}

    # Background monitoring loops

    async def _monitoring_loop(self) -> None:
        """Background monitoring loop."""
        logger.info("Starting health monitoring loop")
        
        while self.running:
            try:
                # Check all monitored services
                tasks = []
                for service_id in list(self.monitored_services.keys()):
                    if service_id not in self.active_checks:
                        task = asyncio.create_task(
                            self.check_service_health(service_id)
                        )
                        tasks.append(task)
                
                # Wait for all checks to complete
                if tasks:
                    await asyncio.gather(*tasks, return_exceptions=True)
                
                # Wait before next cycle
                await asyncio.sleep(self.check_interval)
                
            except Exception as e:
                logger.error("Error in monitoring loop", error=str(e))
                await asyncio.sleep(self.check_interval)

    async def _prediction_loop(self) -> None:
        """Background prediction loop."""
        logger.info("Starting health prediction loop")
        
        # Run predictions less frequently than health checks
        prediction_interval = max(self.prediction_window, 60)
        
        while self.running:
            try:
                # Make predictions for all services
                for service_id in list(self.monitored_services.keys()):
                    try:
                        prediction = await self.predict_health_issues(
                            service_id, self.prediction_window
                        )
                        
                        if prediction and prediction.get("risk_level", "low") in ["high", "critical"]:
                            # Create predictive health event
                            event = HealthEvent(
                                service_id=service_id,
                                timestamp=datetime.utcnow(),
                                status=HealthCheckResult.DEGRADED,
                                message=f"Predicted health issue: {prediction.get('details', '')}",
                                predicted=True,
                            )
                            
                            await self._store_health_event(event)
                            self.stats["early_warnings"] += 1
                            
                            logger.warning(
                                "Predicted health issue",
                                service_id=service_id,
                                prediction=prediction,
                            )
                    
                    except Exception as e:
                        logger.error(
                            "Prediction failed for service",
                            service_id=service_id,
                            error=str(e),
                        )
                
                # Wait before next prediction cycle
                await asyncio.sleep(prediction_interval)
                
            except Exception as e:
                logger.error("Error in prediction loop", error=str(e))
                await asyncio.sleep(prediction_interval)

    # Health check implementation

    async def _perform_health_check(self, service: ServiceInfo) -> HealthEvent:
        """Perform actual health check for a service."""
        try:
            # Collect metrics
            metrics = await self._collect_service_metrics(service)
            
            # Determine health status based on metrics
            status = self._evaluate_health_status(metrics)
            
            # Create health event
            event = HealthEvent(
                service_id=service.id,
                timestamp=datetime.utcnow(),
                status=status,
                metrics=metrics,
                message=f"Health check completed: {status.value}",
            )
            
            return event

        except Exception as e:
            logger.error(
                "Health check execution failed",
                service_id=service.id,
                error=str(e),
            )
            
            # Return unhealthy status on error
            return HealthEvent(
                service_id=service.id,
                timestamp=datetime.utcnow(),
                status=HealthCheckResult.UNKNOWN,
                message=f"Health check failed: {str(e)}",
            )

    async def _collect_service_metrics(self, service: ServiceInfo) -> Dict[str, float]:
        """Collect metrics for a service."""
        metrics = {}
        
        try:
            # Use existing metrics if available
            if service.metrics:
                metrics.update({
                    "response_time": service.metrics.response_time_p95,
                    "error_rate": service.metrics.error_rate,
                    "availability": service.metrics.availability,
                    "throughput": service.metrics.throughput,
                })
            
            # TODO: Collect real-time metrics from service endpoints
            # This would involve making HTTP requests to health endpoints,
            # querying metrics systems, etc.
            
            # For now, use mock metrics that simulate real data
            import random
            base_metrics = {
                "response_time": 100 + random.random() * 200,
                "error_rate": random.random() * 0.1,
                "cpu_usage": 0.3 + random.random() * 0.4,
                "memory_usage": 0.4 + random.random() * 0.3,
                "availability": 0.95 + random.random() * 0.05,
            }
            
            metrics.update(base_metrics)
            
        except Exception as e:
            logger.error(
                "Failed to collect metrics",
                service_id=service.id,
                error=str(e),
            )
        
        return metrics

    def _evaluate_health_status(self, metrics: Dict[str, float]) -> HealthCheckResult:
        """Evaluate health status based on metrics."""
        try:
            # Count threshold violations
            violations = 0
            critical_violations = 0
            
            for metric, value in metrics.items():
                threshold = self.health_thresholds.get(metric)
                if threshold is None:
                    continue
                
                # Different evaluation logic for different metrics
                if metric in ["response_time", "error_rate", "cpu_usage", "memory_usage"]:
                    if value > threshold:
                        violations += 1
                        if value > threshold * 1.5:  # Critical threshold
                            critical_violations += 1
                elif metric == "availability":
                    if value < threshold:
                        violations += 1
                        if value < threshold * 0.9:  # Critical threshold
                            critical_violations += 1
            
            # Determine status based on violations
            if critical_violations > 0:
                return HealthCheckResult.CRITICAL
            elif violations >= 3:
                return HealthCheckResult.UNHEALTHY
            elif violations >= 1:
                return HealthCheckResult.DEGRADED
            else:
                return HealthCheckResult.HEALTHY

        except Exception as e:
            logger.error("Failed to evaluate health status", error=str(e))
            return HealthCheckResult.UNKNOWN

    # Data persistence

    async def _store_health_event(self, event: HealthEvent) -> None:
        """Store health event in memory and Redis."""
        try:
            # Store in memory
            history = self.health_history.setdefault(event.service_id, [])
            history.append(event)
            
            # Limit history size
            if len(history) > self.max_history_per_service:
                history[:] = history[-self.max_history_per_service:]
            
            # Store in Redis
            if self.redis_client:
                key = f"health_event:{event.service_id}:{event.timestamp.isoformat()}"
                value = json.dumps(event.to_dict())
                await self.redis_client.setex(key, 86400 * 7, value)  # 7 days TTL
                
        except Exception as e:
            logger.error("Failed to store health event", error=str(e))

    async def _load_health_history(self) -> None:
        """Load health history from Redis."""
        try:
            if not self.redis_client:
                return
            
            # Scan for health event keys
            async for key in self.redis_client.scan_iter("health_event:*"):
                try:
                    value = await self.redis_client.get(key)
                    if value:
                        data = json.loads(value)
                        event = HealthEvent.from_dict(data)
                        
                        history = self.health_history.setdefault(event.service_id, [])
                        history.append(event)
                        
                except Exception as e:
                    logger.warning(
                        "Failed to load health event",
                        key=key,
                        error=str(e),
                    )
            
            # Sort all histories by timestamp
            for service_id, history in self.health_history.items():
                history.sort(key=lambda x: x.timestamp)
                
        except Exception as e:
            logger.error("Failed to load health history", error=str(e))