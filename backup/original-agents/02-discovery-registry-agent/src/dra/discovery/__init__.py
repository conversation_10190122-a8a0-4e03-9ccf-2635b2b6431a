"""
Discovery components for the Discovery Registry Agent.

This module provides intelligent service discovery capabilities including
service catalog, health monitoring, and registry management.
"""

from dra.discovery.service_catalog import IntelligentServiceCatalog, CatalogBackend
from dra.discovery.health_monitor import (
    PredictiveHealthMonitor,
    HealthCheckResult,
    HealthEvent,
)
from dra.discovery.registry_manager import RegistryManager

__all__ = [
    "IntelligentServiceCatalog",
    "CatalogBackend",
    "PredictiveHealthMonitor",
    "HealthCheckResult",
    "HealthEvent",
    "RegistryManager",
]