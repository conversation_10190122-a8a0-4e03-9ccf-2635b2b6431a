"""
Metrics collection and monitoring for the Discovery Registry Agent.

This module provides comprehensive metrics collection for service discovery
operations, health monitoring, and AI performance analytics.
"""

import asyncio
import time
from typing import Any, Dict, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, field

import structlog
from prometheus_client import Counter, Histogram, Gauge, start_http_server


logger = structlog.get_logger(__name__)


@dataclass
class ServiceMetrics:
    """Metrics for a specific service."""
    service_id: str
    namespace: str
    registration_time: datetime
    last_health_check: Optional[datetime] = None
    health_check_count: int = 0
    discovery_requests: int = 0
    capability_matches: int = 0
    response_times: List[float] = field(default_factory=list)
    error_count: int = 0
    availability_score: float = 1.0


@dataclass
class AIMetrics:
    """Metrics for AI operations."""
    health_predictions_made: int = 0
    health_predictions_accurate: int = 0
    capability_matches_performed: int = 0
    optimization_recommendations_generated: int = 0
    ai_response_times: List[float] = field(default_factory=list)
    ai_errors: int = 0
    fallback_activations: int = 0


class MetricsCollector:
    """
    Comprehensive metrics collector for the Discovery Registry Agent.
    """

    def __init__(self, enable_prometheus: bool = True, prometheus_port: int = 8001) -> None:
        self.enable_prometheus = enable_prometheus
        self.prometheus_port = prometheus_port
        
        # Service metrics
        self.service_metrics: Dict[str, ServiceMetrics] = {}
        self.ai_metrics = AIMetrics()
        
        # System metrics
        self.start_time = datetime.utcnow()
        self.total_requests = 0
        self.active_connections = 0
        
        # Prometheus metrics
        if self.enable_prometheus:
            self._setup_prometheus_metrics()
        
        # Background tasks
        self.collection_task: Optional[asyncio.Task] = None
        self.running = False

    def _setup_prometheus_metrics(self) -> None:
        """Setup Prometheus metrics."""
        # Service discovery metrics
        self.service_registrations = Counter(
            'dra_service_registrations_total',
            'Total number of service registrations'
        )
        
        self.service_deregistrations = Counter(
            'dra_service_deregistrations_total',
            'Total number of service deregistrations'
        )
        
        self.discovery_requests = Counter(
            'dra_discovery_requests_total',
            'Total number of service discovery requests',
            ['namespace', 'result']
        )
        
        self.discovery_response_time = Histogram(
            'dra_discovery_response_seconds',
            'Service discovery response time in seconds'
        )
        
        # Health monitoring metrics
        self.health_checks = Counter(
            'dra_health_checks_total',
            'Total number of health checks performed',
            ['service_id', 'status']
        )
        
        self.health_predictions = Counter(
            'dra_health_predictions_total',
            'Total number of health predictions made',
            ['prediction_type', 'risk_level']
        )
        
        # AI metrics
        self.ai_requests = Counter(
            'dra_ai_requests_total',
            'Total number of AI requests',
            ['provider', 'operation', 'status']
        )
        
        self.ai_response_time = Histogram(
            'dra_ai_response_seconds',
            'AI response time in seconds',
            ['provider', 'operation']
        )
        
        # System metrics
        self.active_services = Gauge(
            'dra_active_services',
            'Number of currently registered services',
            ['namespace']
        )
        
        self.system_uptime = Gauge(
            'dra_uptime_seconds',
            'System uptime in seconds'
        )

    async def initialize(self) -> None:
        """Initialize the metrics collector."""
        logger.info("Initializing metrics collector")
        
        try:
            if self.enable_prometheus:
                # Start Prometheus HTTP server
                start_http_server(self.prometheus_port)
                logger.info(f"Prometheus metrics server started on port {self.prometheus_port}")
            
            self.running = True
            self.collection_task = asyncio.create_task(self._collection_loop())
            
            logger.info("Metrics collector initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize metrics collector", error=str(e))
            raise

    async def shutdown(self) -> None:
        """Shutdown the metrics collector."""
        logger.info("Shutting down metrics collector")
        
        self.running = False
        
        if self.collection_task:
            self.collection_task.cancel()
            try:
                await self.collection_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Metrics collector shutdown complete")

    # Service metrics methods

    def record_service_registration(self, service_id: str, namespace: str) -> None:
        """Record a service registration."""
        try:
            self.service_metrics[service_id] = ServiceMetrics(
                service_id=service_id,
                namespace=namespace,
                registration_time=datetime.utcnow(),
            )
            
            if self.enable_prometheus:
                self.service_registrations.inc()
                self.active_services.labels(namespace=namespace).inc()
            
            logger.debug("Service registration recorded", service_id=service_id)
            
        except Exception as e:
            logger.error("Failed to record service registration", error=str(e))

    def record_service_deregistration(self, service_id: str) -> None:
        """Record a service deregistration."""
        try:
            service_metrics = self.service_metrics.pop(service_id, None)
            
            if service_metrics and self.enable_prometheus:
                self.service_deregistrations.inc()
                self.active_services.labels(namespace=service_metrics.namespace).dec()
            
            logger.debug("Service deregistration recorded", service_id=service_id)
            
        except Exception as e:
            logger.error("Failed to record service deregistration", error=str(e))

    def record_discovery_request(
        self, 
        namespace: str, 
        response_time: float, 
        matches_found: int,
        success: bool = True
    ) -> None:
        """Record a service discovery request."""
        try:
            self.total_requests += 1
            
            if self.enable_prometheus:
                result = "success" if success else "error"
                self.discovery_requests.labels(namespace=namespace, result=result).inc()
                self.discovery_response_time.observe(response_time)
            
            logger.debug(
                "Discovery request recorded",
                namespace=namespace,
                response_time=response_time,
                matches_found=matches_found,
            )
            
        except Exception as e:
            logger.error("Failed to record discovery request", error=str(e))

    def record_health_check(self, service_id: str, status: str, response_time: float) -> None:
        """Record a health check."""
        try:
            service_metrics = self.service_metrics.get(service_id)
            if service_metrics:
                service_metrics.last_health_check = datetime.utcnow()
                service_metrics.health_check_count += 1
                service_metrics.response_times.append(response_time)
                
                # Keep only last 100 response times
                if len(service_metrics.response_times) > 100:
                    service_metrics.response_times = service_metrics.response_times[-100:]
            
            if self.enable_prometheus:
                self.health_checks.labels(service_id=service_id, status=status).inc()
            
            logger.debug(
                "Health check recorded",
                service_id=service_id,
                status=status,
                response_time=response_time,
            )
            
        except Exception as e:
            logger.error("Failed to record health check", error=str(e))

    def record_capability_match(self, service_id: str, confidence: float) -> None:
        """Record a capability matching operation."""
        try:
            service_metrics = self.service_metrics.get(service_id)
            if service_metrics:
                service_metrics.capability_matches += 1
            
            self.ai_metrics.capability_matches_performed += 1
            
            logger.debug(
                "Capability match recorded",
                service_id=service_id,
                confidence=confidence,
            )
            
        except Exception as e:
            logger.error("Failed to record capability match", error=str(e))

    # AI metrics methods

    def record_health_prediction(
        self, 
        service_id: str, 
        prediction_type: str, 
        risk_level: str,
        response_time: float,
        success: bool = True
    ) -> None:
        """Record a health prediction."""
        try:
            if success:
                self.ai_metrics.health_predictions_made += 1
            else:
                self.ai_metrics.ai_errors += 1
            
            self.ai_metrics.ai_response_times.append(response_time)
            
            if self.enable_prometheus:
                self.health_predictions.labels(
                    prediction_type=prediction_type,
                    risk_level=risk_level
                ).inc()
                self.ai_response_time.labels(
                    provider="health_predictor",
                    operation="prediction"
                ).observe(response_time)
            
            logger.debug(
                "Health prediction recorded",
                service_id=service_id,
                prediction_type=prediction_type,
                risk_level=risk_level,
            )
            
        except Exception as e:
            logger.error("Failed to record health prediction", error=str(e))

    def record_ai_request(
        self, 
        provider: str, 
        operation: str, 
        response_time: float,
        success: bool = True
    ) -> None:
        """Record an AI API request."""
        try:
            if not success:
                self.ai_metrics.ai_errors += 1
            
            self.ai_metrics.ai_response_times.append(response_time)
            
            if self.enable_prometheus:
                status = "success" if success else "error"
                self.ai_requests.labels(
                    provider=provider,
                    operation=operation,
                    status=status
                ).inc()
                self.ai_response_time.labels(
                    provider=provider,
                    operation=operation
                ).observe(response_time)
            
            logger.debug(
                "AI request recorded",
                provider=provider,
                operation=operation,
                response_time=response_time,
                success=success,
            )
            
        except Exception as e:
            logger.error("Failed to record AI request", error=str(e))

    def record_optimization_recommendation(self, category: str, impact_score: float) -> None:
        """Record an optimization recommendation."""
        try:
            self.ai_metrics.optimization_recommendations_generated += 1
            
            logger.debug(
                "Optimization recommendation recorded",
                category=category,
                impact_score=impact_score,
            )
            
        except Exception as e:
            logger.error("Failed to record optimization recommendation", error=str(e))

    def record_fallback_activation(self, component: str, reason: str) -> None:
        """Record a fallback activation."""
        try:
            self.ai_metrics.fallback_activations += 1
            
            logger.warning(
                "Fallback activation recorded",
                component=component,
                reason=reason,
            )
            
        except Exception as e:
            logger.error("Failed to record fallback activation", error=str(e))

    # Analytics methods

    def get_service_statistics(self) -> Dict[str, Any]:
        """Get service-level statistics."""
        try:
            total_services = len(self.service_metrics)
            
            if total_services == 0:
                return {
                    "total_services": 0,
                    "namespaces": {},
                    "average_health_checks": 0,
                    "average_response_time": 0,
                }
            
            # Calculate namespace distribution
            namespace_counts = {}
            total_health_checks = 0
            total_response_times = []
            
            for service_metrics in self.service_metrics.values():
                namespace = service_metrics.namespace
                namespace_counts[namespace] = namespace_counts.get(namespace, 0) + 1
                total_health_checks += service_metrics.health_check_count
                total_response_times.extend(service_metrics.response_times)
            
            avg_health_checks = total_health_checks / total_services
            avg_response_time = (
                sum(total_response_times) / len(total_response_times)
                if total_response_times else 0
            )
            
            return {
                "total_services": total_services,
                "namespaces": namespace_counts,
                "average_health_checks": avg_health_checks,
                "average_response_time": avg_response_time,
                "total_discovery_requests": self.total_requests,
            }
            
        except Exception as e:
            logger.error("Failed to get service statistics", error=str(e))
            return {}

    def get_ai_statistics(self) -> Dict[str, Any]:
        """Get AI performance statistics."""
        try:
            total_ai_requests = len(self.ai_metrics.ai_response_times)
            avg_ai_response_time = (
                sum(self.ai_metrics.ai_response_times) / total_ai_requests
                if total_ai_requests > 0 else 0
            )
            
            ai_success_rate = (
                1.0 - (self.ai_metrics.ai_errors / max(total_ai_requests, 1))
            )
            
            return {
                "health_predictions_made": self.ai_metrics.health_predictions_made,
                "capability_matches_performed": self.ai_metrics.capability_matches_performed,
                "optimization_recommendations": self.ai_metrics.optimization_recommendations_generated,
                "average_ai_response_time": avg_ai_response_time,
                "ai_success_rate": ai_success_rate,
                "fallback_activations": self.ai_metrics.fallback_activations,
                "total_ai_errors": self.ai_metrics.ai_errors,
            }
            
        except Exception as e:
            logger.error("Failed to get AI statistics", error=str(e))
            return {}

    def get_system_statistics(self) -> Dict[str, Any]:
        """Get system-level statistics."""
        try:
            uptime = (datetime.utcnow() - self.start_time).total_seconds()
            
            return {
                "uptime_seconds": uptime,
                "start_time": self.start_time.isoformat(),
                "total_requests": self.total_requests,
                "active_connections": self.active_connections,
                "prometheus_enabled": self.enable_prometheus,
                "prometheus_port": self.prometheus_port if self.enable_prometheus else None,
            }
            
        except Exception as e:
            logger.error("Failed to get system statistics", error=str(e))
            return {}

    async def _collection_loop(self) -> None:
        """Background metrics collection loop."""
        logger.info("Starting metrics collection loop")
        
        while self.running:
            try:
                # Update system metrics
                if self.enable_prometheus:
                    uptime = (datetime.utcnow() - self.start_time).total_seconds()
                    self.system_uptime.set(uptime)
                
                # Update namespace service counts
                namespace_counts = {}
                for service_metrics in self.service_metrics.values():
                    namespace = service_metrics.namespace
                    namespace_counts[namespace] = namespace_counts.get(namespace, 0) + 1
                
                if self.enable_prometheus:
                    # Update active services gauge for each namespace
                    for namespace, count in namespace_counts.items():
                        self.active_services.labels(namespace=namespace).set(count)
                
                # Clean up old response time data
                current_time = datetime.utcnow()
                for service_metrics in self.service_metrics.values():
                    # Keep only response times from last hour
                    if len(service_metrics.response_times) > 360:  # 1 per 10 seconds for 1 hour
                        service_metrics.response_times = service_metrics.response_times[-360:]
                
                # Clean up old AI response times
                if len(self.ai_metrics.ai_response_times) > 1000:
                    self.ai_metrics.ai_response_times = self.ai_metrics.ai_response_times[-1000:]
                
                await asyncio.sleep(10)  # Collect every 10 seconds
                
            except Exception as e:
                logger.error("Error in metrics collection loop", error=str(e))
                await asyncio.sleep(10)