package ai.twodot.platform.agents.factory;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Agent Factory Agent Application
 * 
 * Main application class for the Agent Factory Agent (AFA-008).
 * Provides AI-powered agent generation factory that creates complete, 
 * production-ready intelligent agents from natural language requirements.
 * 
 * Key Features:
 * - AI-powered requirement analysis and architecture design
 * - Multi-language code generation (Python, Java, TypeScript, Go)
 * - Intelligent template selection and customization
 * - Automated testing and quality assurance
 * - CI/CD pipeline generation and deployment automation
 * - Real-time code optimization and security validation
 * - Self-improving generation through machine learning
 * 
 * Architecture:
 * - Java 21 with Spring Boot for enterprise generation coordination
 * - Python AI service for code generation and optimization
 * - Integration with multiple AI models (GPT-4, Claude, <PERSON>, Copilot)
 * - A2A communication with all platform agents
 * 
 * <AUTHOR> Platform Team
 * @version 1.0.0
 * @since 2025-01-14
 */
@SpringBootApplication
@EnableAsync
@EnableScheduling
@EnableTransactionManagement
@ComponentScan(basePackages = "ai.twodot.platform.agents.factory")
public class AgentFactoryAgentApplication {

    /**
     * Main application entry point.
     * 
     * Configures the Agent Factory Agent to run on port 8090,
     * following the platform port allocation pattern:
     * - CBA-001: 8081
     * - DRA-002: 8082  
     * - SMA-003: 8083
     * - RMA-004: 8084
     * - DPA-005: 8085
     * - KBA-006: 8086
     * - TOA-007: 8088 (8087 reserved for KBA Python service)
     * - AFA-008: 8090 (8089 reserved for TOA Python service)
     * 
     * @param args Command line arguments
     */
    public static void main(String[] args) {
        // Configure application properties
        System.setProperty("spring.application.name", "agent-factory-agent");
        // System.setProperty("server.port", "8090"); // Use application.properties instead
        
        // Launch the Spring Boot application
        SpringApplication.run(AgentFactoryAgentApplication.class, args);
    }
}