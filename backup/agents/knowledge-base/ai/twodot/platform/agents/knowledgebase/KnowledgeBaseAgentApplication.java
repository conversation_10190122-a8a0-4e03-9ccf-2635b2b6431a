package com.multiagent.platform.agents.knowledge.ai.twodot.platform.agents.knowledgebase;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Knowledge Base Agent (KBA-006) - Main Application
 * 
 * AI-powered knowledge management and intelligent information processing agent.
 * 
 * Features:
 * - AI-powered knowledge extraction from multiple sources
 * - Semantic processing and understanding with vector embeddings
 * - Real-time pattern recognition and learning
 * - Intelligent recommendation engine
 * - Cross-agent knowledge sharing and collaboration
 * - Automated knowledge graph construction
 * - Context-aware information retrieval
 * 
 * Architecture:
 * - Java 21 + Spring Boot 3.2 for enterprise services
 * - Python FastAPI for AI/ML processing
 * - Vector databases for semantic search
 * - Graph databases for relationship mapping
 * - Full-text search with Elasticsearch
 * 
 * Dependencies:
 * - Communication Broker Agent (CBA-001)
 * - Discovery Registry Agent (DRA-002)
 * - Security Monitor Agent (SMA-003)
 * - Resource Manager Agent (RMA-004)
 * - Data Processing Agent (DPA-005)
 */
@SpringBootApplication
@EnableAsync
@EnableScheduling
@EnableTransactionManagement
public class KnowledgeBaseAgentApplication {
    
    public static void main(String[] args) {
        System.setProperty("spring.application.name", "knowledge-base-agent");
        // System.setProperty("server.port", "8085"); // Use application.properties instead
        
        SpringApplication.run(KnowledgeBaseAgentApplication.class, args);
    }
}