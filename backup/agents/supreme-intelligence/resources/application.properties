# Supreme Platform Intelligence Agent Configuration
server.port=8088
spring.application.name=supreme-platform-intelligence-agent

# PostgreSQL Database Configuration
spring.datasource.url=********************************************************
spring.datasource.driverClassName=org.postgresql.Driver
spring.datasource.username=${DB_USERNAME:koneti}
spring.datasource.password=${DB_PASSWORD:}
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=true

# Redis Configuration
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.timeout=2000ms

# Management and Actuator
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=always
management.metrics.export.prometheus.enabled=true

# Supreme Intelligence Specific
supreme.intelligence.strategic.planning.enabled=true
supreme.intelligence.crisis.management.enabled=true
supreme.intelligence.platform.optimization.enabled=true
supreme.intelligence.consciousness.enabled=true
supreme.intelligence.ai.enabled=true
supreme.intelligence.ai.service.url=http://localhost:9092

# Intelligence Configuration
intelligence.model.ensemble.enabled=true
intelligence.model.providers=openai,anthropic,google
intelligence.reasoning.depth=deep
intelligence.consciousness.monitoring.enabled=true

# Logging
logging.level.ai.twodot.platform.agents.spia=INFO
logging.level.org.springframework.web=INFO
logging.level.root=INFO