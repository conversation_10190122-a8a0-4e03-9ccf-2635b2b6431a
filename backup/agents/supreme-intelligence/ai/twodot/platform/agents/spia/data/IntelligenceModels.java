package com.multiagent.platform.agents.intelligence.ai.twodot.platform.agents.spia.data;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.Instant;
import java.util.List;
import java.util.Map;

/**
 * Supreme Platform Intelligence Agent - Data Models.
 *
 * <p>Core data structures for supreme intelligence, strategic planning, platform consciousness,
 * crisis management, and global optimization.
 *
 * <AUTHOR> Platform Team
 * @version 1.0.0
 * @since 2025-01-14
 */
public class IntelligenceModels {

    // ===== Core Intelligence Models =====

    /** The level of intelligence of the agent. */
    public enum IntelligenceLevel {
        BASIC,
        INTERMEDIATE,
        ADVANCED,
        EXPERT,
        SUPREME
    }

    /** The state of consciousness of the agent. */
    public enum ConsciousnessState {
        DORMANT,
        AWAKENING,
        AWARE,
        SELF_AWARE,
        TRANSCENDENT
    }

    /** The level of a crisis. */
    public enum CrisisLevel {
        LOW,
        MEDIUM,
        HIGH,
        CRITICAL,
        CATASTROPHIC
    }

    /** The scope of an optimization. */
    public enum OptimizationScope {
        AGENT,
        SERVICE,
        PLATFORM,
        ECOSYSTEM,
        G<PERSON><PERSON><PERSON>L
    }

    /** The type of a decision. */
    public enum DecisionType {
        TACTICAL,
        OPERATIONAL,
        STRATEGIC,
        VISIONARY
    }

    /** The type of intelligence. */
    public enum IntelligenceType {
        ANALYTICAL,
        CREATIVE,
        EMOTIONAL,
        STRATEGIC,
        OPERATIONAL
    }

    /** The type of a crisis. */
    public enum CrisisType {
        TECHNICAL,
        OPERATIONAL,
        SECURITY,
        BUSINESS,
        REGULATORY
    }

    /** The scope of a crisis. */
    public enum CrisisScope {
        AGENT,
        SERVICE,
        PLATFORM,
        ENTERPRISE,
        GLOBAL
    }

    /** The status of a crisis. */
    public enum CrisisStatus {
        DETECTED,
        ANALYZING,
        RESPONDING,
        RESOLVING,
        RESOLVED
    }

    /** The type of a response. */
    public enum ResponseType {
        AUTOMATED,
        MANUAL,
        HYBRID
    }

    /** The priority of a response. */
    public enum ResponsePriority {
        LOW,
        MEDIUM,
        HIGH,
        CRITICAL,
        EMERGENCY
    }

    /** The type of an optimization. */
    public enum OptimizationType {
        PERFORMANCE,
        RESOURCE,
        COST,
        QUALITY,
        EFFICIENCY
    }

    /** The status of an optimization. */
    public enum OptimizationStatus {
        PLANNED,
        ACTIVE,
        PAUSED,
        COMPLETED,
        CANCELLED
    }

    /** The category of a decision. */
    public enum DecisionCategory {
        STRATEGIC,
        OPERATIONAL,
        TACTICAL,
        EMERGENCY
    }

    /** The status of a decision. */
    public enum DecisionStatus {
        PENDING,
        APPROVED,
        REJECTED,
        DEFERRED,
        IMPLEMENTED
    }

    /** The type of an emergence. */
    public enum EmergenceType {
        CAPABILITY,
        BEHAVIOR,
        PATTERN,
        INTELLIGENCE
    }

    /** The category of an emergence. */
    public enum EmergenceCategory {
        TECHNICAL,
        COGNITIVE,
        SOCIAL,
        SYSTEMIC
    }

    /** The type of a coordination. */
    public enum CoordinationType {
        HIERARCHICAL,
        PEER_TO_PEER,
        HYBRID,
        SWARM
    }

    /** The status of a coordination. */
    public enum CoordinationStatus {
        ACTIVE,
        INACTIVE,
        OPTIMIZING,
        FAILED
    }

    /** The type of a cognitive capability. */
    public enum CognitiveType {
        REASONING,
        LEARNING,
        PERCEPTION,
        MEMORY,
        CREATIVITY
    }

    /** The priority of a plan. */
    public enum PlanPriority {
        LOW,
        MEDIUM,
        HIGH,
        CRITICAL,
        STRATEGIC
    }

    /** The status of an approval. */
    public enum ApprovalStatus {
        DRAFT,
        PENDING,
        APPROVED,
        REJECTED,
        REVISION_REQUIRED
    }

    /** The status of an execution. */
    public enum ExecutionStatus {
        PLANNED,
        EXECUTING,
        COMPLETED,
        FAILED,
        CANCELLED
    }

    /** The phase of an optimization. */
    public enum OptimizationPhase {
        PLANNING,
        EXECUTING,
        MONITORING,
        EVALUATING,
        COMPLETED
    }

    // ===== Strategic Planning Models =====

    /**
     * A strategic plan.
     *
     * @param planId The ID of the plan.
     * @param title The title of the plan.
     * @param description The description of the plan.
     * @param objectives The objectives of the plan.
     * @param priority The priority of the plan.
     * @param confidenceScore The confidence score of the plan.
     * @param approvalStatus The approval status of the plan.
     * @param createdBy The creator of the plan.
     * @param createdAt The creation timestamp of the plan.
     * @param updatedAt The last update timestamp of the plan.
     */
    public record StrategicPlan(
            @JsonProperty("plan_id") String planId,
            @JsonProperty("title") String title,
            @JsonProperty("description") String description,
            @JsonProperty("objectives") List<String> objectives,
            @JsonProperty("priority") PlanPriority priority,
            @JsonProperty("confidence_score") double confidenceScore,
            @JsonProperty("approval_status") ApprovalStatus approvalStatus,
            @JsonProperty("created_by") String createdBy,
            @JsonProperty("created_at") Instant createdAt,
            @JsonProperty("updated_at") Instant updatedAt) {}

    /**
     * A strategic objective.
     *
     * @param objectiveId The ID of the objective.
     * @param title The title of the objective.
     * @param description The description of the objective.
     * @param targetValue The target value of the objective.
     * @param currentValue The current value of the objective.
     * @param unit The unit of the objective.
     * @param progressPercentage The progress percentage of the objective.
     * @param createdAt The creation timestamp of the objective.
     * @param updatedAt The last update timestamp of the objective.
     */
    public record StrategicObjective(
            @JsonProperty("objective_id") String objectiveId,
            @JsonProperty("title") String title,
            @JsonProperty("description") String description,
            @JsonProperty("target_value") double targetValue,
            @JsonProperty("current_value") double currentValue,
            @JsonProperty("unit") String unit,
            @JsonProperty("progress_percentage") double progressPercentage,
            @JsonProperty("created_at") Instant createdAt,
            @JsonProperty("updated_at") Instant updatedAt) {}

    // ===== Platform Consciousness Models =====

    /**
     * The platform consciousness.
     *
     * @param consciousnessId The ID of the consciousness.
     * @param state The state of the consciousness.
     * @param awarenessLevel The awareness level of the consciousness.
     * @param selfAwarenessScore The self-awareness score of the consciousness.
     * @param cognitiveCapabilities The cognitive capabilities of the consciousness.
     * @param createdAt The creation timestamp of the consciousness.
     * @param updatedAt The last update timestamp of the consciousness.
     */
    public record PlatformConsciousness(
            @JsonProperty("consciousness_id") String consciousnessId,
            @JsonProperty("state") ConsciousnessState state,
            @JsonProperty("awareness_level") double awarenessLevel,
            @JsonProperty("self_awareness_score") double selfAwarenessScore,
            @JsonProperty("cognitive_capabilities") List<CognitiveCapability> cognitiveCapabilities,
            @JsonProperty("created_at") Instant createdAt,
            @JsonProperty("updated_at") Instant updatedAt) {}

    /**
     * A cognitive capability.
     *
     * @param capabilityId The ID of the capability.
     * @param name The name of the capability.
     * @param type The type of the capability.
     * @param description The description of the capability.
     * @param proficiencyLevel The proficiency level of the capability.
     * @param processingSpeed The processing speed of the capability.
     * @param accuracyRate The accuracy rate of the capability.
     * @param learningRate The learning rate of the capability.
     * @param memoryCapacity The memory capacity of the capability.
     * @param createdAt The creation timestamp of the capability.
     * @param updatedAt The last update timestamp of the capability.
     */
    public record CognitiveCapability(
            @JsonProperty("capability_id") String capabilityId,
            @JsonProperty("name") String name,
            @JsonProperty("type") CognitiveType type,
            @JsonProperty("description") String description,
            @JsonProperty("proficiency_level") double proficiencyLevel,
            @JsonProperty("processing_speed") double processingSpeed,
            @JsonProperty("accuracy_rate") double accuracyRate,
            @JsonProperty("learning_rate") double learningRate,
            @JsonProperty("memory_capacity") long memoryCapacity,
            @JsonProperty("created_at") Instant createdAt,
            @JsonProperty("updated_at") Instant updatedAt) {}

    // ===== Crisis Management Models =====

    /**
     * A crisis event.
     *
     * @param crisisId The ID of the crisis.
     * @param title The title of the crisis.
     * @param description The description of the crisis.
     * @param type The type of the crisis.
     * @param level The level of the crisis.
     * @param scope The scope of the crisis.
     * @param affectedSystems The affected systems of the crisis.
     * @param detectionTime The detection time of the crisis.
     * @param responseTime The response time of the crisis.
     * @param status The status of the crisis.
     * @param createdAt The creation timestamp of the crisis.
     * @param updatedAt The last update timestamp of the crisis.
     */
    public record CrisisEvent(
            @JsonProperty("crisis_id") String crisisId,
            @JsonProperty("title") String title,
            @JsonProperty("description") String description,
            @JsonProperty("type") CrisisType type,
            @JsonProperty("level") CrisisLevel level,
            @JsonProperty("scope") CrisisScope scope,
            @JsonProperty("affected_systems") List<String> affectedSystems,
            @JsonProperty("detection_time") Instant detectionTime,
            @JsonProperty("response_time") Instant responseTime,
            @JsonProperty("status") CrisisStatus status,
            @JsonProperty("created_at") Instant createdAt,
            @JsonProperty("updated_at") Instant updatedAt) {}

    /**
     * A crisis response.
     *
     * @param responseId The ID of the response.
     * @param crisisId The ID of the crisis.
     * @param responseType The type of the response.
     * @param priority The priority of the response.
     * @param decisionRationale The decision rationale of the response.
     * @param effectivenessScore The effectiveness score of the response.
     * @param executionStatus The execution status of the response.
     * @param createdAt The creation timestamp of the response.
     * @param updatedAt The last update timestamp of the response.
     */
    public record CrisisResponse(
            @JsonProperty("response_id") String responseId,
            @JsonProperty("crisis_id") String crisisId,
            @JsonProperty("response_type") ResponseType responseType,
            @JsonProperty("priority") ResponsePriority priority,
            @JsonProperty("decision_rationale") String decisionRationale,
            @JsonProperty("effectiveness_score") double effectivenessScore,
            @JsonProperty("execution_status") ExecutionStatus executionStatus,
            @JsonProperty("created_at") Instant createdAt,
            @JsonProperty("updated_at") Instant updatedAt) {}

    // ===== Global Optimization Models =====

    /**
     * An optimization strategy.
     *
     * @param strategyId The ID of the strategy.
     * @param name The name of the strategy.
     * @param description The description of the strategy.
     * @param type The type of the strategy.
     * @param scope The scope of the strategy.
     * @param parameters The parameters of the strategy.
     * @param createdAt The creation timestamp of the strategy.
     * @param updatedAt The last update timestamp of the strategy.
     */
    public record OptimizationStrategy(
            @JsonProperty("strategy_id") String strategyId,
            @JsonProperty("name") String name,
            @JsonProperty("description") String description,
            @JsonProperty("type") OptimizationType type,
            @JsonProperty("scope") OptimizationScope scope,
            @JsonProperty("parameters") Map<String, Object> parameters,
            @JsonProperty("created_at") Instant createdAt,
            @JsonProperty("updated_at") Instant updatedAt) {}

    /**
     * An optimization execution.
     *
     * @param executionId The ID of the execution.
     * @param strategyId The ID of the strategy.
     * @param phase The phase of the execution.
     * @param status The status of the execution.
     * @param progressPercentage The progress percentage of the execution.
     * @param currentMetrics The current metrics of the execution.
     * @param improvementMetrics The improvement metrics of the execution.
     * @param estimatedCompletion The estimated completion of the execution.
     * @param createdAt The creation timestamp of the execution.
     * @param updatedAt The last update timestamp of the execution.
     */
    public record OptimizationExecution(
            @JsonProperty("execution_id") String executionId,
            @JsonProperty("strategy_id") String strategyId,
            @JsonProperty("phase") OptimizationPhase phase,
            @JsonProperty("status") OptimizationStatus status,
            @JsonProperty("progress_percentage") double progressPercentage,
            @JsonProperty("current_metrics") Map<String, Double> currentMetrics,
            @JsonProperty("improvement_metrics") Map<String, Double> improvementMetrics,
            @JsonProperty("estimated_completion") Instant estimatedCompletion,
            @JsonProperty("created_at") Instant createdAt,
            @JsonProperty("updated_at") Instant updatedAt) {}

    // ===== Decision Intelligence Models =====

    /**
     * An intelligent decision.
     *
     * @param decisionId The ID of the decision.
     * @param title The title of the decision.
     * @param description The description of the decision.
     * @param type The type of the decision.
     * @param category The category of the decision.
     * @param rationale The rationale of the decision.
     * @param confidenceScore The confidence score of the decision.
     * @param decisionStatus The status of the decision.
     * @param createdAt The creation timestamp of the decision.
     * @param updatedAt The last update timestamp of the decision.
     */
    public record IntelligentDecision(
            @JsonProperty("decision_id") String decisionId,
            @JsonProperty("title") String title,
            @JsonProperty("description") String description,
            @JsonProperty("type") DecisionType type,
            @JsonProperty("category") DecisionCategory category,
            @JsonProperty("rationale") String rationale,
            @JsonProperty("confidence_score") double confidenceScore,
            @JsonProperty("decision_status") DecisionStatus decisionStatus,
            @JsonProperty("created_at") Instant createdAt,
            @JsonProperty("updated_at") Instant updatedAt) {}

    /**
     * A decision alternative.
     *
     * @param alternativeId The ID of the alternative.
     * @param name The name of the alternative.
     * @param description The description of the alternative.
     * @param pros The pros of the alternative.
     * @param cons The cons of the alternative.
     * @param cost The cost of the alternative.
     * @param riskLevel The risk level of the alternative.
     * @param feasibilityScore The feasibility score of the alternative.
     */
    public record DecisionAlternative(
            @JsonProperty("alternative_id") String alternativeId,
            @JsonProperty("name") String name,
            @JsonProperty("description") String description,
            @JsonProperty("pros") List<String> pros,
            @JsonProperty("cons") List<String> cons,
            @JsonProperty("cost") double cost,
            @JsonProperty("risk_level") String riskLevel,
            @JsonProperty("feasibility_score") double feasibilityScore) {}

    // ===== Emergent Intelligence Models =====

    /**
     * An emergent intelligence.
     *
     * @param emergenceId The ID of the emergence.
     * @param name The name of the emergence.
     * @param description The description of the emergence.
     * @param type The type of the emergence.
     * @param category The category of the emergence.
     * @param readinessScore The readiness score of the emergence.
     * @param createdAt The creation timestamp of the emergence.
     * @param updatedAt The last update timestamp of the emergence.
     */
    public record EmergentIntelligence(
            @JsonProperty("emergence_id") String emergenceId,
            @JsonProperty("name") String name,
            @JsonProperty("description") String description,
            @JsonProperty("type") EmergenceType type,
            @JsonProperty("category") EmergenceCategory category,
            @JsonProperty("readiness_score") double readinessScore,
            @JsonProperty("created_at") Instant createdAt,
            @JsonProperty("updated_at") Instant updatedAt) {}

    /**
     * An emergence indicator.
     *
     * @param indicatorId The ID of the indicator.
     * @param name The name of the indicator.
     * @param description The description of the indicator.
     * @param currentValue The current value of the indicator.
     * @param threshold The threshold of the indicator.
     * @param trend The trend of the indicator.
     * @param significance The significance of the indicator.
     * @param detectionConfidence The detection confidence of the indicator.
     * @param createdAt The creation timestamp of the indicator.
     * @param updatedAt The last update timestamp of the indicator.
     */
    public record EmergenceIndicator(
            @JsonProperty("indicator_id") String indicatorId,
            @JsonProperty("name") String name,
            @JsonProperty("description") String description,
            @JsonProperty("current_value") double currentValue,
            @JsonProperty("threshold") double threshold,
            @JsonProperty("trend") String trend,
            @JsonProperty("significance") String significance,
            @JsonProperty("detection_confidence") double detectionConfidence,
            @JsonProperty("created_at") Instant createdAt,
            @JsonProperty("updated_at") Instant updatedAt) {}

    // ===== Multi-Agent Coordination Models =====

    /**
     * An agent coordination.
     *
     * @param coordinationId The ID of the coordination.
     * @param coordinationType The type of the coordination.
     * @param participants The participants of the coordination.
     * @param status The status of the coordination.
     * @param effectivenessScore The effectiveness score of the coordination.
     * @param createdAt The creation timestamp of the coordination.
     * @param updatedAt The last update timestamp of the coordination.
     */
    public record AgentCoordination(
            @JsonProperty("coordination_id") String coordinationId,
            @JsonProperty("coordination_type") CoordinationType coordinationType,
            @JsonProperty("participants") List<AgentParticipant> participants,
            @JsonProperty("status") CoordinationStatus status,
            @JsonProperty("effectiveness_score") double effectivenessScore,
            @JsonProperty("created_at") Instant createdAt,
            @JsonProperty("updated_at") Instant updatedAt) {}

    /**
     * An agent participant.
     *
     * @param participantId The ID of the participant.
     * @param agentId The ID of the agent.
     * @param agentType The type of the agent.
     * @param role The role of the agent.
     * @param capabilities The capabilities of the agent.
     * @param performanceMetrics The performance metrics of the agent.
     * @param trustScore The trust score of the agent.
     * @param reliabilityScore The reliability score of the agent.
     * @param joinedAt The joined timestamp of the agent.
     * @param updatedAt The last update timestamp of the agent.
     */
    public record AgentParticipant(
            @JsonProperty("participant_id") String participantId,
            @JsonProperty("agent_id") String agentId,
            @JsonProperty("agent_type") String agentType,
            @JsonProperty("role") String role,
            @JsonProperty("capabilities") List<String> capabilities,
            @JsonProperty("performance_metrics") Map<String, Double> performanceMetrics,
            @JsonProperty("trust_score") double trustScore,
            @JsonProperty("reliability_score") double reliabilityScore,
            @JsonProperty("joined_at") Instant joinedAt,
            @JsonProperty("updated_at") Instant updatedAt) {}

    // ===== Predictive Analytics Models =====

    /**
     * A predictive model.
     *
     * @param modelId The ID of the model.
     * @param name The name of the model.
     * @param description The description of the model.
     * @param type The type of the model.
     * @param accuracyScore The accuracy score of the model.
     * @param precisionScore The precision score of the model.
     * @param recallScore The recall score of the model.
     * @param f1Score The F1 score of the model.
     * @param version The version of the model.
     * @param createdAt The creation timestamp of the model.
     * @param updatedAt The last update timestamp of the model.
     */
    public record PredictiveModel(
            @JsonProperty("model_id") String modelId,
            @JsonProperty("name") String name,
            @JsonProperty("description") String description,
            @JsonProperty("type") String type,
            @JsonProperty("accuracy_score") double accuracyScore,
            @JsonProperty("precision_score") double precisionScore,
            @JsonProperty("recall_score") double recallScore,
            @JsonProperty("f1_score") double f1Score,
            @JsonProperty("version") String version,
            @JsonProperty("created_at") Instant createdAt,
            @JsonProperty("updated_at") Instant updatedAt) {}

    /**
     * A predictive analysis.
     *
     * @param analysisId The ID of the analysis.
     * @param modelId The ID of the model.
     * @param analysisType The type of the analysis.
     * @param inputData The input data of the analysis.
     * @param predictions The predictions of the analysis.
     * @param createdAt The creation timestamp of the analysis.
     * @param updatedAt The last update timestamp of the analysis.
     */
    public record PredictiveAnalysis(
            @JsonProperty("analysis_id") String analysisId,
            @JsonProperty("model_id") String modelId,
            @JsonProperty("analysis_type") String analysisType,
            @JsonProperty("input_data") Map<String, Object> inputData,
            @JsonProperty("predictions") List<Prediction> predictions,
            @JsonProperty("created_at") Instant createdAt,
            @JsonProperty("updated_at") Instant updatedAt) {}

    /**
     * A prediction.
     *
     * @param predictionId The ID of the prediction.
     * @param targetVariable The target variable of the prediction.
     * @param predictedValue The predicted value of the prediction.
     * @param confidenceScore The confidence score of the prediction.
     * @param predictionHorizon The prediction horizon of the prediction.
     * @param createdAt The creation timestamp of the prediction.
     */
    public record Prediction(
            @JsonProperty("prediction_id") String predictionId,
            @JsonProperty("target_variable") String targetVariable,
            @JsonProperty("predicted_value") double predictedValue,
            @JsonProperty("confidence_score") double confidenceScore,
            @JsonProperty("prediction_horizon") long predictionHorizon,
            @JsonProperty("created_at") Instant createdAt) {}

    // ===== Supporting Models =====

    /**
     * A response action.
     *
     * @param actionId The ID of the action.
     * @param type The type of the action.
     * @param description The description of the action.
     * @param priority The priority of the action.
     * @param estimatedDuration The estimated duration of the action.
     * @param requiredResources The required resources of the action.
     * @param executionStatus The execution status of the action.
     */
    public record ResponseAction(
            @JsonProperty("action_id") String actionId,
            @JsonProperty("type") String type,
            @JsonProperty("description") String description,
            @JsonProperty("priority") int priority,
            @JsonProperty("estimated_duration") long estimatedDuration,
            @JsonProperty("required_resources") List<String> requiredResources,
            @JsonProperty("execution_status") String executionStatus) {}

    /**
     * An optimization objective.
     *
     * @param objectiveId The ID of the objective.
     * @param description The description of the objective.
     * @param targetValue The target value of the objective.
     * @param currentValue The current value of the objective.
     * @param unit The unit of the objective.
     * @param priority The priority of the objective.
     * @param weight The weight of the objective.
     */
    public record OptimizationObjective(
            @JsonProperty("objective_id") String objectiveId,
            @JsonProperty("description") String description,
            @JsonProperty("target_value") double targetValue,
            @JsonProperty("current_value") double currentValue,
            @JsonProperty("unit") String unit,
            @JsonProperty("priority") int priority,
            @JsonProperty("weight") double weight) {}

    /**
     * A decision criterion.
     *
     * @param criterionId The ID of the criterion.
     * @param name The name of the criterion.
     * @param description The description of the criterion.
     * @param weight The weight of the criterion.
     * @param measurementScale The measurement scale of the criterion.
     * @param evaluationMethod The evaluation method of the criterion.
     */
    public record DecisionCriterion(
            @JsonProperty("criterion_id") String criterionId,
            @JsonProperty("name") String name,
            @JsonProperty("description") String description,
            @JsonProperty("weight") double weight,
            @JsonProperty("measurement_scale") String measurementScale,
            @JsonProperty("evaluation_method") String evaluationMethod) {}

    // ===== Metrics and Monitoring =====

    /**
     * The intelligence metrics.
     *
     * @param metricsId The ID of the metrics.
     * @param agentStatus The status of the agent.
     * @param intelligenceLevel The level of intelligence.
     * @param consciousnessState The state of consciousness.
     * @param totalDecisionsMade The total decisions made.
     * @param totalCrisesManaged The total crises managed.
     * @param totalOptimizationsExecuted The total optimizations executed.
     * @param totalStrategicPlansGenerated The total strategic plans generated.
     * @param totalEmergentIntelligenceDetected The total emergent intelligence detected.
     * @param activeStrategicPlans The active strategic plans.
     * @param activeCrises The active crises.
     * @param activeOptimizations The active optimizations.
     * @param activeCoordinations The active coordinations.
     * @param emergentIntelligencePatterns The emergent intelligence patterns.
     * @param lastHealthCheck The last health check.
     * @param lastOptimizationCycle The last optimization cycle.
     * @param lastConsciousnessUpdate The last consciousness update.
     * @param timestamp The timestamp of the metrics.
     */
    public record IntelligenceMetrics(
            @JsonProperty("metrics_id") String metricsId,
            @JsonProperty("agent_status") String agentStatus,
            @JsonProperty("intelligence_level") IntelligenceLevel intelligenceLevel,
            @JsonProperty("consciousness_state") ConsciousnessState consciousnessState,
            @JsonProperty("total_decisions_made") long totalDecisionsMade,
            @JsonProperty("total_crises_managed") long totalCrisesManaged,
            @JsonProperty("total_optimizations_executed") long totalOptimizationsExecuted,
            @JsonProperty("total_strategic_plans_generated") long totalStrategicPlansGenerated,
            @JsonProperty("total_emergent_intelligence_detected")
                    long totalEmergentIntelligenceDetected,
            @JsonProperty("active_strategic_plans") int activeStrategicPlans,
            @JsonProperty("active_crises") int activeCrises,
            @JsonProperty("active_optimizations") int activeOptimizations,
            @JsonProperty("active_coordinations") int activeCoordinations,
            @JsonProperty("emergent_intelligence_patterns") int emergentIntelligencePatterns,
            @JsonProperty("last_health_check") long lastHealthCheck,
            @JsonProperty("last_optimization_cycle") long lastOptimizationCycle,
            @JsonProperty("last_consciousness_update") long lastConsciousnessUpdate,
            @JsonProperty("timestamp") Instant timestamp) {}

    /**
     * The platform state.
     *
     * @param stateId The ID of the state.
     * @param activeAgents The active agents.
     * @param systemPerformance The system performance.
     * @param resourceUtilization The resource utilization.
     * @param intelligenceLevel The level of intelligence.
     * @param consciousnessState The state of consciousness.
     * @param timestamp The timestamp of the state.
     */
    public record PlatformState(
            @JsonProperty("state_id") String stateId,
            @JsonProperty("active_agents") int activeAgents,
            @JsonProperty("system_performance") Map<String, Double> systemPerformance,
            @JsonProperty("resource_utilization") Map<String, Double> resourceUtilization,
            @JsonProperty("intelligence_level") IntelligenceLevel intelligenceLevel,
            @JsonProperty("consciousness_state") ConsciousnessState consciousnessState,
            @JsonProperty("timestamp") Instant timestamp) {}

    /**
     * The health status.
     *
     * @param component The component.
     * @param status The status.
     * @param lastCheck The last check.
     * @param details The details.
     */
    public record HealthStatus(
            @JsonProperty("component") String component,
            @JsonProperty("status") String status,
            @JsonProperty("last_check") Instant lastCheck,
            @JsonProperty("details") Map<String, Object> details) {}

    /**
     * The processing result.
     *
     * @param resultId The ID of the result.
     * @param processingTimeMs The processing time in milliseconds.
     * @param success Whether the processing was successful.
     * @param errorMessage The error message.
     * @param resultData The result data.
     * @param timestamp The timestamp of the result.
     */
    public record ProcessingResult(
            @JsonProperty("result_id") String resultId,
            @JsonProperty("processing_time_ms") long processingTimeMs,
            @JsonProperty("success") boolean success,
            @JsonProperty("error_message") String errorMessage,
            @JsonProperty("result_data") Map<String, Object> resultData,
            @JsonProperty("timestamp") Instant timestamp) {}
}
