package com.multiagent.platform.agents.resource.ai.twodot.platform.agents.resourcemanager.optimization;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Cost Optimization Service
 * 
 * Analyzes resource usage and provides cost optimization recommendations.
 */
@Service
public class CostOptimizationService {

    private static final Logger logger = LoggerFactory.getLogger(CostOptimizationService.class);

    private final AtomicLong optimizationRequests = new AtomicLong(0);
    private final AtomicLong totalSavingsIdentified = new AtomicLong(0);
    private final Map<String, OptimizationHistory> optimizationHistory = new ConcurrentHashMap<>();

    public CostOptimizationRecommendation optimizeCosts(AllocationRequest request, CapacityPrediction prediction) {
        logger.info("Optimizing costs for service: {}", request.serviceId());
        
        try {
            optimizationRequests.incrementAndGet();
            
            // Analyze current resource requirements vs predicted needs
            Map<String, Object> analysis = analyzeResourceEfficiency(request, prediction);
            
            // Calculate potential savings
            double potentialSavings = calculatePotentialSavings(request, analysis);
            totalSavingsIdentified.addAndGet((long) potentialSavings);
            
            // Generate recommendations
            Map<String, Object> recommendations = generateCostRecommendations(request, analysis, potentialSavings);
            
            // Store optimization history
            OptimizationHistory history = new OptimizationHistory(
                request.serviceId(),
                potentialSavings,
                recommendations,
                Instant.now()
            );
            optimizationHistory.put(request.serviceId(), history);
            
            logger.info("Cost optimization completed for service {}: ${} potential savings", 
                request.serviceId(), potentialSavings);
            
            return new CostOptimizationRecommendation(request.serviceId(), recommendations);
            
        } catch (Exception e) {
            logger.error("Error optimizing costs for service {}: {}", request.serviceId(), e.getMessage(), e);
            throw new RuntimeException("Cost optimization failed: " + e.getMessage());
        }
    }

    public Map<String, Object> getCostOptimizationMetrics() {
        return Map.of(
            "optimization_requests", optimizationRequests.get(),
            "total_savings_identified", totalSavingsIdentified.get(),
            "services_optimized", optimizationHistory.size(),
            "average_savings_per_service", optimizationHistory.isEmpty() ? 0.0 : 
                (double) totalSavingsIdentified.get() / optimizationHistory.size()
        );
    }

    public OptimizationHistory getOptimizationHistory(String serviceId) {
        return optimizationHistory.get(serviceId);
    }

    private Map<String, Object> analyzeResourceEfficiency(AllocationRequest request, CapacityPrediction prediction) {
        // Simplified efficiency analysis
        ResourceRequirements requirements = request.requirements();
        
        // Calculate utilization efficiency
        double cpuEfficiency = Math.min(100.0, 
            (requirements.cpu().minCores() / requirements.cpu().maxCores()) * 100);
        double memoryEfficiency = Math.min(100.0, 
            (requirements.memory().minGb() / requirements.memory().maxGb()) * 100);
        
        return Map.of(
            "cpu_efficiency", cpuEfficiency,
            "memory_efficiency", memoryEfficiency,
            "scaling_efficiency", calculateScalingEfficiency(requirements.scaling()),
            "storage_efficiency", calculateStorageEfficiency(requirements.storage()),
            "right_sizing_opportunity", identifyRightSizingOpportunity(requirements)
        );
    }

    private double calculatePotentialSavings(AllocationRequest request, Map<String, Object> analysis) {
        BudgetConstraint budget = request.constraints().budgetLimit();
        if (budget == null) {
            return 0.0;
        }
        
        double baseCost = budget.monthlyLimit();
        double inefficiencyFactor = 100.0 - (Double) analysis.get("cpu_efficiency");
        
        // Calculate savings based on inefficiency
        return (baseCost * inefficiencyFactor) / 1000.0; // Conservative estimate
    }

    private Map<String, Object> generateCostRecommendations(AllocationRequest request, 
                                                           Map<String, Object> analysis, 
                                                           double potentialSavings) {
        return Map.of(
            "right_sizing", Map.of(
                "recommended", (Double) analysis.get("right_sizing_opportunity") > 20.0,
                "potential_savings", potentialSavings * 0.4,
                "description", "Right-size instances based on actual usage"
            ),
            "reserved_instances", Map.of(
                "recommended", true,
                "potential_savings", potentialSavings * 0.3,
                "description", "Purchase reserved instances for predictable workloads"
            ),
            "auto_scaling", Map.of(
                "recommended", (Double) analysis.get("scaling_efficiency") < 80.0,
                "potential_savings", potentialSavings * 0.2,
                "description", "Implement more aggressive auto-scaling policies"
            ),
            "storage_optimization", Map.of(
                "recommended", (Double) analysis.get("storage_efficiency") < 70.0,
                "potential_savings", potentialSavings * 0.1,
                "description", "Optimize storage types and usage patterns"
            )
        );
    }

    private double calculateScalingEfficiency(ScalingRequirement scaling) {
        if (scaling.maxInstances() <= scaling.minInstances()) {
            return 50.0; // Poor scaling configuration
        }
        
        double scalingRange = scaling.maxInstances() - scaling.minInstances();
        double efficiencyScore = Math.min(100.0, (scalingRange / scaling.maxInstances()) * 100);
        
        return efficiencyScore;
    }

    private double calculateStorageEfficiency(StorageRequirement storage) {
        double utilizationRatio = storage.minGb() / storage.maxGb();
        return Math.min(100.0, utilizationRatio * 100);
    }

    private double identifyRightSizingOpportunity(ResourceRequirements requirements) {
        // Calculate how much over-provisioning exists
        double cpuOverProvisioning = (requirements.cpu().maxCores() - requirements.cpu().minCores()) / 
                                   requirements.cpu().maxCores() * 100;
        double memoryOverProvisioning = (requirements.memory().maxGb() - requirements.memory().minGb()) / 
                                      requirements.memory().maxGb() * 100;
        
        return (cpuOverProvisioning + memoryOverProvisioning) / 2.0;
    }

    // Additional methods required by ResourceManagerAgent
    
    public CostOptimizationRecommendation optimizeAllocation(AllocationRequest request) {
        logger.info("Optimizing allocation for service: {}", request.serviceId());
        
        try {
            // Simplified optimization without capacity prediction
            Map<String, Object> analysis = Map.of(
                "cpu_efficiency", 75.0,
                "memory_efficiency", 80.0,
                "scaling_efficiency", 85.0,
                "storage_efficiency", 70.0,
                "right_sizing_opportunity", 25.0
            );
            
            double potentialSavings = 100.0; // Simplified
            Map<String, Object> recommendations = generateCostRecommendations(request, analysis, potentialSavings);
            
            return new CostOptimizationRecommendation(request.serviceId(), recommendations);
            
        } catch (Exception e) {
            logger.error("Error optimizing allocation for service {}: {}", request.serviceId(), e.getMessage(), e);
            throw new RuntimeException("Allocation optimization failed: " + e.getMessage());
        }
    }
    
    public CostAnalysis analyzeCosts(String serviceId) {
        logger.info("Analyzing costs for service: {}", serviceId);
        
        return new CostAnalysis(
            serviceId,
            1000.0, // current monthly cost
            800.0,  // projected monthly cost
            200.0,  // potential savings
            Map.of(
                "compute", 600.0,
                "storage", 200.0,
                "network", 200.0
            )
        );
    }
    
    public CostOptimizationRecommendation generateOptimizationRecommendations(String serviceId, CostAnalysis analysis) {
        logger.info("Generating optimization recommendations for service: {}", serviceId);
        
        Map<String, Object> recommendations = Map.of(
            "cost_reduction", Map.of(
                "target_savings", analysis.potentialSavings(),
                "priority_actions", java.util.List.of("right-sizing", "reserved-instances"),
                "timeline", "30 days"
            )
        );
        
        return new CostOptimizationRecommendation(serviceId, recommendations);
    }
    
    public double calculateSavings(String serviceId, CostAnalysis analysis, CostOptimizationRecommendation recommendation) {
        logger.info("Calculating potential savings for service: {}", serviceId);
        
        // Simplified savings calculation
        return analysis.potentialSavings() * 0.8; // 80% confidence in savings
    }
    
    public Map<String, Object> getOptimizationMetrics() {
        return getCostOptimizationMetrics();
    }

    // Helper record classes
    public record CostAnalysis(
        String serviceId,
        double currentMonthlyCost,
        double projectedMonthlyCost,
        double potentialSavings,
        Map<String, Double> costBreakdown
    ) {}

    public record OptimizationHistory(
        String serviceId,
        double potentialSavings,
        Map<String, Object> recommendations,
        Instant optimizedAt
    ) {}
}