package com.multiagent.platform.agents.resource.ai.twodot.platform.agents.resourcemanager.a2a;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Security Monitoring Agent Client (SMA-003)
 * 
 * Integrates with the Security Monitoring Agent to validate security compliance and monitor threats.
 */
@Service
public class SecurityMonitoringAgentClient {

    private static final Logger logger = LoggerFactory.getLogger(SecurityMonitoringAgentClient.class);

    @Autowired
    private A2ACommunicationService communicationService;

    private final AtomicLong complianceRequests = new AtomicLong(0);
    private final AtomicLong successfulCompliance = new AtomicLong(0);
    private final AtomicLong failedCompliance = new AtomicLong(0);
    private final Map<String, CachedCompliance> complianceCache = new HashMap<>();

    /**
     * Validate security compliance for a resource allocation
     */
    public CompletableFuture<SecurityComplianceResult> validateSecurityCompliance(AllocationRequest request) {
        logger.info("Requesting security compliance validation for allocation: {}", request.allocationId());
        
        complianceRequests.incrementAndGet();
        
        // Check cache first
        String cacheKey = generateCacheKey(request);
        CachedCompliance cached = complianceCache.get(cacheKey);
        if (cached != null && isValidCache(cached)) {
            logger.debug("Using cached security compliance for allocation: {}", request.allocationId());
            return CompletableFuture.completedFuture(cached.result());
        }
        
        // Prepare request payload
        Map<String, Object> payload = createSecurityCompliancePayload(request);
        
        return communicationService
            .sendRequest(OperationType.VALIDATE_SECURITY_COMPLIANCE, AgentType.SECURITY_MONITORING_AGENT, 
                        payload, SecurityCompliance.Response.class)
            .thenApply(response -> {
                if (response.success()) {
                    successfulCompliance.incrementAndGet();
                    SecurityComplianceResult result = convertToSecurityComplianceResult(response.data());
                    
                    // Cache the result
                    complianceCache.put(cacheKey, new CachedCompliance(result, Instant.now()));
                    
                    logger.info("Security compliance validation completed for allocation {}: status={}, score={}", 
                        request.allocationId(), result.complianceStatus(), result.overallComplianceScore());
                    
                    return result;
                } else {
                    failedCompliance.incrementAndGet();
                    logger.error("Security compliance validation failed for allocation {}: {} - {}", 
                        request.allocationId(), response.errorCode(), response.errorMessage());
                    
                    return createFallbackSecurityCompliance(request);
                }
            })
            .exceptionally(throwable -> {
                failedCompliance.incrementAndGet();
                logger.error("Error during security compliance validation for allocation {}: {}", 
                    request.allocationId(), throwable.getMessage(), throwable);
                
                return createFallbackSecurityCompliance(request);
            });
    }

    /**
     * Get security recommendations for a resource allocation
     */
    public CompletableFuture<SecurityRecommendationResult> getSecurityRecommendations(String resourceId,
                                                                                     String serviceId,
                                                                                     Map<String, Object> securityContext) {
        logger.info("Requesting security recommendations for resource: {}", resourceId);
        
        Map<String, Object> payload = Map.of(
            "resource_id", resourceId,
            "service_id", serviceId,
            "security_context", securityContext,
            "recommendation_scope", "comprehensive",
            "include_preventive_measures", true
        );
        
        return communicationService
            .sendRequest(OperationType.GET_SECURITY_RECOMMENDATIONS, AgentType.SECURITY_MONITORING_AGENT, 
                        payload, Map.class)
            .thenApply(response -> {
                if (response.success()) {
                    return convertToSecurityRecommendationResult(response.data());
                } else {
                    logger.error("Security recommendations request failed for resource {}: {} - {}", 
                        resourceId, response.errorCode(), response.errorMessage());
                    return createFallbackSecurityRecommendations(resourceId, serviceId);
                }
            })
            .exceptionally(throwable -> {
                logger.error("Error getting security recommendations for resource {}: {}", 
                    resourceId, throwable.getMessage(), throwable);
                return createFallbackSecurityRecommendations(resourceId, serviceId);
            });
    }

    /**
     * Monitor security events for allocated resources
     */
    public CompletableFuture<SecurityMonitoringResult> monitorSecurityEvents(String allocationId,
                                                                            List<String> resourceIds) {
        logger.info("Requesting security monitoring for allocation: {}", allocationId);
        
        Map<String, Object> payload = Map.of(
            "allocation_id", allocationId,
            "resource_ids", resourceIds,
            "monitoring_duration", "continuous",
            "alert_thresholds", createDefaultAlertThresholds(),
            "monitoring_scope", List.of("threats", "compliance", "vulnerabilities", "access")
        );
        
        return communicationService
            .sendRequest(OperationType.MONITOR_SECURITY_EVENTS, AgentType.SECURITY_MONITORING_AGENT, 
                        payload, Map.class)
            .thenApply(response -> {
                if (response.success()) {
                    return convertToSecurityMonitoringResult(response.data());
                } else {
                    logger.error("Security monitoring request failed for allocation {}: {} - {}", 
                        allocationId, response.errorCode(), response.errorMessage());
                    return createFallbackSecurityMonitoring(allocationId, resourceIds);
                }
            })
            .exceptionally(throwable -> {
                logger.error("Error setting up security monitoring for allocation {}: {}", 
                    allocationId, throwable.getMessage(), throwable);
                return createFallbackSecurityMonitoring(allocationId, resourceIds);
            });
    }

    /**
     * Get security metrics and status
     */
    public CompletableFuture<SecurityStatusResult> getSecurityStatus(List<String> resourceIds) {
        logger.info("Requesting security status for {} resources", resourceIds.size());
        
        Map<String, Object> payload = Map.of(
            "resource_ids", resourceIds,
            "include_threat_intelligence", true,
            "include_vulnerability_scan", true,
            "include_compliance_status", true
        );
        
        return communicationService
            .sendRequest(OperationType.GET_STATUS, AgentType.SECURITY_MONITORING_AGENT, 
                        payload, Map.class)
            .thenApply(response -> {
                if (response.success()) {
                    return convertToSecurityStatusResult(response.data());
                } else {
                    logger.error("Security status request failed: {} - {}", 
                        response.errorCode(), response.errorMessage());
                    return createFallbackSecurityStatus(resourceIds);
                }
            })
            .exceptionally(throwable -> {
                logger.error("Error getting security status: {}", throwable.getMessage(), throwable);
                return createFallbackSecurityStatus(resourceIds);
            });
    }

    /**
     * Get SMA client metrics
     */
    public Map<String, Object> getMetrics() {
        return Map.of(
            "compliance_requests", complianceRequests.get(),
            "successful_compliance", successfulCompliance.get(),
            "failed_compliance", failedCompliance.get(),
            "success_rate", calculateSuccessRate(),
            "cache_size", complianceCache.size(),
            "sma_agent_available", communicationService.isAgentAvailable(AgentType.SECURITY_MONITORING_AGENT),
            "last_updated", Instant.now()
        );
    }

    // Private methods

    private Map<String, Object> createSecurityCompliancePayload(AllocationRequest request) {
        Map<String, Object> securityContext = Map.of(
            "service_criticality", determineCriticality(request.priority()),
            "data_classification", determineDataClassification(request),
            "network_exposure", "internal", // Default to internal
            "encryption_requirements", Map.of(
                "at_rest", true,
                "in_transit", true,
                "key_management", "cloud_provider"
            ),
            "access_control", Map.of(
                "authentication", "multi_factor",
                "authorization", "rbac",
                "audit_logging", true
            )
        );

        List<String> complianceStandards = new ArrayList<>();
        if (request.constraints().complianceRequirements() != null) {
            complianceStandards.addAll(request.constraints().complianceRequirements());
        }
        
        // Add default standards based on service criticality
        switch (request.priority()) {
            case CRITICAL -> complianceStandards.addAll(List.of("SOC2", "ISO27001", "HIPAA", "PCI-DSS"));
            case HIGH -> complianceStandards.addAll(List.of("SOC2", "ISO27001"));
            case NORMAL -> complianceStandards.add("SOC2");
            default -> { /* No additional standards for LOW priority */ }
        }

        Map<String, Object> securityRequirements = Map.of(
            "minimum_security_level", request.priority() == AllocationPriority.CRITICAL ? "high" : "medium",
            "vulnerability_scanning", true,
            "penetration_testing", request.priority().ordinal() >= AllocationPriority.HIGH.ordinal(),
            "security_monitoring", true,
            "incident_response", true,
            "backup_encryption", true,
            "network_segmentation", request.priority() == AllocationPriority.CRITICAL
        );

        return Map.of(
            "resource_id", request.allocationId(),
            "service_id", request.serviceId(),
            "security_context", securityContext,
            "compliance_standards", complianceStandards,
            "security_requirements", securityRequirements
        );
    }

    private Map<String, Object> createDefaultAlertThresholds() {
        return Map.of(
            "failed_login_attempts", 5,
            "unusual_access_patterns", 3,
            "data_exfiltration_mb", 100,
            "vulnerability_severity", "HIGH",
            "compliance_violations", 1,
            "unauthorized_access_attempts", 3
        );
    }

    private SecurityComplianceResult convertToSecurityComplianceResult(SecurityCompliance.Response response) {
        List<SecurityFindingResult> findings = response.securityFindings().stream()
            .map(finding -> new SecurityFindingResult(
                finding.findingId(),
                finding.severity(),
                finding.category(),
                finding.description(),
                finding.affectedResources(),
                finding.cveReferences()
            ))
            .toList();

        List<ComplianceGapResult> gaps = response.complianceGaps().stream()
            .map(gap -> new ComplianceGapResult(
                gap.standard(),
                gap.requirement(),
                gap.currentState(),
                gap.requiredState(),
                gap.gapSeverity()
            ))
            .toList();

        List<SecurityRecommendationDetail> recommendations = response.securityRecommendations().stream()
            .map(rec -> new SecurityRecommendationDetail(
                rec.recommendationId(),
                rec.title(),
                rec.description(),
                rec.priority().name(),
                rec.implementationEffort(),
                rec.expectedImpact()
            ))
            .toList();

        List<RemediationActionDetail> remediationPlan = response.remediationPlan().stream()
            .map(action -> new RemediationActionDetail(
                action.actionId(),
                action.description(),
                action.priority().name(),
                action.estimatedEffortHours(),
                action.dependencies()
            ))
            .toList();

        return new SecurityComplianceResult(
            response.complianceId(),
            response.overallComplianceScore(),
            response.complianceStatus(),
            findings,
            gaps,
            recommendations,
            remediationPlan,
            Instant.now()
        );
    }

    private SecurityRecommendationResult convertToSecurityRecommendationResult(Map<String, Object> data) {
        List<Map<String, Object>> recommendationsData = (List<Map<String, Object>>) data.getOrDefault("recommendations", List.of());
        List<SecurityRecommendationDetail> recommendations = recommendationsData.stream()
            .map(recData -> new SecurityRecommendationDetail(
                (String) recData.get("recommendation_id"),
                (String) recData.get("title"),
                (String) recData.get("description"),
                (String) recData.getOrDefault("priority", "MEDIUM"),
                (String) recData.getOrDefault("implementation_effort", "medium"),
                (String) recData.getOrDefault("expected_impact", "medium")
            ))
            .toList();

        return new SecurityRecommendationResult(
            (String) data.get("recommendation_set_id"),
            recommendations,
            (List<String>) data.getOrDefault("immediate_actions", List.of()),
            (Double) data.getOrDefault("security_improvement_score", 85.0),
            Instant.now()
        );
    }

    private SecurityMonitoringResult convertToSecurityMonitoringResult(Map<String, Object> data) {
        return new SecurityMonitoringResult(
            (String) data.get("monitoring_id"),
            (Boolean) data.getOrDefault("monitoring_active", true),
            (List<String>) data.getOrDefault("monitored_resources", List.of()),
            (Map<String, Object>) data.getOrDefault("alert_configuration", Map.of()),
            (List<String>) data.getOrDefault("monitoring_capabilities", List.of()),
            Instant.now()
        );
    }

    private SecurityStatusResult convertToSecurityStatusResult(Map<String, Object> data) {
        return new SecurityStatusResult(
            (String) data.get("status_id"),
            (String) data.getOrDefault("overall_security_status", "SECURE"),
            (Double) data.getOrDefault("security_score", 85.0),
            (Integer) data.getOrDefault("active_threats", 0),
            (Integer) data.getOrDefault("vulnerabilities_found", 0),
            (Map<String, Boolean>) data.getOrDefault("compliance_status", Map.of()),
            (List<String>) data.getOrDefault("recent_security_events", List.of()),
            (List<String>) data.getOrDefault("recommendations", List.of()),
            Instant.now()
        );
    }

    private SecurityComplianceResult createFallbackSecurityCompliance(AllocationRequest request) {
        logger.info("Creating fallback security compliance for allocation: {}", request.allocationId());
        
        // Create basic compliance assessment
        List<SecurityFindingResult> findings = List.of(
            new SecurityFindingResult(
                "FALLBACK-001", "MEDIUM", "configuration",
                "Default security configurations in use",
                List.of(request.allocationId()), List.of()
            )
        );
        
        List<ComplianceGapResult> gaps = List.of(
            new ComplianceGapResult(
                "SOC2", "Encryption at rest", "basic", "advanced", "MEDIUM"
            )
        );
        
        List<SecurityRecommendationDetail> recommendations = List.of(
            new SecurityRecommendationDetail(
                "REC-001", "Enable enhanced monitoring", 
                "Implement comprehensive security monitoring", "HIGH", "medium", "high"
            ),
            new SecurityRecommendationDetail(
                "REC-002", "Configure encryption", 
                "Enable encryption for all data at rest", "HIGH", "low", "high"
            )
        );
        
        List<RemediationActionDetail> remediationPlan = List.of(
            new RemediationActionDetail(
                "ACT-001", "Configure monitoring dashboard", "HIGH", 4, List.of()
            ),
            new RemediationActionDetail(
                "ACT-002", "Enable encryption settings", "HIGH", 2, List.of("ACT-001")
            )
        );
        
        return new SecurityComplianceResult(
            UUID.randomUUID().toString(),
            75.0, // Good baseline score
            "PARTIAL",
            findings,
            gaps,
            recommendations,
            remediationPlan,
            Instant.now()
        );
    }

    private SecurityRecommendationResult createFallbackSecurityRecommendations(String resourceId, String serviceId) {
        List<SecurityRecommendationDetail> recommendations = List.of(
            new SecurityRecommendationDetail(
                "FB-REC-001", "Enable security monitoring", 
                "Implement comprehensive security monitoring for the resource", "HIGH", "medium", "high"
            ),
            new SecurityRecommendationDetail(
                "FB-REC-002", "Configure access controls", 
                "Implement proper access controls and authentication", "HIGH", "low", "high"
            ),
            new SecurityRecommendationDetail(
                "FB-REC-003", "Set up automated backups", 
                "Configure automated backups with encryption", "MEDIUM", "low", "medium"
            )
        );
        
        return new SecurityRecommendationResult(
            UUID.randomUUID().toString(),
            recommendations,
            List.of("Enable basic monitoring", "Configure firewall rules"),
            80.0,
            Instant.now()
        );
    }

    private SecurityMonitoringResult createFallbackSecurityMonitoring(String allocationId, List<String> resourceIds) {
        return new SecurityMonitoringResult(
            UUID.randomUUID().toString(),
            true,
            resourceIds,
            createDefaultAlertThresholds(),
            List.of("basic_monitoring", "threat_detection", "compliance_checking"),
            Instant.now()
        );
    }

    private SecurityStatusResult createFallbackSecurityStatus(List<String> resourceIds) {
        return new SecurityStatusResult(
            UUID.randomUUID().toString(),
            "SECURE",
            80.0,
            0, // No active threats
            0, // No vulnerabilities found
            Map.of("basic_security", true, "encryption", true, "monitoring", false),
            List.of("Security monitoring activated"),
            List.of("Enable comprehensive monitoring", "Review access controls"),
            Instant.now()
        );
    }

    private String determineCriticality(AllocationPriority priority) {
        return switch (priority) {
            case CRITICAL, EMERGENCY -> "critical";
            case HIGH -> "high";
            case NORMAL -> "medium";
            case LOW -> "low";
        };
    }

    private String determineDataClassification(AllocationRequest request) {
        // Determine data classification based on compliance requirements
        if (request.constraints().complianceRequirements() != null) {
            List<String> requirements = request.constraints().complianceRequirements();
            if (requirements.contains("HIPAA") || requirements.contains("PCI-DSS")) {
                return "sensitive";
            }
            if (requirements.contains("SOC2") || requirements.contains("ISO27001")) {
                return "confidential";
            }
        }
        return "internal";
    }

    private String generateCacheKey(AllocationRequest request) {
        return String.format("sma_%s_%d", request.serviceId(), 
            Objects.hash(request.requirements(), request.constraints()));
    }

    private boolean isValidCache(CachedCompliance cached) {
        return cached.timestamp().isAfter(Instant.now().minusSeconds(1800)); // 30 minutes TTL
    }

    private double calculateSuccessRate() {
        long total = complianceRequests.get();
        if (total == 0) {
            return 1.0;
        }
        return (double) successfulCompliance.get() / total;
    }

    // Result classes

    public record SecurityComplianceResult(
        String complianceId,
        double overallComplianceScore,
        String complianceStatus,
        List<SecurityFindingResult> securityFindings,
        List<ComplianceGapResult> complianceGaps,
        List<SecurityRecommendationDetail> securityRecommendations,
        List<RemediationActionDetail> remediationPlan,
        Instant validatedAt
    ) {}

    public record SecurityFindingResult(
        String findingId,
        String severity,
        String category,
        String description,
        List<String> affectedResources,
        List<String> cveReferences
    ) {}

    public record ComplianceGapResult(
        String standard,
        String requirement,
        String currentState,
        String requiredState,
        String gapSeverity
    ) {}

    public record SecurityRecommendationDetail(
        String recommendationId,
        String title,
        String description,
        String priority,
        String implementationEffort,
        String expectedImpact
    ) {}

    public record RemediationActionDetail(
        String actionId,
        String description,
        String priority,
        int estimatedEffortHours,
        List<String> dependencies
    ) {}

    public record SecurityRecommendationResult(
        String recommendationSetId,
        List<SecurityRecommendationDetail> recommendations,
        List<String> immediateActions,
        double securityImprovementScore,
        Instant generatedAt
    ) {}

    public record SecurityMonitoringResult(
        String monitoringId,
        boolean monitoringActive,
        List<String> monitoredResources,
        Map<String, Object> alertConfiguration,
        List<String> monitoringCapabilities,
        Instant configuredAt
    ) {}

    public record SecurityStatusResult(
        String statusId,
        String overallSecurityStatus,
        double securityScore,
        int activeThreats,
        int vulnerabilitiesFound,
        Map<String, Boolean> complianceStatus,
        List<String> recentSecurityEvents,
        List<String> recommendations,
        Instant statusAt
    ) {}

    private record CachedCompliance(
        SecurityComplianceResult result,
        Instant timestamp
    ) {}
}