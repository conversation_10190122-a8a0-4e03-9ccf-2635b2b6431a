package com.multiagent.platform.agents.resource.ai.twodot.platform.agents.resourcemanager.scaling;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Auto Scaling Service
 * 
 * Manages automatic scaling of resources based on AI predictions and triggers.
 */
@Service
public class AutoScalingService {

    private static final Logger logger = LoggerFactory.getLogger(AutoScalingService.class);

    private final AtomicBoolean autoScalingEnabled = new AtomicBoolean(true);
    private final AtomicLong scalingDecisionsMade = new AtomicLong(0);
    private final AtomicLong scalingErrors = new AtomicLong(0);
    private final Map<String, ScalingConfiguration> scalingConfigs = new ConcurrentHashMap<>();

    public void configureAutoScaling(AllocationResult allocationResult) {
        String serviceId = extractServiceId(allocationResult);
        logger.info("Configuring auto-scaling for service: {}", serviceId);
        
        // Create default scaling configuration
        ScalingConfiguration config = new ScalingConfiguration(
            serviceId,
            true, // enabled
            70.0, // CPU threshold
            80.0, // Memory threshold
            1, // min instances
            10, // max instances
            300, // scale up cooldown seconds
            600, // scale down cooldown seconds
            Instant.now()
        );
        
        scalingConfigs.put(serviceId, config);
        logger.info("Auto-scaling configured for service: {}", serviceId);
    }

    public ScalingDecision executeScaling(String serviceId, ScalingRecommendation recommendation, ResourceHealth currentHealth) {
        logger.info("Executing scaling for service: {}", serviceId);
        
        try {
            scalingDecisionsMade.incrementAndGet();
            
            // Simplified scaling logic
            int currentInstances = getCurrentInstanceCount(serviceId);
            int targetInstances = calculateTargetInstances(recommendation, currentHealth);
            
            ScalingDirection direction = targetInstances > currentInstances ? 
                ScalingDirection.UP : 
                (targetInstances < currentInstances ? ScalingDirection.DOWN : ScalingDirection.NONE);
            
            ScalingDecision decision = new ScalingDecision(
                serviceId,
                currentInstances,
                targetInstances,
                direction,
                "Auto-scaling based on AI recommendation",
                0.85,
                Instant.now().plusSeconds(120),
                calculateCostImpact(currentInstances, targetInstances),
                "Expected performance improvement"
            );
            
            logger.info("Scaling decision made for service {}: {} -> {} instances", 
                serviceId, currentInstances, targetInstances);
            
            return decision;
            
        } catch (Exception e) {
            scalingErrors.incrementAndGet();
            logger.error("Error executing scaling for service {}: {}", serviceId, e.getMessage(), e);
            throw new RuntimeException("Scaling execution failed: " + e.getMessage());
        }
    }

    public boolean isAutoScalingEnabled() {
        return autoScalingEnabled.get();
    }

    public void setAutoScalingEnabled(boolean enabled) {
        autoScalingEnabled.set(enabled);
        logger.info("Auto-scaling {}", enabled ? "enabled" : "disabled");
    }

    public Map<String, Object> getScalingMetrics() {
        return Map.of(
            "auto_scaling_enabled", autoScalingEnabled.get(),
            "scaling_decisions_made", scalingDecisionsMade.get(),
            "scaling_errors", scalingErrors.get(),
            "configured_services", scalingConfigs.size(),
            "service_configs", scalingConfigs
        );
    }

    private String extractServiceId(AllocationResult allocationResult) {
        // Extract service ID from allocation result
        return "service-" + allocationResult.allocationId().substring(0, 8);
    }

    private int getCurrentInstanceCount(String serviceId) {
        ScalingConfiguration config = scalingConfigs.get(serviceId);
        return config != null ? 2 : 1; // Default to 2 instances if configured, 1 otherwise
    }

    private int calculateTargetInstances(ScalingRecommendation recommendation, ResourceHealth currentHealth) {
        // Simplified calculation based on CPU utilization
        double cpuUtilization = currentHealth.cpuUtilization();
        
        if (cpuUtilization > 80) {
            return Math.min(10, getCurrentInstanceCount("") + 2); // Scale up
        } else if (cpuUtilization < 30) {
            return Math.max(1, getCurrentInstanceCount("") - 1); // Scale down
        } else {
            return getCurrentInstanceCount(""); // No change
        }
    }

    private double calculateCostImpact(int currentInstances, int targetInstances) {
        double costPerInstance = 0.10; // $0.10 per hour per instance
        return (targetInstances - currentInstances) * costPerInstance;
    }

    public record ScalingConfiguration(
        String serviceId,
        boolean enabled,
        double cpuThreshold,
        double memoryThreshold,
        int minInstances,
        int maxInstances,
        int scaleUpCooldownSeconds,
        int scaleDownCooldownSeconds,
        Instant lastUpdated
    ) {}
}