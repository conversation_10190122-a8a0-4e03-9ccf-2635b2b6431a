package com.multiagent.platform.agents.resource.ai.twodot.platform.agents.resourcemanager;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Resource Manager Agent Application
 * 
 * AI-powered resource allocation and management with multi-cloud optimization.
 * Provides intelligent auto-scaling, capacity prediction, cost optimization,
 * and sustainability tracking across AWS, GCP, Azure, and Kubernetes.
 */
@SpringBootApplication
@EnableAsync
@EnableScheduling
public class ResourceManagerAgentApplication {

    public static void main(String[] args) {
        SpringApplication.run(ResourceManagerAgentApplication.class, args);
    }
}