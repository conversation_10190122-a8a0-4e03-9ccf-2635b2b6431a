# Resource Manager Agent Configuration
server.port=8083
spring.application.name=resource-manager-agent

# PostgreSQL Database Configuration
spring.datasource.url=****************************************************
spring.datasource.driverClassName=org.postgresql.Driver
spring.datasource.username=${DB_USERNAME:koneti}
spring.datasource.password=${DB_PASSWORD:}
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=true

# Redis Configuration
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.timeout=2000ms

# Management and Actuator
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=always
management.metrics.export.prometheus.enabled=true

# Resource Manager Specific
resource.manager.cloud.providers=aws,gcp,azure,kubernetes
resource.manager.auto.scaling.enabled=true
resource.manager.cost.optimization.enabled=true
resource.manager.ai.enabled=true
resource.manager.ai.service.url=http://localhost:9083

# Cloud Provider Configuration
cloud.aws.enabled=false
cloud.gcp.enabled=false
cloud.azure.enabled=false
cloud.kubernetes.enabled=true

# Logging
logging.level.ai.twodot.platform.agents.resourcemanager=INFO
logging.level.org.springframework.web=INFO
logging.level.root=INFO