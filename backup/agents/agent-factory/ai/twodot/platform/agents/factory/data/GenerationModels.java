package com.multiagent.platform.agents.factory.ai.twodot.platform.agents.factory.data;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.Instant;
import java.util.List;
import java.util.Map;

/**
 * Agent Factory Agent Data Models
 * 
 * Comprehensive data structures for AI-powered agent generation, template management,
 * quality validation, and deployment automation in the Agent Factory Agent.
 * 
 * These models support:
 * - Agent generation requests and responses
 * - Multi-language code generation
 * - Template management and customization
 * - Quality validation and optimization
 * - Deployment automation and CI/CD integration
 * - AI model integration and feedback
 * 
 * <AUTHOR> Platform Team
 * @version 1.0.0
 * @since 2025-01-14
 */
public class GenerationModels {

    // ===== Agent Generation Models =====

    /**
     * Agent generation request from user or system
     */
    public record AgentGenerationRequest(
        @JsonProperty("request_id") String requestId,
        @JsonProperty("agent_name") String agentName,
        @JsonProperty("description") String description,
        @JsonProperty("requirements") AgentRequirements requirements,
        @JsonProperty("preferences") GenerationPreferences preferences,
        @JsonProperty("context") GenerationContext context,
        @JsonProperty("constraints") List<GenerationConstraint> constraints,
        @JsonProperty("quality_requirements") QualityRequirements qualityRequirements,
        @JsonProperty("deployment_requirements") DeploymentRequirements deploymentRequirements,
        @JsonProperty("requested_by") String requestedBy,
        @JsonProperty("priority") GenerationPriority priority,
        @JsonProperty("timeout_minutes") int timeoutMinutes,
        @JsonProperty("created_at") Instant createdAt
    ) {}

    /**
     * Agent generation response with complete results
     */
    public record AgentGenerationResponse(
        @JsonProperty("request_id") String requestId,
        @JsonProperty("generation_id") String generationId,
        @JsonProperty("status") GenerationStatus status,
        @JsonProperty("agent_specification") AgentSpecification agentSpecification,
        @JsonProperty("generated_code") GeneratedCodeResult generatedCode,
        @JsonProperty("test_suite") TestSuiteResult testSuite,
        @JsonProperty("documentation") DocumentationResult documentation,
        @JsonProperty("deployment_config") DeploymentConfiguration deploymentConfig,
        @JsonProperty("quality_report") QualityReport qualityReport,
        @JsonProperty("generation_metadata") GenerationMetadata generationMetadata,
        @JsonProperty("ai_insights") List<AIInsight> aiInsights,
        @JsonProperty("completion_time") Instant completionTime,
        @JsonProperty("total_duration_ms") long totalDurationMs
    ) {}

    /**
     * Agent requirements specification
     */
    public record AgentRequirements(
        @JsonProperty("functional_requirements") List<FunctionalRequirement> functionalRequirements,
        @JsonProperty("non_functional_requirements") List<NonFunctionalRequirement> nonFunctionalRequirements,
        @JsonProperty("integration_requirements") List<IntegrationRequirement> integrationRequirements,
        @JsonProperty("ai_capabilities") List<AICapabilityRequirement> aiCapabilities,
        @JsonProperty("data_requirements") List<DataRequirement> dataRequirements,
        @JsonProperty("security_requirements") List<SecurityRequirement> securityRequirements,
        @JsonProperty("performance_requirements") PerformanceRequirements performanceRequirements,
        @JsonProperty("scalability_requirements") ScalabilityRequirements scalabilityRequirements
    ) {}

    /**
     * Generation preferences and options
     */
    public record GenerationPreferences(
        @JsonProperty("preferred_languages") List<ProgrammingLanguage> preferredLanguages,
        @JsonProperty("architecture_style") ArchitectureStyle architectureStyle,
        @JsonProperty("framework_preferences") List<FrameworkPreference> frameworkPreferences,
        @JsonProperty("code_style") CodeStyle codeStyle,
        @JsonProperty("testing_approach") TestingApproach testingApproach,
        @JsonProperty("documentation_level") DocumentationLevel documentationLevel,
        @JsonProperty("optimization_focus") List<OptimizationFocus> optimizationFocus,
        @JsonProperty("deployment_target") DeploymentTarget deploymentTarget
    ) {}

    /**
     * Agent specification generated by AI
     */
    public record AgentSpecification(
        @JsonProperty("agent_id") String agentId,
        @JsonProperty("agent_name") String agentName,
        @JsonProperty("agent_type") AgentType agentType,
        @JsonProperty("architecture") AgentArchitecture architecture,
        @JsonProperty("components") List<ComponentSpecification> components,
        @JsonProperty("interfaces") List<InterfaceSpecification> interfaces,
        @JsonProperty("dependencies") List<DependencySpecification> dependencies,
        @JsonProperty("capabilities") List<CapabilitySpecification> capabilities,
        @JsonProperty("ai_integration") AIIntegrationSpecification aiIntegration,
        @JsonProperty("data_models") List<DataModelSpecification> dataModels,
        @JsonProperty("configuration") ConfigurationSpecification configuration,
        @JsonProperty("monitoring") MonitoringSpecification monitoring
    ) {}

    // ===== Code Generation Models =====

    /**
     * Generated code result with all languages
     */
    public record GeneratedCodeResult(
        @JsonProperty("generation_id") String generationId,
        @JsonProperty("languages_generated") List<ProgrammingLanguage> languagesGenerated,
        @JsonProperty("python_code") PythonCodeResult pythonCode,
        @JsonProperty("java_code") JavaCodeResult javaCode,
        @JsonProperty("typescript_code") TypeScriptCodeResult typeScriptCode,
        @JsonProperty("go_code") GoCodeResult goCode,
        @JsonProperty("integration_code") IntegrationCodeResult integrationCode,
        @JsonProperty("build_configuration") BuildConfiguration buildConfiguration,
        @JsonProperty("project_structure") ProjectStructure projectStructure,
        @JsonProperty("code_metrics") CodeMetrics codeMetrics,
        @JsonProperty("generated_at") Instant generatedAt
    ) {}

    /**
     * Python code generation result
     */
    public record PythonCodeResult(
        @JsonProperty("main_application") GeneratedFile mainApplication,
        @JsonProperty("agent_implementation") GeneratedFile agentImplementation,
        @JsonProperty("ai_integration") List<GeneratedFile> aiIntegration,
        @JsonProperty("api_endpoints") List<GeneratedFile> apiEndpoints,
        @JsonProperty("data_models") List<GeneratedFile> dataModels,
        @JsonProperty("utilities") List<GeneratedFile> utilities,
        @JsonProperty("configuration") List<GeneratedFile> configuration,
        @JsonProperty("requirements") GeneratedFile requirements,
        @JsonProperty("dockerfile") GeneratedFile dockerfile,
        @JsonProperty("package_structure") PackageStructure packageStructure
    ) {}

    /**
     * Java code generation result
     */
    public record JavaCodeResult(
        @JsonProperty("main_application") GeneratedFile mainApplication,
        @JsonProperty("agent_implementation") GeneratedFile agentImplementation,
        @JsonProperty("controllers") List<GeneratedFile> controllers,
        @JsonProperty("services") List<GeneratedFile> services,
        @JsonProperty("repositories") List<GeneratedFile> repositories,
        @JsonProperty("models") List<GeneratedFile> models,
        @JsonProperty("configuration") List<GeneratedFile> configuration,
        @JsonProperty("integration") List<GeneratedFile> integration,
        @JsonProperty("pom_xml") GeneratedFile pomXml,
        @JsonProperty("dockerfile") GeneratedFile dockerfile,
        @JsonProperty("package_structure") PackageStructure packageStructure
    ) {}

    /**
     * TypeScript code generation result
     */
    public record TypeScriptCodeResult(
        @JsonProperty("main_application") GeneratedFile mainApplication,
        @JsonProperty("agent_implementation") GeneratedFile agentImplementation,
        @JsonProperty("components") List<GeneratedFile> components,
        @JsonProperty("services") List<GeneratedFile> services,
        @JsonProperty("models") List<GeneratedFile> models,
        @JsonProperty("utilities") List<GeneratedFile> utilities,
        @JsonProperty("configuration") List<GeneratedFile> configuration,
        @JsonProperty("package_json") GeneratedFile packageJson,
        @JsonProperty("tsconfig") GeneratedFile tsConfig,
        @JsonProperty("dockerfile") GeneratedFile dockerfile
    ) {}

    /**
     * Go code generation result
     */
    public record GoCodeResult(
        @JsonProperty("main_application") GeneratedFile mainApplication,
        @JsonProperty("agent_implementation") GeneratedFile agentImplementation,
        @JsonProperty("handlers") List<GeneratedFile> handlers,
        @JsonProperty("services") List<GeneratedFile> services,
        @JsonProperty("models") List<GeneratedFile> models,
        @JsonProperty("utilities") List<GeneratedFile> utilities,
        @JsonProperty("configuration") List<GeneratedFile> configuration,
        @JsonProperty("go_mod") GeneratedFile goMod,
        @JsonProperty("dockerfile") GeneratedFile dockerfile,
        @JsonProperty("package_structure") PackageStructure packageStructure
    ) {}

    /**
     * Generated file representation
     */
    public record GeneratedFile(
        @JsonProperty("file_path") String filePath,
        @JsonProperty("file_name") String fileName,
        @JsonProperty("content") String content,
        @JsonProperty("language") ProgrammingLanguage language,
        @JsonProperty("file_type") FileType fileType,
        @JsonProperty("template_used") String templateUsed,
        @JsonProperty("ai_model_used") String aiModelUsed,
        @JsonProperty("quality_score") double qualityScore,
        @JsonProperty("complexity_score") double complexityScore,
        @JsonProperty("security_score") double securityScore,
        @JsonProperty("generated_at") Instant generatedAt
    ) {}

    // ===== Template Management Models =====

    /**
     * Template specification and metadata
     */
    public record AgentTemplate(
        @JsonProperty("template_id") String templateId,
        @JsonProperty("template_name") String templateName,
        @JsonProperty("description") String description,
        @JsonProperty("version") String version,
        @JsonProperty("template_type") TemplateType templateType,
        @JsonProperty("supported_languages") List<ProgrammingLanguage> supportedLanguages,
        @JsonProperty("template_files") List<TemplateFile> templateFiles,
        @JsonProperty("variables") List<TemplateVariable> variables,
        @JsonProperty("constraints") List<TemplateConstraint> constraints,
        @JsonProperty("usage_statistics") TemplateUsageStatistics usageStatistics,
        @JsonProperty("quality_rating") double qualityRating,
        @JsonProperty("created_by") String createdBy,
        @JsonProperty("created_at") Instant createdAt,
        @JsonProperty("updated_at") Instant updatedAt
    ) {}

    /**
     * Template file specification
     */
    public record TemplateFile(
        @JsonProperty("file_path") String filePath,
        @JsonProperty("template_content") String templateContent,
        @JsonProperty("file_type") FileType fileType,
        @JsonProperty("language") ProgrammingLanguage language,
        @JsonProperty("generation_order") int generationOrder,
        @JsonProperty("dependencies") List<String> dependencies,
        @JsonProperty("conditional_generation") ConditionalGeneration conditionalGeneration
    ) {}

    /**
     * Template variable definition
     */
    public record TemplateVariable(
        @JsonProperty("variable_name") String variableName,
        @JsonProperty("variable_type") VariableType variableType,
        @JsonProperty("description") String description,
        @JsonProperty("default_value") Object defaultValue,
        @JsonProperty("validation_rules") List<ValidationRule> validationRules,
        @JsonProperty("is_required") boolean isRequired,
        @JsonProperty("possible_values") List<Object> possibleValues
    ) {}

    // ===== Quality Validation Models =====

    /**
     * Comprehensive quality report
     */
    public record QualityReport(
        @JsonProperty("report_id") String reportId,
        @JsonProperty("generation_id") String generationId,
        @JsonProperty("overall_quality_score") double overallQualityScore,
        @JsonProperty("meets_standards") boolean meetsStandards,
        @JsonProperty("code_quality") CodeQualityMetrics codeQuality,
        @JsonProperty("security_analysis") SecurityAnalysisResult securityAnalysis,
        @JsonProperty("performance_analysis") PerformanceAnalysisResult performanceAnalysis,
        @JsonProperty("test_coverage") TestCoverageReport testCoverage,
        @JsonProperty("documentation_quality") DocumentationQualityReport documentationQuality,
        @JsonProperty("compliance_check") ComplianceCheckResult complianceCheck,
        @JsonProperty("improvement_suggestions") List<ImprovementSuggestion> improvementSuggestions,
        @JsonProperty("generated_at") Instant generatedAt
    ) {}

    /**
     * Code quality metrics
     */
    public record CodeQualityMetrics(
        @JsonProperty("complexity_score") double complexityScore,
        @JsonProperty("maintainability_index") double maintainabilityIndex,
        @JsonProperty("duplication_percentage") double duplicationPercentage,
        @JsonProperty("code_coverage_percentage") double codeCoveragePercentage,
        @JsonProperty("technical_debt_minutes") long technicalDebtMinutes,
        @JsonProperty("bugs_count") int bugsCount,
        @JsonProperty("vulnerabilities_count") int vulnerabilitiesCount,
        @JsonProperty("code_smells_count") int codeSmellsCount,
        @JsonProperty("lines_of_code") int linesOfCode,
        @JsonProperty("cognitive_complexity") int cognitiveComplexity
    ) {}

    // ===== Testing Models =====

    /**
     * Generated test suite result
     */
    public record TestSuiteResult(
        @JsonProperty("suite_id") String suiteId,
        @JsonProperty("generation_id") String generationId,
        @JsonProperty("test_types") List<TestType> testTypes,
        @JsonProperty("unit_tests") List<GeneratedTest> unitTests,
        @JsonProperty("integration_tests") List<GeneratedTest> integrationTests,
        @JsonProperty("performance_tests") List<GeneratedTest> performanceTests,
        @JsonProperty("security_tests") List<GeneratedTest> securityTests,
        @JsonProperty("test_configuration") TestConfiguration testConfiguration,
        @JsonProperty("coverage_report") TestCoverageReport coverageReport,
        @JsonProperty("estimated_execution_time") long estimatedExecutionTimeMs,
        @JsonProperty("generated_at") Instant generatedAt
    ) {}

    /**
     * Individual generated test
     */
    public record GeneratedTest(
        @JsonProperty("test_id") String testId,
        @JsonProperty("test_name") String testName,
        @JsonProperty("test_type") TestType testType,
        @JsonProperty("test_file") GeneratedFile testFile,
        @JsonProperty("target_component") String targetComponent,
        @JsonProperty("test_scenarios") List<TestScenario> testScenarios,
        @JsonProperty("dependencies") List<String> dependencies,
        @JsonProperty("setup_requirements") List<String> setupRequirements,
        @JsonProperty("expected_coverage") double expectedCoverage
    ) {}

    // ===== Deployment Models =====

    /**
     * Deployment configuration for generated agent
     */
    public record DeploymentConfiguration(
        @JsonProperty("deployment_id") String deploymentId,
        @JsonProperty("generation_id") String generationId,
        @JsonProperty("deployment_strategy") DeploymentStrategy deploymentStrategy,
        @JsonProperty("target_environment") DeploymentEnvironment targetEnvironment,
        @JsonProperty("kubernetes_manifests") List<GeneratedFile> kubernetesManifests,
        @JsonProperty("docker_configuration") DockerConfiguration dockerConfiguration,
        @JsonProperty("ci_cd_pipeline") CICDPipelineConfiguration ciCdPipeline,
        @JsonProperty("monitoring_configuration") MonitoringConfiguration monitoringConfiguration,
        @JsonProperty("secrets_configuration") SecretsConfiguration secretsConfiguration,
        @JsonProperty("scaling_configuration") ScalingConfiguration scalingConfiguration,
        @JsonProperty("health_checks") List<HealthCheckConfiguration> healthChecks,
        @JsonProperty("generated_at") Instant generatedAt
    ) {}

    // ===== AI Integration Models =====

    /**
     * AI model usage and insights
     */
    public record AIInsight(
        @JsonProperty("insight_id") String insightId,
        @JsonProperty("ai_model_used") String aiModelUsed,
        @JsonProperty("generation_phase") GenerationPhase generationPhase,
        @JsonProperty("insight_type") AIInsightType insightType,
        @JsonProperty("insight_content") String insightContent,
        @JsonProperty("confidence_score") double confidenceScore,
        @JsonProperty("processing_time_ms") long processingTimeMs,
        @JsonProperty("tokens_used") int tokensUsed,
        @JsonProperty("cost_estimation") double costEstimation,
        @JsonProperty("generated_at") Instant generatedAt
    ) {}

    /**
     * AI model configuration
     */
    public record AIModelConfiguration(
        @JsonProperty("model_id") String modelId,
        @JsonProperty("model_name") String modelName,
        @JsonProperty("model_type") AIModelType modelType,
        @JsonProperty("provider") AIProvider provider,
        @JsonProperty("api_endpoint") String apiEndpoint,
        @JsonProperty("model_version") String modelVersion,
        @JsonProperty("capabilities") List<AICapability> capabilities,
        @JsonProperty("cost_per_token") double costPerToken,
        @JsonProperty("rate_limits") RateLimits rateLimits,
        @JsonProperty("quality_metrics") AIQualityMetrics qualityMetrics,
        @JsonProperty("is_enabled") boolean isEnabled
    ) {}

    // ===== Supporting Data Structures =====

    /**
     * Generation metadata and tracking
     */
    public record GenerationMetadata(
        @JsonProperty("generation_id") String generationId,
        @JsonProperty("generation_phases") List<GenerationPhaseResult> generationPhases,
        @JsonProperty("ai_models_used") List<String> aiModelsUsed,
        @JsonProperty("templates_used") List<String> templatesUsed,
        @JsonProperty("total_tokens_used") int totalTokensUsed,
        @JsonProperty("total_cost") double totalCost,
        @JsonProperty("generation_statistics") GenerationStatistics generationStatistics,
        @JsonProperty("error_count") int errorCount,
        @JsonProperty("warnings_count") int warningsCount,
        @JsonProperty("optimization_applied") List<OptimizationApplied> optimizationsApplied
    ) {}

    /**
     * Generation phase result
     */
    public record GenerationPhaseResult(
        @JsonProperty("phase") GenerationPhase phase,
        @JsonProperty("status") PhaseStatus status,
        @JsonProperty("start_time") Instant startTime,
        @JsonProperty("end_time") Instant endTime,
        @JsonProperty("duration_ms") long durationMs,
        @JsonProperty("ai_model_used") String aiModelUsed,
        @JsonProperty("tokens_used") int tokensUsed,
        @JsonProperty("result_quality") double resultQuality,
        @JsonProperty("errors") List<GenerationError> errors,
        @JsonProperty("warnings") List<GenerationWarning> warnings
    ) {}

    /**
     * Build configuration for generated code
     */
    public record BuildConfiguration(
        @JsonProperty("build_system") BuildSystem buildSystem,
        @JsonProperty("build_files") List<GeneratedFile> buildFiles,
        @JsonProperty("dependencies") List<Dependency> dependencies,
        @JsonProperty("build_scripts") List<BuildScript> buildScripts,
        @JsonProperty("quality_gates") List<QualityGate> qualityGates,
        @JsonProperty("test_configuration") TestConfiguration testConfiguration
    ) {}

    // ===== Enumeration Types =====

    public enum GenerationStatus {
        PENDING, IN_PROGRESS, COMPLETED, FAILED, CANCELLED, TIMEOUT
    }

    public enum GenerationPriority {
        LOW, NORMAL, HIGH, URGENT, CRITICAL
    }

    public enum ProgrammingLanguage {
        PYTHON, JAVA, TYPESCRIPT, JAVASCRIPT, GO, RUST, KOTLIN, SCALA, CSHARP
    }

    public enum ArchitectureStyle {
        MICROSERVICES, MONOLITH, SERVERLESS, EVENT_DRIVEN, LAYERED, HEXAGONAL, CLEAN
    }

    public enum AgentType {
        AI_AGENT, DATA_AGENT, COMMUNICATION_AGENT, SECURITY_AGENT, MONITORING_AGENT, 
        INTEGRATION_AGENT, WORKFLOW_AGENT, ANALYSIS_AGENT, CUSTOM
    }

    public enum TemplateType {
        AGENT_TEMPLATE, FRAMEWORK_TEMPLATE, COMPONENT_TEMPLATE, INTEGRATION_TEMPLATE, 
        DEPLOYMENT_TEMPLATE, TEST_TEMPLATE, DOCUMENTATION_TEMPLATE
    }

    public enum FileType {
        SOURCE_CODE, CONFIGURATION, TEST, DOCUMENTATION, BUILD_SCRIPT, 
        DEPLOYMENT_MANIFEST, SCHEMA, TEMPLATE
    }

    public enum TestType {
        UNIT, INTEGRATION, PERFORMANCE, SECURITY, ACCEPTANCE, REGRESSION, SMOKE, LOAD
    }

    public enum GenerationPhase {
        REQUIREMENT_ANALYSIS, ARCHITECTURE_DESIGN, TEMPLATE_SELECTION, CODE_GENERATION,
        TEST_GENERATION, DOCUMENTATION_GENERATION, QUALITY_VALIDATION, OPTIMIZATION,
        DEPLOYMENT_CONFIGURATION, FINALIZATION
    }

    public enum AIModelType {
        LARGE_LANGUAGE_MODEL, CODE_GENERATION_MODEL, EMBEDDING_MODEL, 
        CLASSIFICATION_MODEL, OPTIMIZATION_MODEL
    }

    public enum AIProvider {
        OPENAI, ANTHROPIC, GOOGLE, GITHUB, HUGGINGFACE, CUSTOM
    }

    public enum DeploymentStrategy {
        BLUE_GREEN, ROLLING, CANARY, RECREATE, IMMEDIATE
    }

    public enum DeploymentEnvironment {
        DEVELOPMENT, TESTING, STAGING, PRODUCTION, LOCAL
    }

    public enum BuildSystem {
        MAVEN, GRADLE, NPM, YARN, POETRY, GO_MODULES, BAZEL, MAKE
    }

    public enum PhaseStatus {
        PENDING, RUNNING, COMPLETED, FAILED, SKIPPED
    }

    public enum AIInsightType {
        OPTIMIZATION_SUGGESTION, SECURITY_RECOMMENDATION, PERFORMANCE_INSIGHT,
        ARCHITECTURE_ADVICE, CODE_IMPROVEMENT, TESTING_RECOMMENDATION
    }

    public enum OptimizationFocus {
        PERFORMANCE, SECURITY, MAINTAINABILITY, SCALABILITY, COST, ENERGY_EFFICIENCY
    }

    public enum DocumentationLevel {
        MINIMAL, STANDARD, COMPREHENSIVE, EXTENSIVE
    }

    public enum TestingApproach {
        TDD, BDD, TRADITIONAL, AUTOMATED_ONLY, MANUAL_ONLY, HYBRID
    }

    public enum CodeStyle {
        GOOGLE, AIRBNB, STANDARD, PRETTIER, BLACK, CUSTOM
    }

    public enum DeploymentTarget {
        KUBERNETES, DOCKER, SERVERLESS, BARE_METAL, CLOUD_NATIVE, EDGE
    }

    public enum VariableType {
        STRING, INTEGER, BOOLEAN, ARRAY, OBJECT, ENUM, DATE, URL, EMAIL
    }

    public enum AICapability {
        TEXT_GENERATION, CODE_GENERATION, CODE_COMPLETION, CODE_REVIEW,
        ARCHITECTURE_DESIGN, TEST_GENERATION, DOCUMENTATION_GENERATION,
        OPTIMIZATION, SECURITY_ANALYSIS, PERFORMANCE_ANALYSIS
    }

    // ===== Additional Supporting Records =====

    public record FunctionalRequirement(
        @JsonProperty("requirement_id") String requirementId,
        @JsonProperty("description") String description,
        @JsonProperty("priority") RequirementPriority priority,
        @JsonProperty("acceptance_criteria") List<String> acceptanceCriteria
    ) {}

    public record NonFunctionalRequirement(
        @JsonProperty("requirement_id") String requirementId,
        @JsonProperty("type") NFRType type,
        @JsonProperty("description") String description,
        @JsonProperty("target_value") String targetValue,
        @JsonProperty("measurement_criteria") String measurementCriteria
    ) {}

    public record PerformanceRequirements(
        @JsonProperty("max_response_time_ms") int maxResponseTimeMs,
        @JsonProperty("min_throughput_rps") int minThroughputRps,
        @JsonProperty("max_memory_usage_mb") int maxMemoryUsageMb,
        @JsonProperty("max_cpu_usage_percent") int maxCpuUsagePercent
    ) {}

    public record QualityRequirements(
        @JsonProperty("min_code_coverage_percent") double minCodeCoveragePercent,
        @JsonProperty("max_complexity_score") double maxComplexityScore,
        @JsonProperty("security_scan_required") boolean securityScanRequired,
        @JsonProperty("performance_test_required") boolean performanceTestRequired,
        @JsonProperty("documentation_required") boolean documentationRequired
    ) {}

    public record DeploymentRequirements(
        @JsonProperty("target_platform") String targetPlatform,
        @JsonProperty("scaling_requirements") ScalingRequirements scalingRequirements,
        @JsonProperty("availability_requirements") AvailabilityRequirements availabilityRequirements,
        @JsonProperty("security_requirements") List<SecurityRequirement> securityRequirements
    ) {}

    public record GenerationContext(
        @JsonProperty("project_context") String projectContext,
        @JsonProperty("existing_agents") List<String> existingAgents,
        @JsonProperty("integration_points") List<String> integrationPoints,
        @JsonProperty("environment_constraints") Map<String, String> environmentConstraints
    ) {}

    public record GenerationConstraint(
        @JsonProperty("constraint_type") String constraintType,
        @JsonProperty("constraint_value") String constraintValue,
        @JsonProperty("is_hard_constraint") boolean isHardConstraint
    ) {}

    public record ComponentSpecification(
        @JsonProperty("component_name") String componentName,
        @JsonProperty("component_type") String componentType,
        @JsonProperty("responsibilities") List<String> responsibilities,
        @JsonProperty("interfaces") List<String> interfaces,
        @JsonProperty("dependencies") List<String> dependencies
    ) {}

    public record PackageStructure(
        @JsonProperty("root_package") String rootPackage,
        @JsonProperty("package_hierarchy") Map<String, List<String>> packageHierarchy,
        @JsonProperty("file_organization") Map<String, List<String>> fileOrganization
    ) {}

    public record CodeMetrics(
        @JsonProperty("total_lines") int totalLines,
        @JsonProperty("source_lines") int sourceLines,
        @JsonProperty("comment_lines") int commentLines,
        @JsonProperty("blank_lines") int blankLines,
        @JsonProperty("complexity_score") double complexityScore,
        @JsonProperty("maintainability_index") double maintainabilityIndex
    ) {}

    public record TestCoverageReport(
        @JsonProperty("line_coverage_percent") double lineCoveragePercent,
        @JsonProperty("branch_coverage_percent") double branchCoveragePercent,
        @JsonProperty("function_coverage_percent") double functionCoveragePercent,
        @JsonProperty("statement_coverage_percent") double statementCoveragePercent,
        @JsonProperty("meets_requirements") boolean meetsRequirements
    ) {}

    public record GenerationStatistics(
        @JsonProperty("total_files_generated") int totalFilesGenerated,
        @JsonProperty("total_lines_generated") int totalLinesGenerated,
        @JsonProperty("languages_used") List<ProgrammingLanguage> languagesUsed,
        @JsonProperty("templates_applied") int templatesApplied,
        @JsonProperty("optimizations_applied") int optimizationsApplied
    ) {}

    public record Dependency(
        @JsonProperty("name") String name,
        @JsonProperty("version") String version,
        @JsonProperty("scope") String scope,
        @JsonProperty("type") String type
    ) {}

    public record BuildScript(
        @JsonProperty("script_name") String scriptName,
        @JsonProperty("script_content") String scriptContent,
        @JsonProperty("execution_order") int executionOrder
    ) {}

    public record QualityGate(
        @JsonProperty("gate_name") String gateName,
        @JsonProperty("metric_name") String metricName,
        @JsonProperty("threshold_value") double thresholdValue,
        @JsonProperty("is_blocking") boolean isBlocking
    ) {}

    public record RateLimits(
        @JsonProperty("requests_per_minute") int requestsPerMinute,
        @JsonProperty("tokens_per_day") int tokensPerDay,
        @JsonProperty("concurrent_requests") int concurrentRequests
    ) {}

    public record AIQualityMetrics(
        @JsonProperty("accuracy_score") double accuracyScore,
        @JsonProperty("latency_p95_ms") long latencyP95Ms,
        @JsonProperty("success_rate") double successRate,
        @JsonProperty("cost_efficiency") double costEfficiency
    ) {}

    public record GenerationError(
        @JsonProperty("error_code") String errorCode,
        @JsonProperty("error_message") String errorMessage,
        @JsonProperty("error_type") String errorType,
        @JsonProperty("context") Map<String, Object> context
    ) {}

    public record GenerationWarning(
        @JsonProperty("warning_code") String warningCode,
        @JsonProperty("warning_message") String warningMessage,
        @JsonProperty("severity") String severity
    ) {}

    public record OptimizationApplied(
        @JsonProperty("optimization_type") String optimizationType,
        @JsonProperty("description") String description,
        @JsonProperty("impact_score") double impactScore
    ) {}

    public record TestScenario(
        @JsonProperty("scenario_name") String scenarioName,
        @JsonProperty("scenario_description") String scenarioDescription,
        @JsonProperty("test_steps") List<String> testSteps,
        @JsonProperty("expected_outcome") String expectedOutcome
    ) {}

    public record TestConfiguration(
        @JsonProperty("test_framework") String testFramework,
        @JsonProperty("test_runner") String testRunner,
        @JsonProperty("coverage_tool") String coverageTool,
        @JsonProperty("mock_framework") String mockFramework
    ) {}

    public record ValidationRule(
        @JsonProperty("rule_name") String ruleName,
        @JsonProperty("rule_expression") String ruleExpression,
        @JsonProperty("error_message") String errorMessage
    ) {}

    public record ConditionalGeneration(
        @JsonProperty("condition") String condition,
        @JsonProperty("condition_type") String conditionType,
        @JsonProperty("condition_value") Object conditionValue
    ) {}

    public record TemplateUsageStatistics(
        @JsonProperty("usage_count") int usageCount,
        @JsonProperty("success_rate") double successRate,
        @JsonProperty("average_quality_score") double averageQualityScore,
        @JsonProperty("last_used") Instant lastUsed
    ) {}

    public record TemplateConstraint(
        @JsonProperty("constraint_name") String constraintName,
        @JsonProperty("constraint_description") String constraintDescription,
        @JsonProperty("constraint_rule") String constraintRule
    ) {}

    public record SecurityAnalysisResult(
        @JsonProperty("security_score") double securityScore,
        @JsonProperty("vulnerabilities_found") int vulnerabilitiesFound,
        @JsonProperty("security_issues") List<SecurityIssue> securityIssues,
        @JsonProperty("compliance_status") Map<String, Boolean> complianceStatus
    ) {}

    public record SecurityIssue(
        @JsonProperty("issue_id") String issueId,
        @JsonProperty("severity") SecuritySeverity severity,
        @JsonProperty("description") String description,
        @JsonProperty("file_location") String fileLocation,
        @JsonProperty("remediation") String remediation
    ) {}

    public record PerformanceAnalysisResult(
        @JsonProperty("performance_score") double performanceScore,
        @JsonProperty("bottlenecks_identified") List<PerformanceBottleneck> bottlenecks,
        @JsonProperty("optimization_opportunities") List<OptimizationOpportunity> optimizationOpportunities
    ) {}

    public record PerformanceBottleneck(
        @JsonProperty("location") String location,
        @JsonProperty("type") String type,
        @JsonProperty("impact_score") double impactScore,
        @JsonProperty("recommendation") String recommendation
    ) {}

    public record OptimizationOpportunity(
        @JsonProperty("opportunity_type") String opportunityType,
        @JsonProperty("description") String description,
        @JsonProperty("expected_improvement") String expectedImprovement
    ) {}

    public record DocumentationQualityReport(
        @JsonProperty("completeness_score") double completenessScore,
        @JsonProperty("clarity_score") double clarityScore,
        @JsonProperty("coverage_percentage") double coveragePercentage,
        @JsonProperty("missing_documentation") List<String> missingDocumentation
    ) {}

    public record ComplianceCheckResult(
        @JsonProperty("overall_compliance") boolean overallCompliance,
        @JsonProperty("compliance_items") Map<String, Boolean> complianceItems,
        @JsonProperty("violations") List<ComplianceViolation> violations
    ) {}

    public record ComplianceViolation(
        @JsonProperty("rule_name") String ruleName,
        @JsonProperty("description") String description,
        @JsonProperty("severity") ComplianceSeverity severity,
        @JsonProperty("remediation") String remediation
    ) {}

    public record ImprovementSuggestion(
        @JsonProperty("suggestion_type") String suggestionType,
        @JsonProperty("description") String description,
        @JsonProperty("priority") SuggestionPriority priority,
        @JsonProperty("estimated_effort") String estimatedEffort,
        @JsonProperty("expected_benefit") String expectedBenefit
    ) {}

    // Additional enums for completeness
    
    public enum RequirementPriority {
        MUST_HAVE, SHOULD_HAVE, COULD_HAVE, WONT_HAVE
    }

    public enum NFRType {
        PERFORMANCE, SECURITY, USABILITY, RELIABILITY, SCALABILITY, 
        MAINTAINABILITY, PORTABILITY, AVAILABILITY
    }

    public enum SecuritySeverity {
        CRITICAL, HIGH, MEDIUM, LOW, INFO
    }

    public enum ComplianceSeverity {
        CRITICAL, MAJOR, MINOR, INFO
    }

    public enum SuggestionPriority {
        CRITICAL, HIGH, MEDIUM, LOW
    }

    public record AgentArchitecture(
        @JsonProperty("architecture_type") ArchitectureStyle architectureType,
        @JsonProperty("layers") List<ArchitectureLayer> layers,
        @JsonProperty("patterns") List<ArchitecturePattern> patterns,
        @JsonProperty("technologies") List<Technology> technologies
    ) {}

    public record ArchitectureLayer(
        @JsonProperty("layer_name") String layerName,
        @JsonProperty("layer_type") String layerType,
        @JsonProperty("responsibilities") List<String> responsibilities
    ) {}

    public record ArchitecturePattern(
        @JsonProperty("pattern_name") String patternName,
        @JsonProperty("pattern_type") String patternType,
        @JsonProperty("implementation_details") String implementationDetails
    ) {}

    public record Technology(
        @JsonProperty("technology_name") String technologyName,
        @JsonProperty("technology_type") String technologyType,
        @JsonProperty("version") String version,
        @JsonProperty("purpose") String purpose
    ) {}

    public record InterfaceSpecification(
        @JsonProperty("interface_name") String interfaceName,
        @JsonProperty("interface_type") String interfaceType,
        @JsonProperty("methods") List<MethodSpecification> methods,
        @JsonProperty("data_contracts") List<DataContract> dataContracts
    ) {}

    public record MethodSpecification(
        @JsonProperty("method_name") String methodName,
        @JsonProperty("parameters") List<Parameter> parameters,
        @JsonProperty("return_type") String returnType,
        @JsonProperty("description") String description
    ) {}

    public record Parameter(
        @JsonProperty("parameter_name") String parameterName,
        @JsonProperty("parameter_type") String parameterType,
        @JsonProperty("is_required") boolean isRequired,
        @JsonProperty("description") String description
    ) {}

    public record DataContract(
        @JsonProperty("contract_name") String contractName,
        @JsonProperty("schema") String schema,
        @JsonProperty("validation_rules") List<String> validationRules
    ) {}

    public record DependencySpecification(
        @JsonProperty("dependency_name") String dependencyName,
        @JsonProperty("dependency_type") String dependencyType,
        @JsonProperty("version_constraint") String versionConstraint,
        @JsonProperty("is_optional") boolean isOptional
    ) {}

    public record CapabilitySpecification(
        @JsonProperty("capability_name") String capabilityName,
        @JsonProperty("capability_type") String capabilityType,
        @JsonProperty("implementation_approach") String implementationApproach,
        @JsonProperty("performance_characteristics") String performanceCharacteristics
    ) {}

    public record AIIntegrationSpecification(
        @JsonProperty("ai_models_required") List<String> aiModelsRequired,
        @JsonProperty("ai_capabilities") List<String> aiCapabilities,
        @JsonProperty("data_requirements") List<String> dataRequirements,
        @JsonProperty("training_requirements") String trainingRequirements
    ) {}

    public record DataModelSpecification(
        @JsonProperty("model_name") String modelName,
        @JsonProperty("model_type") String modelType,
        @JsonProperty("fields") List<FieldSpecification> fields,
        @JsonProperty("relationships") List<RelationshipSpecification> relationships
    ) {}

    public record FieldSpecification(
        @JsonProperty("field_name") String fieldName,
        @JsonProperty("field_type") String fieldType,
        @JsonProperty("is_required") boolean isRequired,
        @JsonProperty("validation_rules") List<String> validationRules
    ) {}

    public record RelationshipSpecification(
        @JsonProperty("relationship_name") String relationshipName,
        @JsonProperty("relationship_type") String relationshipType,
        @JsonProperty("target_model") String targetModel,
        @JsonProperty("cardinality") String cardinality
    ) {}

    public record ConfigurationSpecification(
        @JsonProperty("configuration_items") List<ConfigurationItem> configurationItems,
        @JsonProperty("environment_specific") Map<String, Map<String, String>> environmentSpecific,
        @JsonProperty("secrets_required") List<String> secretsRequired
    ) {}

    public record ConfigurationItem(
        @JsonProperty("key") String key,
        @JsonProperty("value_type") String valueType,
        @JsonProperty("default_value") String defaultValue,
        @JsonProperty("description") String description,
        @JsonProperty("is_secret") boolean isSecret
    ) {}

    public record MonitoringSpecification(
        @JsonProperty("metrics_to_collect") List<String> metricsToCollect,
        @JsonProperty("health_checks") List<String> healthChecks,
        @JsonProperty("alerting_rules") List<AlertingRule> alertingRules,
        @JsonProperty("logging_configuration") LoggingConfiguration loggingConfiguration
    ) {}

    public record AlertingRule(
        @JsonProperty("rule_name") String ruleName,
        @JsonProperty("condition") String condition,
        @JsonProperty("severity") String severity,
        @JsonProperty("notification_channels") List<String> notificationChannels
    ) {}

    public record LoggingConfiguration(
        @JsonProperty("log_level") String logLevel,
        @JsonProperty("log_format") String logFormat,
        @JsonProperty("log_aggregation") String logAggregation
    ) {}

    public record IntegrationCodeResult(
        @JsonProperty("a2a_integration") List<GeneratedFile> a2aIntegration,
        @JsonProperty("api_clients") List<GeneratedFile> apiClients,
        @JsonProperty("message_handlers") List<GeneratedFile> messageHandlers,
        @JsonProperty("configuration_files") List<GeneratedFile> configurationFiles
    ) {}

    public record ProjectStructure(
        @JsonProperty("root_directory") String rootDirectory,
        @JsonProperty("directory_structure") Map<String, List<String>> directoryStructure,
        @JsonProperty("key_files") List<String> keyFiles,
        @JsonProperty("readme_content") String readmeContent
    ) {}

    public record DocumentationResult(
        @JsonProperty("api_documentation") GeneratedFile apiDocumentation,
        @JsonProperty("user_guide") GeneratedFile userGuide,
        @JsonProperty("developer_guide") GeneratedFile developerGuide,
        @JsonProperty("architecture_documentation") GeneratedFile architectureDocumentation,
        @JsonProperty("readme_file") GeneratedFile readmeFile,
        @JsonProperty("changelog") GeneratedFile changelog
    ) {}

    public record DockerConfiguration(
        @JsonProperty("dockerfile") GeneratedFile dockerfile,
        @JsonProperty("docker_compose") GeneratedFile dockerCompose,
        @JsonProperty("build_args") Map<String, String> buildArgs,
        @JsonProperty("environment_variables") Map<String, String> environmentVariables
    ) {}

    public record CICDPipelineConfiguration(
        @JsonProperty("pipeline_type") String pipelineType,
        @JsonProperty("pipeline_file") GeneratedFile pipelineFile,
        @JsonProperty("build_stages") List<BuildStage> buildStages,
        @JsonProperty("deployment_stages") List<DeploymentStage> deploymentStages,
        @JsonProperty("quality_gates") List<QualityGate> qualityGates
    ) {}

    public record BuildStage(
        @JsonProperty("stage_name") String stageName,
        @JsonProperty("stage_order") int stageOrder,
        @JsonProperty("commands") List<String> commands,
        @JsonProperty("artifacts") List<String> artifacts
    ) {}

    public record DeploymentStage(
        @JsonProperty("stage_name") String stageName,
        @JsonProperty("environment") String environment,
        @JsonProperty("deployment_strategy") String deploymentStrategy,
        @JsonProperty("approval_required") boolean approvalRequired
    ) {}

    public record MonitoringConfiguration(
        @JsonProperty("prometheus_config") GeneratedFile prometheusConfig,
        @JsonProperty("grafana_dashboards") List<GeneratedFile> grafanaDashboards,
        @JsonProperty("alerting_rules") GeneratedFile alertingRules,
        @JsonProperty("log_configuration") GeneratedFile logConfiguration
    ) {}

    public record SecretsConfiguration(
        @JsonProperty("secrets_list") List<SecretDefinition> secretsList,
        @JsonProperty("secret_management_approach") String secretManagementApproach,
        @JsonProperty("encryption_requirements") String encryptionRequirements
    ) {}

    public record SecretDefinition(
        @JsonProperty("secret_name") String secretName,
        @JsonProperty("secret_type") String secretType,
        @JsonProperty("description") String description,
        @JsonProperty("rotation_policy") String rotationPolicy
    ) {}

    public record ScalingConfiguration(
        @JsonProperty("auto_scaling_enabled") boolean autoScalingEnabled,
        @JsonProperty("min_replicas") int minReplicas,
        @JsonProperty("max_replicas") int maxReplicas,
        @JsonProperty("scaling_metrics") List<ScalingMetric> scalingMetrics
    ) {}

    public record ScalingMetric(
        @JsonProperty("metric_name") String metricName,
        @JsonProperty("target_value") double targetValue,
        @JsonProperty("metric_type") String metricType
    ) {}

    public record HealthCheckConfiguration(
        @JsonProperty("check_name") String checkName,
        @JsonProperty("check_type") String checkType,
        @JsonProperty("endpoint") String endpoint,
        @JsonProperty("interval_seconds") int intervalSeconds,
        @JsonProperty("timeout_seconds") int timeoutSeconds,
        @JsonProperty("failure_threshold") int failureThreshold
    ) {}

    public record FrameworkPreference(
        @JsonProperty("framework_name") String frameworkName,
        @JsonProperty("framework_type") String frameworkType,
        @JsonProperty("preference_weight") double preferenceWeight,
        @JsonProperty("specific_version") String specificVersion
    ) {}

    public record IntegrationRequirement(
        @JsonProperty("integration_type") String integrationType,
        @JsonProperty("target_system") String targetSystem,
        @JsonProperty("integration_pattern") String integrationPattern,
        @JsonProperty("data_format") String dataFormat
    ) {}

    public record AICapabilityRequirement(
        @JsonProperty("capability_name") String capabilityName,
        @JsonProperty("capability_type") String capabilityType,
        @JsonProperty("performance_requirements") String performanceRequirements,
        @JsonProperty("data_requirements") String dataRequirements
    ) {}

    public record DataRequirement(
        @JsonProperty("data_type") String dataType,
        @JsonProperty("data_source") String dataSource,
        @JsonProperty("data_volume") String dataVolume,
        @JsonProperty("data_quality_requirements") String dataQualityRequirements
    ) {}

    public record SecurityRequirement(
        @JsonProperty("security_control") String securityControl,
        @JsonProperty("requirement_level") String requirementLevel,
        @JsonProperty("compliance_standard") String complianceStandard
    ) {}

    public record ScalabilityRequirements(
        @JsonProperty("horizontal_scaling") boolean horizontalScaling,
        @JsonProperty("vertical_scaling") boolean verticalScaling,
        @JsonProperty("expected_load") String expectedLoad,
        @JsonProperty("performance_targets") String performanceTargets
    ) {}

    public record AvailabilityRequirements(
        @JsonProperty("uptime_percentage") double uptimePercentage,
        @JsonProperty("rto_minutes") int rtoMinutes,
        @JsonProperty("rpo_minutes") int rpoMinutes,
        @JsonProperty("disaster_recovery_required") boolean disasterRecoveryRequired
    ) {}

    public record ScalingRequirements(
        @JsonProperty("auto_scaling") boolean autoScaling,
        @JsonProperty("load_balancing") boolean loadBalancing,
        @JsonProperty("clustering") boolean clustering,
        @JsonProperty("geographic_distribution") boolean geographicDistribution
    ) {}
}