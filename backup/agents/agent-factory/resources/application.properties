# Agent Factory Agent Configuration
server.port=8087

# PostgreSQL Database Configuration
spring.datasource.url=*************************************************
spring.datasource.driverClassName=org.postgresql.Driver
spring.datasource.username=${DB_USERNAME:koneti}
spring.datasource.password=${DB_PASSWORD:}
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=true

# Redis Configuration
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.timeout=2000ms

# Management and Actuator
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=always
management.metrics.export.prometheus.enabled=true

# Agent Factory Specific
agent.factory.code.generation.enabled=true
agent.factory.deployment.enabled=true
agent.factory.testing.enabled=true
agent.factory.ai.enabled=true
agent.factory.ai.service.url=http://localhost:9091

# Factory Configuration
factory.template.repository.enabled=true
factory.template.repository.path=/tmp/agent-templates
factory.build.timeout=600000
factory.test.timeout=300000

# Logging
logging.level.ai.twodot.platform.agents.agentfactory=INFO
logging.level.org.springframework.web=INFO
logging.level.root=INFO