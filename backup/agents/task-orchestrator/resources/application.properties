# Task Orchestrator Agent Configuration
server.port=8086

# PostgreSQL Database Configuration
spring.datasource.url=*****************************************************
spring.datasource.driverClassName=org.postgresql.Driver
spring.datasource.username=${DB_USERNAME:koneti}
spring.datasource.password=${DB_PASSWORD:}
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=true

# Redis Configuration
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.timeout=2000ms

# Management and Actuator
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=always
management.metrics.export.prometheus.enabled=true

# Task Orchestrator Specific
task.orchestrator.workflow.enabled=true
task.orchestrator.parallel.execution.enabled=true
task.orchestrator.priority.scheduling.enabled=true
task.orchestrator.ai.enabled=true
task.orchestrator.ai.service.url=http://localhost:9086

# Workflow Configuration
workflow.engine.enabled=true
workflow.parallel.max.threads=10
workflow.timeout.default=300000

# Disable observations to avoid tracing issues
management.observations.web.enabled=false
management.tracing.enabled=false

# Logging
logging.level.ai.twodot.platform.agents.taskorchestrator=INFO
logging.level.org.springframework.web=INFO
logging.level.root=INFO
