package com.multiagent.platform.agents.security.ai.twodot.platform.agents.securitymonitor.security;

/**
 * Threat Types - Enumeration of possible threat types
 */
public enum ThreatType {
    // Network-based threats
    NETWORK_INTRUSION("Network intrusion detected"),
    DDoS_ATTACK("Distributed Denial of Service attack"),
    POR<PERSON>_SCAN("Port scanning activity"),
    SUSPICIOUS_TRAFFIC("Suspicious network traffic"),
    
    // Application threats
    SQL_INJECTION("SQL injection attempt"),
    XSS_ATTACK("Cross-site scripting attack"),
    CSRF_ATTACK("Cross-site request forgery"),
    FILE_UPLOAD_THREAT("Malicious file upload"),
    
    // Authentication threats
    BRUTE_FORCE_ATTACK("Brute force login attempt"),
    CREDENTIAL_STUFFING("Credential stuffing attack"),
    ACCOUNT_TAKEOVER("Account takeover attempt"),
    
    // Data threats
    DATA_EXFILTRATION("Data exfiltration attempt"),
    UNAUTHORIZED_ACCESS("Unauthorized data access"),
    <PERSON><PERSON><PERSON>_BREACH("Data breach detected"),
    
    // Malware threats
    MALWARE_DETECTED("Malware detected"),
    RANSOMWARE("Ransomware activity"),
    TROJAN("Trojan horse detected"),
    VIRUS("Virus detected"),
    
    // Behavioral threats
    ANOMALOUS_BEHAVIOR("Anomalous user behavior"),
    INSIDER_THREAT("Potential insider threat"),
    PRIVILEGE_ABUSE("Privilege abuse detected"),
    
    // Advanced threats
    APT_ACTIVITY("Advanced Persistent Threat activity"),
    ZERO_DAY_EXPLOIT("Zero-day exploit attempt"),
    LATERAL_MOVEMENT("Lateral movement detected"),
    
    // System threats
    SYSTEM_COMPROMISE("System compromise detected"),
    CONFIGURATION_TAMPERING("Configuration tampering"),
    
    // Normal/Safe
    NORMAL("Normal activity"),
    FALSE_POSITIVE("False positive"),
    
    // Errors
    ANALYSIS_ERROR("Error in threat analysis"),
    UNKNOWN("Unknown threat type");
    
    private final String description;
    
    ThreatType(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * Check if this is a high-severity threat type
     */
    public boolean isHighSeverity() {
        return this == NETWORK_INTRUSION || 
               this == DDoS_ATTACK ||
               this == SQL_INJECTION ||
               this == DATA_EXFILTRATION ||
               this == DATA_BREACH ||
               this == RANSOMWARE ||
               this == APT_ACTIVITY ||
               this == ZERO_DAY_EXPLOIT ||
               this == SYSTEM_COMPROMISE;
    }
    
    /**
     * Check if this threat requires immediate response
     */
    public boolean requiresImmediateResponse() {
        return this == RANSOMWARE ||
               this == DATA_BREACH ||
               this == SYSTEM_COMPROMISE ||
               this == APT_ACTIVITY ||
               this == ZERO_DAY_EXPLOIT;
    }
}