package com.multiagent.platform.agents.security.ai.twodot.platform.agents.securitymonitor.integration;

import com.multiagent.platform.agents.securitymonitor.security.SecurityEvent;
import com.multiagent.platform.agents.securitymonitor.security.ThreatAnalysisResult;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Security Event Streaming Service
 * 
 * Handles real-time streaming of security events to other agents
 * via A2A protocol through the Communication Broker Agent.
 */
@Component
public class SecurityEventStreamer {

    private static final Logger logger = LoggerFactory.getLogger(SecurityEventStreamer.class);

    @Autowired
    private CommunicationBrokerClient cbaClient;

    @Value("${sma.streaming.enabled:true}")
    private boolean streamingEnabled;

    @Value("${sma.streaming.batch.size:10}")
    private int batchSize;

    @Value("${sma.streaming.batch.timeout:5000}")
    private int batchTimeoutMs;

    @Value("${sma.streaming.buffer.size:1000}")
    private int bufferSize;

    @Value("${sma.agent.id:SMA-003}")
    private String agentId;

    // Streaming infrastructure
    private final BlockingQueue<SecurityEventStreamItem> eventQueue = new LinkedBlockingQueue<>();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);
    private final ExecutorService batchProcessor = Executors.newFixedThreadPool(3);
    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    
    // Metrics
    private final AtomicLong eventsStreamed = new AtomicLong(0);
    private final AtomicLong batchesProcessed = new AtomicLong(0);
    private final AtomicLong streamingErrors = new AtomicLong(0);
    private final AtomicLong queueOverflows = new AtomicLong(0);

    // Batch processing
    private final List<SecurityEventStreamItem> currentBatch = new CopyOnWriteArrayList<>();
    private volatile Instant lastBatchTime = Instant.now();

    @PostConstruct
    public void initialize() {
        if (streamingEnabled) {
            logger.info("Initializing Security Event Streamer...");
            isRunning.set(true);
            startEventStreaming();
            logger.info("Security Event Streamer initialized and running");
        } else {
            logger.info("Security Event Streaming is disabled");
        }
    }

    @PreDestroy
    public void shutdown() {
        logger.info("Shutting down Security Event Streamer...");
        isRunning.set(false);
        
        // Process remaining events
        processRemainingEvents();
        
        // Shutdown executors
        scheduler.shutdown();
        batchProcessor.shutdown();
        
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
            if (!batchProcessor.awaitTermination(5, TimeUnit.SECONDS)) {
                batchProcessor.shutdownNow();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn("Interrupted while shutting down executor services");
        }
        
        logger.info("Security Event Streamer shutdown complete");
    }

    /**
     * Stream security event to other agents
     */
    public CompletableFuture<StreamingResponse> streamSecurityEvent(SecurityEvent event, ThreatAnalysisResult analysis) {
        return streamEvent(event, analysis, StreamEventType.THREAT_DETECTION);
    }

    /**
     * Stream security alert to other agents
     */
    public CompletableFuture<StreamingResponse> streamSecurityAlert(SecurityEvent event, ThreatAnalysisResult analysis) {
        return streamEvent(event, analysis, StreamEventType.SECURITY_ALERT);
    }

    /**
     * Stream incident notification to other agents
     */
    public CompletableFuture<StreamingResponse> streamSecurityIncident(SecurityEvent event, ThreatAnalysisResult analysis) {
        return streamEvent(event, analysis, StreamEventType.SECURITY_INCIDENT);
    }

    /**
     * Stream threat intelligence update to other agents
     */
    public CompletableFuture<StreamingResponse> streamThreatIntelligence(Map<String, Object> intelligence) {
        SecurityEventStreamItem item = new SecurityEventStreamItem(
            UUID.randomUUID().toString(),
            StreamEventType.THREAT_INTELLIGENCE,
            null,
            null,
            intelligence,
            Instant.now(),
            Map.of(
                "source", agentId,
                "intelligence_type", intelligence.getOrDefault("type", "unknown"),
                "confidence", intelligence.getOrDefault("confidence", 0.8)
            )
        );

        return queueEvent(item);
    }

    /**
     * Stream generic security event
     */
    private CompletableFuture<StreamingResponse> streamEvent(SecurityEvent event, ThreatAnalysisResult analysis, StreamEventType eventType) {
        if (!streamingEnabled) {
            return CompletableFuture.completedFuture(StreamingResponse.disabled());
        }

        SecurityEventStreamItem item = new SecurityEventStreamItem(
            UUID.randomUUID().toString(),
            eventType,
            event,
            analysis,
            Map.of(
                "event_summary", event.getSummary(),
                "threat_type", analysis != null ? analysis.threatType() : "none",
                "risk_level", analysis != null ? analysis.riskLevel() : "unknown",
                "confidence", analysis != null ? analysis.confidenceScore() : 0.0
            ),
            Instant.now(),
            Map.of(
                "agent_id", agentId,
                "processing_time_ms", analysis != null ? analysis.processingTimeMs() : 0,
                "analysis_method", analysis != null ? analysis.analysisMethod() : "rule_based"
            )
        );

        return queueEvent(item);
    }

    /**
     * Queue event for streaming
     */
    private CompletableFuture<StreamingResponse> queueEvent(SecurityEventStreamItem item) {
        try {
            if (eventQueue.size() >= bufferSize) {
                queueOverflows.incrementAndGet();
                logger.warn("Event queue overflow, dropping oldest events");
                eventQueue.poll(); // Remove oldest event
            }

            boolean queued = eventQueue.offer(item);
            if (queued) {
                logger.debug("Security event queued for streaming: {}", item.eventType());
                return CompletableFuture.completedFuture(StreamingResponse.queued());
            } else {
                streamingErrors.incrementAndGet();
                return CompletableFuture.completedFuture(StreamingResponse.error("Failed to queue event"));
            }

        } catch (Exception e) {
            streamingErrors.incrementAndGet();
            logger.error("Error queuing security event: {}", e.getMessage());
            return CompletableFuture.completedFuture(StreamingResponse.error("Queue error: " + e.getMessage()));
        }
    }

    /**
     * Start event streaming processes
     */
    private void startEventStreaming() {
        // Event batch processor
        scheduler.scheduleAtFixedRate(this::processBatch, 1, 1, TimeUnit.SECONDS);
        
        // Batch timeout processor
        scheduler.scheduleAtFixedRate(this::checkBatchTimeout, 100, 100, TimeUnit.MILLISECONDS);
        
        // Queue monitor
        scheduler.scheduleAtFixedRate(this::monitorQueue, 10, 10, TimeUnit.SECONDS);
    }

    /**
     * Process events in batches
     */
    private void processBatch() {
        if (!isRunning.get()) return;

        try {
            // Drain events from queue into current batch
            SecurityEventStreamItem item;
            while ((item = eventQueue.poll()) != null && currentBatch.size() < batchSize) {
                currentBatch.add(item);
            }

            // Process batch if it reaches size limit or timeout
            if (currentBatch.size() >= batchSize || shouldProcessBatchByTimeout()) {
                if (!currentBatch.isEmpty()) {
                    List<SecurityEventStreamItem> batchToProcess = List.copyOf(currentBatch);
                    currentBatch.clear();
                    lastBatchTime = Instant.now();
                    
                    // Process batch asynchronously
                    batchProcessor.submit(() -> processBatchAsync(batchToProcess));
                }
            }

        } catch (Exception e) {
            logger.error("Error processing event batch: {}", e.getMessage());
            streamingErrors.incrementAndGet();
        }
    }

    /**
     * Check if batch should be processed due to timeout
     */
    private boolean shouldProcessBatchByTimeout() {
        return !currentBatch.isEmpty() && 
               Instant.now().toEpochMilli() - lastBatchTime.toEpochMilli() > batchTimeoutMs;
    }

    /**
     * Check batch timeout and process if needed
     */
    private void checkBatchTimeout() {
        if (shouldProcessBatchByTimeout()) {
            processBatch();
        }
    }

    /**
     * Process batch of events asynchronously
     */
    private void processBatchAsync(List<SecurityEventStreamItem> batch) {
        try {
            logger.debug("Processing batch of {} security events", batch.size());

            SecurityEventBatch eventBatch = new SecurityEventBatch(
                UUID.randomUUID().toString(),
                batch,
                batch.size(),
                Instant.now(),
                Map.of(
                    "batch_processor", agentId,
                    "processing_strategy", "a2a_broadcast"
                )
            );

            // Send batch via A2A protocol to all interested agents
            CompletableFuture<CommunicationBrokerClient.MessageResponse> response = 
                cbaClient.sendBroadcastMessage("security_event_batch", eventBatch);

            response.thenAccept(result -> {
                if (result.success()) {
                    eventsStreamed.addAndGet(batch.size());
                    batchesProcessed.incrementAndGet();
                    logger.debug("Successfully streamed batch of {} events", batch.size());
                } else {
                    streamingErrors.incrementAndGet();
                    logger.error("Failed to stream event batch: {}", result.error());
                }
            }).exceptionally(throwable -> {
                streamingErrors.incrementAndGet();
                logger.error("Exception streaming event batch: {}", throwable.getMessage());
                return null;
            });

        } catch (Exception e) {
            streamingErrors.incrementAndGet();
            logger.error("Error processing event batch: {}", e.getMessage());
        }
    }

    /**
     * Process remaining events during shutdown
     */
    private void processRemainingEvents() {
        logger.info("Processing remaining {} events in queue", eventQueue.size() + currentBatch.size());
        
        // Process current batch
        if (!currentBatch.isEmpty()) {
            processBatchAsync(List.copyOf(currentBatch));
            currentBatch.clear();
        }
        
        // Process remaining events in queue
        List<SecurityEventStreamItem> remainingEvents = new CopyOnWriteArrayList<>();
        eventQueue.drainTo(remainingEvents);
        
        if (!remainingEvents.isEmpty()) {
            processBatchAsync(remainingEvents);
        }
    }

    /**
     * Monitor queue health
     */
    private void monitorQueue() {
        int queueSize = eventQueue.size();
        int batchSize = currentBatch.size();
        
        if (queueSize > bufferSize * 0.8) {
            logger.warn("Event queue is {}% full ({}/{})", 
                (queueSize * 100 / bufferSize), queueSize, bufferSize);
        }
        
        logger.debug("Event streaming status: queue={}, batch={}, processed={}, errors={}", 
            queueSize, batchSize, eventsStreamed.get(), streamingErrors.get());
    }

    /**
     * Get streaming metrics
     */
    public StreamingMetrics getMetrics() {
        return new StreamingMetrics(
            eventsStreamed.get(),
            batchesProcessed.get(),
            streamingErrors.get(),
            queueOverflows.get(),
            eventQueue.size(),
            currentBatch.size(),
            isRunning.get()
        );
    }

    /**
     * Enable/disable streaming
     */
    public void setStreamingEnabled(boolean enabled) {
        this.streamingEnabled = enabled;
        logger.info("Security event streaming {}", enabled ? "enabled" : "disabled");
    }

    // Data Models

    /**
     * Security event stream item
     */
    public record SecurityEventStreamItem(
        @JsonProperty("id") String id,
        @JsonProperty("event_type") StreamEventType eventType,
        @JsonProperty("security_event") SecurityEvent securityEvent,
        @JsonProperty("threat_analysis") ThreatAnalysisResult threatAnalysis,
        @JsonProperty("event_data") Map<String, Object> eventData,
        @JsonProperty("timestamp") Instant timestamp,
        @JsonProperty("metadata") Map<String, Object> metadata
    ) {}

    /**
     * Security event batch for streaming
     */
    public record SecurityEventBatch(
        @JsonProperty("batch_id") String batchId,
        @JsonProperty("events") List<SecurityEventStreamItem> events,
        @JsonProperty("event_count") int eventCount,
        @JsonProperty("timestamp") Instant timestamp,
        @JsonProperty("metadata") Map<String, Object> metadata
    ) {}

    /**
     * Stream event types
     */
    public enum StreamEventType {
        THREAT_DETECTION,
        SECURITY_ALERT,
        SECURITY_INCIDENT,
        THREAT_INTELLIGENCE,
        BEHAVIORAL_ANOMALY,
        POLICY_VIOLATION,
        SYSTEM_EVENT
    }

    /**
     * Streaming response
     */
    public record StreamingResponse(
        @JsonProperty("success") boolean success,
        @JsonProperty("status") String status,
        @JsonProperty("message") String message,
        @JsonProperty("error") String error,
        @JsonProperty("timestamp") Instant timestamp
    ) {
        public static StreamingResponse queued() {
            return new StreamingResponse(true, "queued", "Event queued for streaming", null, Instant.now());
        }

        public static StreamingResponse disabled() {
            return new StreamingResponse(false, "disabled", "Streaming is disabled", null, Instant.now());
        }

        public static StreamingResponse error(String error) {
            return new StreamingResponse(false, "error", null, error, Instant.now());
        }
    }

    /**
     * Streaming metrics
     */
    public record StreamingMetrics(
        long eventsStreamed,
        long batchesProcessed,
        long streamingErrors,
        long queueOverflows,
        int currentQueueSize,
        int currentBatchSize,
        boolean isRunning
    ) {}
}