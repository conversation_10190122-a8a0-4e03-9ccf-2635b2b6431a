package com.multiagent.platform.agents.security.ai.twodot.platform.agents.securitymonitor.security;

import java.time.Instant;
import java.util.List;

/**
 * Incident Response Result - Result of incident response
 */
public record IncidentResponseResult(
    String responseId,
    String incidentId,
    List<SecurityResponse> responses,
    boolean containmentSuccessful,
    boolean eradicationComplete,
    boolean recoveryComplete,
    String status,
    Instant startTime,
    Instant endTime,
    double effectivenessScore
) {
    public static IncidentResponseResult initiated(String incidentId) {
        return new IncidentResponseResult(
            java.util.UUID.randomUUID().toString(),
            incidentId,
            List.of(),
            false,
            false,
            false,
            "INITIATED",
            Instant.now(),
            null,
            0.0
        );
    }
}