package com.multiagent.platform.agents.security.ai.twodot.platform.agents.securitymonitor.monitoring;

import com.multiagent.platform.agents.securitymonitor.security.SecurityResponse;
import com.multiagent.platform.agents.securitymonitor.security.ThreatAnalysisResult;
import com.multiagent.platform.agents.securitymonitor.security.SecurityEvent;

import java.time.Instant;

public interface SecurityMetricsCollector {
    void startCollection();

    void stopCollection();

    void recordEvent(SecurityEvent event);

    void recordThreatAnalysis(ThreatAnalysisResult threatAnalysis);

    void recordSecurityResponse(SecurityResponse securityResponse);

    SecurityMetrics getCurrentMetrics();

    record SecurityMetrics(long totalEvents, long totalThreats, boolean collecting, Instant lastCollected) {
    }
}