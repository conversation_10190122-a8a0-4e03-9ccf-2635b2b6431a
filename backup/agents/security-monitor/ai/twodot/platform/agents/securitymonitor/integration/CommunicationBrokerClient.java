package com.multiagent.platform.agents.security.ai.twodot.platform.agents.securitymonitor.integration;

import com.multiagent.platform.agents.securitymonitor.security.SecurityEvent;
import com.multiagent.platform.agents.securitymonitor.security.ThreatAnalysisResult;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import jakarta.annotation.PostConstruct;
import java.time.Duration;
import java.time.Instant;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Communication Broker Agent (CBA) Integration Client
 * 
 * Handles all communication between Security Monitor Agent and other platform agents
 * through the Communication Broker Agent's A2A (Agent-to-Agent) protocol.
 */
@Component
public class CommunicationBrokerClient {

    private static final Logger logger = LoggerFactory.getLogger(CommunicationBrokerClient.class);

    @Value("${sma.cba.url:http://localhost:8080}")
    private String cbaUrl;

    @Value("${sma.cba.timeout:5000}")
    private int timeoutMs;

    @Value("${sma.agent.id:SMA-003}")
    private String agentId;

    private WebClient webClient;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final AtomicBoolean connected = new AtomicBoolean(false);
    private final AtomicLong messagesSent = new AtomicLong(0);
    private final AtomicLong messagesReceived = new AtomicLong(0);
    private final AtomicLong failedMessages = new AtomicLong(0);

    @PostConstruct
    public void initialize() {
        logger.info("Initializing Communication Broker Client...");
        
        this.webClient = WebClient.builder()
            .baseUrl(cbaUrl)
            .defaultHeader("Content-Type", "application/json")
            .defaultHeader("Accept", "application/json")
            .defaultHeader("X-Agent-ID", agentId)
            .build();
        
        // Test connection to CBA
        connectToCBA();
        
        logger.info("Communication Broker Client initialized. CBA URL: {}", cbaUrl);
    }

    /**
     * Register Security Monitor Agent with the Communication Broker
     */
    public void registerWithCBA() {
        logger.info("Registering Security Monitor Agent with CBA...");
        
        try {
            AgentRegistrationMessage registration = new AgentRegistrationMessage(
                agentId,
                "Security Monitor Agent",
                "1.0.0",
                "AI-powered cybersecurity monitoring and threat detection",
                Map.of(
                    "threat_detection", "Real-time threat detection and analysis",
                    "behavioral_analysis", "User and entity behavior analytics",
                    "incident_response", "Automated incident response and containment",
                    "security_monitoring", "Continuous security event monitoring",
                    "policy_enforcement", "Dynamic security policy enforcement"
                ),
                Map.of(
                    "intelligence_level", "HIGH",
                    "processing_capacity", "100+ events/second",
                    "ml_capabilities", "isolation_forest,lstm,nlp,ai_reasoning"
                )
            );

            sendMessage("CBA-001", "agent_registration", registration)
                .thenAccept(response -> {
                    if (response.success()) {
                        connected.set(true);
                        logger.info("Successfully registered with CBA: {}", response.result());
                    } else {
                        logger.error("Failed to register with CBA: {}", response.error());
                    }
                })
                .exceptionally(throwable -> {
                    logger.error("Exception during CBA registration: {}", throwable.getMessage());
                    return null;
                });
                
        } catch (Exception e) {
            logger.error("Error registering with CBA: {}", e.getMessage(), e);
        }
    }

    /**
     * Send security alert to other agents via CBA
     */
    public CompletableFuture<MessageResponse> sendSecurityAlert(SecurityEvent event, ThreatAnalysisResult analysis) {
        logger.debug("Sending security alert for event: {}", event.eventId());
        
        SecurityAlertMessage alert = new SecurityAlertMessage(
            event.eventId(),
            analysis.threatType(),
            analysis.riskLevel(),
            analysis.confidenceScore(),
            event.entityId(),
            event.description(),
            Map.of(
                "event_type", event.eventType().name(),
                "severity", event.severity().name(),
                "source", event.source().name(),
                "timestamp", event.timestamp().toString(),
                "analysis_method", analysis.analysisMethod(),
                "processing_time_ms", analysis.processingTimeMs()
            )
        );

        // Send to all relevant agents
        return sendBroadcastMessage("security_alert", alert);
    }

    /**
     * Request service information from Discovery Registry Agent
     */
    public CompletableFuture<MessageResponse> requestServiceDiscovery(String serviceType, Map<String, Object> requirements) {
        logger.debug("Requesting service discovery for type: {}", serviceType);
        
        ServiceDiscoveryRequest request = new ServiceDiscoveryRequest(
            serviceType,
            requirements,
            Map.of(
                "requester", agentId,
                "urgency", "normal",
                "context", "security_monitoring"
            )
        );

        return sendMessage("DRA-002", "service_discovery_request", request);
    }

    /**
     * Send threat intelligence update to other security agents
     */
    public CompletableFuture<MessageResponse> sendThreatIntelligenceUpdate(String threatType, Map<String, Object> intelligence) {
        logger.debug("Sending threat intelligence update: {}", threatType);
        
        ThreatIntelligenceMessage intel = new ThreatIntelligenceMessage(
            UUID.randomUUID().toString(),
            threatType,
            intelligence,
            Instant.now(),
            Map.of(
                "source", agentId,
                "confidence", intelligence.getOrDefault("confidence", 0.8),
                "severity", intelligence.getOrDefault("severity", "MEDIUM")
            )
        );

        return sendBroadcastMessage("threat_intelligence_update", intel);
    }

    /**
     * Send heartbeat to maintain connection with CBA
     */
    public CompletableFuture<MessageResponse> sendHeartbeat() {
        AgentHeartbeatMessage heartbeat = new AgentHeartbeatMessage(
            agentId,
            "HEALTHY",
            Instant.now(),
            Map.of(
                "events_processed", messagesSent.get(),
                "messages_received", messagesReceived.get(),
                "uptime", System.currentTimeMillis()
            )
        );

        return sendMessage("CBA-001", "heartbeat", heartbeat);
    }

    /**
     * Send message to specific agent via CBA
     */
    public CompletableFuture<MessageResponse> sendMessage(String toAgentId, String messageType, Object payload) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                CBAMessage message = new CBAMessage(
                    UUID.randomUUID().toString(),
                    agentId,
                    toAgentId,
                    messageType,
                    "request",
                    "HIGH",
                    payload,
                    Instant.now(),
                    Map.of("protocol", "a2a")
                );

                MessageResponse response = webClient.post()
                    .uri("/api/v1/messages")
                    .bodyValue(message)
                    .retrieve()
                    .bodyToMono(MessageResponse.class)
                    .timeout(Duration.ofMillis(timeoutMs))
                    .retryWhen(Retry.backoff(2, Duration.ofMillis(100)))
                    .block();

                if (response != null) {
                    messagesSent.incrementAndGet();
                    logger.debug("Message sent successfully to {}: {}", toAgentId, message.id());
                    return response;
                } else {
                    failedMessages.incrementAndGet();
                    return MessageResponse.error("No response received");
                }

            } catch (WebClientResponseException e) {
                failedMessages.incrementAndGet();
                logger.error("HTTP error sending message to {}: {} - {}", toAgentId, e.getStatusCode(), e.getResponseBodyAsString());
                return MessageResponse.error("HTTP error: " + e.getStatusCode());
            } catch (Exception e) {
                failedMessages.incrementAndGet();
                logger.error("Error sending message to {}: {}", toAgentId, e.getMessage());
                return MessageResponse.error("Communication error: " + e.getMessage());
            }
        });
    }

    /**
     * Send broadcast message to all agents via CBA
     */
    public CompletableFuture<MessageResponse> sendBroadcastMessage(String messageType, Object payload) {
        return sendMessage("*", messageType, payload);
    }

    /**
     * Test connection to CBA
     */
    private void connectToCBA() {
        try {
            String healthResponse = webClient.get()
                .uri("/actuator/health")
                .retrieve()
                .bodyToMono(String.class)
                .timeout(Duration.ofMillis(timeoutMs))
                .block();

            if (healthResponse != null) {
                connected.set(true);
                logger.info("Successfully connected to CBA at: {}", cbaUrl);
            }
        } catch (Exception e) {
            connected.set(false);
            logger.warn("Failed to connect to CBA at {}: {}", cbaUrl, e.getMessage());
        }
    }

    /**
     * Get connection status
     */
    public boolean isConnected() {
        return connected.get();
    }

    /**
     * Get communication metrics
     */
    public CommunicationMetrics getMetrics() {
        return new CommunicationMetrics(
            messagesSent.get(),
            messagesReceived.get(),
            failedMessages.get(),
            connected.get()
        );
    }

    // Message Models

    /**
     * Base CBA message structure
     */
    public record CBAMessage(
        @JsonProperty("id") String id,
        @JsonProperty("from_agent_id") String fromAgentId,
        @JsonProperty("to_agent_id") String toAgentId,
        @JsonProperty("subject") String subject,
        @JsonProperty("type") String type,
        @JsonProperty("priority") String priority,
        @JsonProperty("payload") Object payload,
        @JsonProperty("timestamp") Instant timestamp,
        @JsonProperty("metadata") Map<String, Object> metadata
    ) {}

    /**
     * Message response from CBA
     */
    public record MessageResponse(
        @JsonProperty("message_id") String messageId,
        @JsonProperty("status") String status,
        @JsonProperty("success") boolean success,
        @JsonProperty("result") Object result,
        @JsonProperty("error") String error,
        @JsonProperty("processing_time") String processingTime,
        @JsonProperty("timestamp") Instant timestamp
    ) {
        public static MessageResponse error(String error) {
            return new MessageResponse(
                UUID.randomUUID().toString(),
                "failed",
                false,
                null,
                error,
                "0ms",
                Instant.now()
            );
        }
    }

    /**
     * Agent registration message
     */
    public record AgentRegistrationMessage(
        @JsonProperty("agent_id") String agentId,
        @JsonProperty("agent_name") String agentName,
        @JsonProperty("version") String version,
        @JsonProperty("description") String description,
        @JsonProperty("capabilities") Map<String, String> capabilities,
        @JsonProperty("metadata") Map<String, Object> metadata
    ) {}

    /**
     * Security alert message
     */
    public record SecurityAlertMessage(
        @JsonProperty("event_id") String eventId,
        @JsonProperty("threat_type") String threatType,
        @JsonProperty("risk_level") String riskLevel,
        @JsonProperty("confidence") double confidence,
        @JsonProperty("affected_entity") String affectedEntity,
        @JsonProperty("description") String description,
        @JsonProperty("details") Map<String, Object> details
    ) {}

    /**
     * Service discovery request
     */
    public record ServiceDiscoveryRequest(
        @JsonProperty("service_type") String serviceType,
        @JsonProperty("requirements") Map<String, Object> requirements,
        @JsonProperty("metadata") Map<String, Object> metadata
    ) {}

    /**
     * Threat intelligence message
     */
    public record ThreatIntelligenceMessage(
        @JsonProperty("intelligence_id") String intelligenceId,
        @JsonProperty("threat_type") String threatType,
        @JsonProperty("intelligence_data") Map<String, Object> intelligenceData,
        @JsonProperty("timestamp") Instant timestamp,
        @JsonProperty("metadata") Map<String, Object> metadata
    ) {}

    /**
     * Agent heartbeat message
     */
    public record AgentHeartbeatMessage(
        @JsonProperty("agent_id") String agentId,
        @JsonProperty("status") String status,
        @JsonProperty("timestamp") Instant timestamp,
        @JsonProperty("metrics") Map<String, Object> metrics
    ) {}

    /**
     * Communication metrics
     */
    public record CommunicationMetrics(
        long messagesSent,
        long messagesReceived,
        long failedMessages,
        boolean connected
    ) {}
}