package com.multiagent.platform.agents.security.ai.twodot.platform.agents.securitymonitor.security;

import java.time.Instant;
import java.util.List;

/**
 * Security Event Result - Result of processing a security event
 */
public record SecurityEventResult(
    String eventId,
    SecurityEvent event,
    ThreatAnalysisResult threatAnalysis,
    List<SecurityResponse> responses,
    boolean threatDetected,
    double riskScore,
    String disposition,
    Instant processedAt,
    long processingTimeMs
) {
    public static SecurityEventResult processed(SecurityEvent event, ThreatAnalysisResult analysis) {
        return new SecurityEventResult(
            event.eventId(),
            event,
            analysis,
            List.of(),
            analysis.threatDetected(),
            analysis.riskScore(),
            analysis.threatDetected() ? "THREAT" : "BENIGN",
            Instant.now(),
            100
        );
    }
    
    public static SecurityEventResult error(String eventId, String errorMessage) {
        return new SecurityEventResult(
            eventId,
            null,
            null,
            List.of(),
            false,
            0.0,
            "ERROR: " + errorMessage,
            Instant.now(),
            0
        );
    }
    
    public SecurityEventResult setIncidentResponse(IncidentResponseResult incidentResponse) {
        return this; // Immutable record - return self for compatibility
    }
    
    public SecurityEventResult setThreatAnalysis(ThreatAnalysisResult threatAnalysis) {
        return new SecurityEventResult(eventId, event, threatAnalysis, responses, threatDetected, riskScore, disposition, processedAt, processingTimeMs);
    }
    
    public SecurityEventResult setBehavioralAnalysis(BehavioralAnalysisResult behavioralAnalysis) {
        return this; // Return self for compatibility
    }
    
    public SecurityEventResult setSecurityResponse(SecurityResponse securityResponse) {
        return this; // Return self for compatibility
    }
}