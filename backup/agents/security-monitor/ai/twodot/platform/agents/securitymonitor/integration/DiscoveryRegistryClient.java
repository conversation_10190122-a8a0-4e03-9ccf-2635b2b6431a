package com.multiagent.platform.agents.security.ai.twodot.platform.agents.securitymonitor.integration;

import com.multiagent.platform.agents.securitymonitor.security.SecurityEvent;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import jakarta.annotation.PostConstruct;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Discovery Registry Agent (DRA) Integration Client
 * 
 * Handles service registration, discovery, and health monitoring
 * with the Discovery Registry Agent for the Security Monitor Agent.
 */
@Component
public class DiscoveryRegistryClient {

    private static final Logger logger = LoggerFactory.getLogger(DiscoveryRegistryClient.class);

    @Value("${sma.dra.url:http://localhost:8081}")
    private String draUrl;

    @Value("${sma.dra.timeout:5000}")
    private int timeoutMs;

    @Value("${sma.agent.id:SMA-003}")
    private String agentId;

    @Value("${sma.service.port:8083}")
    private int servicePort;

    @Value("${sma.service.host:localhost}")
    private String serviceHost;

    private WebClient webClient;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final AtomicBoolean registered = new AtomicBoolean(false);
    private final AtomicLong servicesRegistered = new AtomicLong(0);
    private final AtomicLong discoveryRequests = new AtomicLong(0);
    private final AtomicLong healthChecks = new AtomicLong(0);

    @PostConstruct
    public void initialize() {
        logger.info("Initializing Discovery Registry Client...");
        
        this.webClient = WebClient.builder()
            .baseUrl(draUrl)
            .defaultHeader("Content-Type", "application/json")
            .defaultHeader("Accept", "application/json")
            .defaultHeader("X-Agent-ID", agentId)
            .build();
        
        // Test connection to DRA
        connectToDRA();
        
        logger.info("Discovery Registry Client initialized. DRA URL: {}", draUrl);
    }

    /**
     * Register Security Monitor Agent services with DRA via A2A protocol through CBA
     */
    public CompletableFuture<ServiceRegistrationResponse> registerSecurityMonitorServices() {
        logger.info("Registering Security Monitor Agent services with DRA via A2A protocol...");
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                // Main security monitoring service
                ServiceRegistrationRequest mainService = new ServiceRegistrationRequest(
                    agentId + "-main",
                    "Security Monitor Agent - Main Service",
                    "1.0.0",
                    "Core security monitoring and threat detection service",
                    List.of(
                        new ServiceEndpoint("http", serviceHost, servicePort, "/api/v1/security"),
                        new ServiceEndpoint("http", serviceHost, servicePort, "/actuator/health")
                    ),
                    List.of(
                        new ServiceCapability("threat_detection", "1.0.0", "Real-time threat detection and analysis"),
                        new ServiceCapability("behavioral_analysis", "1.0.0", "User and entity behavior analytics"),
                        new ServiceCapability("incident_response", "1.0.0", "Automated incident response and containment"),
                        new ServiceCapability("security_monitoring", "1.0.0", "Continuous security event monitoring"),
                        new ServiceCapability("policy_enforcement", "1.0.0", "Dynamic security policy enforcement")
                    ),
                    List.of("security", "monitoring", "ai-powered", "real-time"),
                    Map.of(
                        "agent_id", agentId,
                        "intelligence_level", "HIGH",
                        "processing_capacity", "100+ events/second",
                        "ml_capabilities", "isolation_forest,lstm,nlp,ai_reasoning",
                        "protocols", "http,rest,websocket,a2a"
                    )
                );

                // AI/ML threat detection service
                ServiceRegistrationRequest mlService = new ServiceRegistrationRequest(
                    agentId + "-ml",
                    "Security Monitor Agent - ML Service",
                    "1.0.0",
                    "AI/ML powered threat detection and analysis service",
                    List.of(
                        new ServiceEndpoint("http", serviceHost, 8084, "/api/v1/ml"),
                        new ServiceEndpoint("http", serviceHost, 8084, "/health")
                    ),
                    List.of(
                        new ServiceCapability("anomaly_detection", "1.0.0", "ML-based anomaly detection using Isolation Forest"),
                        new ServiceCapability("nlp_analysis", "1.0.0", "Natural language processing for threat analysis"),
                        new ServiceCapability("ai_reasoning", "1.0.0", "AI-powered threat reasoning and classification"),
                        new ServiceCapability("behavioral_modeling", "1.0.0", "Advanced behavioral pattern modeling")
                    ),
                    List.of("ai", "ml", "threat-detection", "nlp", "anomaly-detection"),
                    Map.of(
                        "parent_agent", agentId,
                        "ai_models", "isolation_forest,transformer,lstm",
                        "languages", "python,fastapi",
                        "inference_time", "<150ms",
                        "a2a_enabled", true
                    )
                );

                // Send registration requests via A2A protocol through CBA
                A2AMessage mainRegMessage = createA2AMessage("DRA-002", "service_registration", mainService);
                A2AMessage mlRegMessage = createA2AMessage("DRA-002", "service_registration", mlService);

                // Send both registration messages through CBA
                ServiceRegistrationResponse mainResponse = sendA2AMessage(mainRegMessage);
                ServiceRegistrationResponse mlResponse = sendA2AMessage(mlRegMessage);

                if (mainResponse.success() && mlResponse.success()) {
                    registered.set(true);
                    servicesRegistered.set(2);
                    logger.info("Successfully registered Security Monitor Agent services with DRA via A2A");
                    return ServiceRegistrationResponse.success("Both services registered successfully via A2A");
                } else {
                    logger.error("Failed to register services via A2A: Main={}, ML={}", 
                        mainResponse.error(), mlResponse.error());
                    return ServiceRegistrationResponse.error("Failed to register services via A2A");
                }

            } catch (Exception e) {
                logger.error("Error registering services with DRA via A2A: {}", e.getMessage(), e);
                return ServiceRegistrationResponse.error("A2A registration error: " + e.getMessage());
            }
        });
    }

    /**
     * Discover security-related services via A2A protocol through CBA
     */
    public CompletableFuture<ServiceDiscoveryResponse> discoverSecurityServices(ServiceRequirements requirements) {
        logger.debug("Discovering security services via A2A with requirements: {}", requirements);
        discoveryRequests.incrementAndGet();
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                // Create A2A message for service discovery
                A2AMessage discoveryMessage = createA2AMessage("DRA-002", "service_discovery", requirements);
                
                // Send discovery request through CBA using A2A protocol
                ServiceDiscoveryResponse response = sendA2AMessage(discoveryMessage, ServiceDiscoveryResponse.class);

                if (response != null && response.success()) {
                    logger.debug("Discovered {} security services via A2A", response.services().size());
                    return response;
                } else {
                    return ServiceDiscoveryResponse.error("No response received via A2A");
                }

            } catch (Exception e) {
                logger.error("Error discovering services via A2A: {}", e.getMessage());
                return ServiceDiscoveryResponse.error("A2A discovery error: " + e.getMessage());
            }
        });
    }

    /**
     * Send health status update to DRA via A2A protocol through CBA
     */
    public CompletableFuture<HealthUpdateResponse> sendHealthUpdate(String serviceId, HealthStatus health) {
        logger.debug("Sending health update via A2A for service: {}", serviceId);
        healthChecks.incrementAndGet();
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                HealthUpdateRequest request = new HealthUpdateRequest(serviceId, health, Instant.now());
                
                // Create A2A message for health update
                A2AMessage healthMessage = createA2AMessage("DRA-002", "health_update", request);
                
                // Send health update through CBA using A2A protocol
                HealthUpdateResponse response = sendA2AMessage(healthMessage, HealthUpdateResponse.class);

                if (response != null && response.success()) {
                    logger.debug("Health update sent successfully via A2A for service: {}", serviceId);
                    return response;
                } else {
                    return HealthUpdateResponse.error("Failed to update health via A2A");
                }

            } catch (Exception e) {
                logger.error("Error sending health update via A2A: {}", e.getMessage());
                return HealthUpdateResponse.error("A2A health update error: " + e.getMessage());
            }
        });
    }

    /**
     * Register a single service
     */
    private ServiceRegistrationResponse registerService(ServiceRegistrationRequest request) {
        try {
            ServiceRegistrationResponse response = webClient.post()
                .uri("/api/v1/services/register")
                .bodyValue(request)
                .retrieve()
                .bodyToMono(ServiceRegistrationResponse.class)
                .timeout(Duration.ofMillis(timeoutMs))
                .retryWhen(Retry.backoff(2, Duration.ofMillis(100)))
                .block();

            return response != null ? response : ServiceRegistrationResponse.error("No response received");

        } catch (WebClientResponseException e) {
            logger.error("HTTP error registering service {}: {} - {}", 
                request.name(), e.getStatusCode(), e.getResponseBodyAsString());
            return ServiceRegistrationResponse.error("HTTP error: " + e.getStatusCode());
        } catch (Exception e) {
            logger.error("Error registering service {}: {}", request.name(), e.getMessage());
            return ServiceRegistrationResponse.error("Registration error: " + e.getMessage());
        }
    }

    /**
     * Test connection to DRA
     */
    private void connectToDRA() {
        try {
            String healthResponse = webClient.get()
                .uri("/actuator/health")
                .retrieve()
                .bodyToMono(String.class)
                .timeout(Duration.ofMillis(timeoutMs))
                .block();

            if (healthResponse != null) {
                logger.info("Successfully connected to DRA at: {}", draUrl);
            }
        } catch (Exception e) {
            logger.warn("Failed to connect to DRA at {}: {}", draUrl, e.getMessage());
        }
    }

    /**
     * Get registration status
     */
    public boolean isRegistered() {
        return registered.get();
    }

    /**
     * Get discovery metrics
     */
    public DiscoveryMetrics getMetrics() {
        return new DiscoveryMetrics(
            servicesRegistered.get(),
            discoveryRequests.get(),
            healthChecks.get(),
            registered.get()
        );
    }

    // A2A Protocol Support Methods

    /**
     * Create A2A message for communication through CBA
     */
    private A2AMessage createA2AMessage(String toAgentId, String messageType, Object payload) {
        return new A2AMessage(
            UUID.randomUUID().toString(),
            agentId,
            toAgentId,
            messageType,
            "request",
            "HIGH",
            payload,
            Instant.now(),
            Map.of(
                "protocol", "a2a",
                "routing_strategy", "direct",
                "security_context", "agent_verified"
            )
        );
    }

    /**
     * Send A2A message through CBA with specific response type
     */
    @SuppressWarnings("unchecked")
    private <T> T sendA2AMessage(A2AMessage message, Class<T> responseType) {
        try {
            A2AResponse response = webClient.post()
                .uri("/api/v1/a2a/messages")
                .bodyValue(message)
                .retrieve()
                .bodyToMono(A2AResponse.class)
                .timeout(Duration.ofMillis(timeoutMs))
                .retryWhen(Retry.backoff(2, Duration.ofMillis(100)))
                .block();

            if (response != null && response.success()) {
                // Convert response payload to target type
                if (response.payload() instanceof Map) {
                    return objectMapper.convertValue(response.payload(), responseType);
                } else {
                    return (T) response.payload();
                }
            } else {
                logger.error("A2A message failed: {}", response != null ? response.error() : "No response");
                return null;
            }

        } catch (WebClientResponseException e) {
            logger.error("HTTP error sending A2A message: {} - {}", e.getStatusCode(), e.getResponseBodyAsString());
            return null;
        } catch (Exception e) {
            logger.error("Error sending A2A message: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Send A2A message through CBA (generic method for ServiceRegistrationResponse)
     */
    private ServiceRegistrationResponse sendA2AMessage(A2AMessage message) {
        try {
            A2AResponse response = webClient.post()
                .uri("/api/v1/a2a/messages")
                .bodyValue(message)
                .retrieve()
                .bodyToMono(A2AResponse.class)
                .timeout(Duration.ofMillis(timeoutMs))
                .retryWhen(Retry.backoff(2, Duration.ofMillis(100)))
                .block();

            if (response != null && response.success()) {
                logger.debug("A2A message sent successfully: {}", message.id());
                return ServiceRegistrationResponse.success("Service registered via A2A");
            } else {
                String error = response != null ? response.error() : "No response received";
                logger.error("A2A message failed: {}", error);
                return ServiceRegistrationResponse.error("A2A error: " + error);
            }

        } catch (WebClientResponseException e) {
            logger.error("HTTP error sending A2A message: {} - {}", e.getStatusCode(), e.getResponseBodyAsString());
            return ServiceRegistrationResponse.error("HTTP error: " + e.getStatusCode());
        } catch (Exception e) {
            logger.error("Error sending A2A message: {}", e.getMessage());
            return ServiceRegistrationResponse.error("A2A communication error: " + e.getMessage());
        }
    }

    // Data Models

    /**
     * Service registration request
     */
    public record ServiceRegistrationRequest(
        @JsonProperty("id") String id,
        @JsonProperty("name") String name,
        @JsonProperty("version") String version,
        @JsonProperty("description") String description,
        @JsonProperty("endpoints") List<ServiceEndpoint> endpoints,
        @JsonProperty("capabilities") List<ServiceCapability> capabilities,
        @JsonProperty("tags") List<String> tags,
        @JsonProperty("metadata") Map<String, Object> metadata
    ) {}

    /**
     * Service endpoint definition
     */
    public record ServiceEndpoint(
        @JsonProperty("protocol") String protocol,
        @JsonProperty("host") String host,
        @JsonProperty("port") int port,
        @JsonProperty("path") String path
    ) {}

    /**
     * Service capability definition
     */
    public record ServiceCapability(
        @JsonProperty("name") String name,
        @JsonProperty("version") String version,
        @JsonProperty("description") String description
    ) {}

    /**
     * Service requirements for discovery
     */
    public record ServiceRequirements(
        @JsonProperty("capabilities") List<String> capabilities,
        @JsonProperty("protocols") List<String> protocols,
        @JsonProperty("tags") List<String> tags,
        @JsonProperty("min_availability") double minAvailability,
        @JsonProperty("max_response_time") double maxResponseTime,
        @JsonProperty("metadata") Map<String, Object> metadata
    ) {}

    /**
     * Health status information
     */
    public record HealthStatus(
        @JsonProperty("status") String status,
        @JsonProperty("details") Map<String, Object> details,
        @JsonProperty("checks") Map<String, Boolean> checks,
        @JsonProperty("message") String message
    ) {}

    /**
     * Service registration response
     */
    public record ServiceRegistrationResponse(
        @JsonProperty("service_id") String serviceId,
        @JsonProperty("success") boolean success,
        @JsonProperty("message") String message,
        @JsonProperty("error") String error,
        @JsonProperty("timestamp") Instant timestamp
    ) {
        public static ServiceRegistrationResponse success(String message) {
            return new ServiceRegistrationResponse(
                UUID.randomUUID().toString(),
                true,
                message,
                null,
                Instant.now()
            );
        }

        public static ServiceRegistrationResponse error(String error) {
            return new ServiceRegistrationResponse(
                UUID.randomUUID().toString(),
                false,
                null,
                error,
                Instant.now()
            );
        }
    }

    /**
     * Service discovery response
     */
    public record ServiceDiscoveryResponse(
        @JsonProperty("services") List<ServiceInfo> services,
        @JsonProperty("total") int total,
        @JsonProperty("success") boolean success,
        @JsonProperty("error") String error,
        @JsonProperty("timestamp") Instant timestamp
    ) {
        public static ServiceDiscoveryResponse error(String error) {
            return new ServiceDiscoveryResponse(
                List.of(),
                0,
                false,
                error,
                Instant.now()
            );
        }
    }

    /**
     * Service information
     */
    public record ServiceInfo(
        @JsonProperty("id") String id,
        @JsonProperty("name") String name,
        @JsonProperty("version") String version,
        @JsonProperty("endpoints") List<ServiceEndpoint> endpoints,
        @JsonProperty("capabilities") List<ServiceCapability> capabilities,
        @JsonProperty("tags") List<String> tags,
        @JsonProperty("health") HealthStatus health,
        @JsonProperty("metadata") Map<String, Object> metadata
    ) {}

    /**
     * Health update request
     */
    public record HealthUpdateRequest(
        @JsonProperty("service_id") String serviceId,
        @JsonProperty("health") HealthStatus health,
        @JsonProperty("timestamp") Instant timestamp
    ) {}

    /**
     * Health update response
     */
    public record HealthUpdateResponse(
        @JsonProperty("success") boolean success,
        @JsonProperty("message") String message,
        @JsonProperty("error") String error,
        @JsonProperty("timestamp") Instant timestamp
    ) {
        public static HealthUpdateResponse error(String error) {
            return new HealthUpdateResponse(
                false,
                null,
                error,
                Instant.now()
            );
        }
    }

    /**
     * Discovery metrics
     */
    public record DiscoveryMetrics(
        long servicesRegistered,
        long discoveryRequests,
        long healthChecks,
        boolean registered
    ) {}

    // A2A Protocol Models

    /**
     * A2A message structure for agent-to-agent communication through CBA
     */
    public record A2AMessage(
        @JsonProperty("id") String id,
        @JsonProperty("from_agent_id") String fromAgentId,
        @JsonProperty("to_agent_id") String toAgentId,
        @JsonProperty("subject") String subject,
        @JsonProperty("type") String type,
        @JsonProperty("priority") String priority,
        @JsonProperty("payload") Object payload,
        @JsonProperty("timestamp") Instant timestamp,
        @JsonProperty("metadata") Map<String, Object> metadata
    ) {}

    /**
     * A2A response from CBA
     */
    public record A2AResponse(
        @JsonProperty("message_id") String messageId,
        @JsonProperty("status") String status,
        @JsonProperty("success") boolean success,
        @JsonProperty("payload") Object payload,
        @JsonProperty("error") String error,
        @JsonProperty("processing_time") String processingTime,
        @JsonProperty("timestamp") Instant timestamp
    ) {}
}