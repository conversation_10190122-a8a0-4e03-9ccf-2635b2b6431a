package com.multiagent.platform.agents.security.ai.twodot.platform.agents.securitymonitor.ai;

import com.multiagent.platform.agents.securitymonitor.security.SecurityEvent;
import com.multiagent.platform.agents.securitymonitor.security.ThreatAnalysisResult;

import java.util.concurrent.CompletableFuture;

public interface ThreatDetectionService {
    String getStatus();

    void initializeModels();

    CompletableFuture<ThreatAnalysisResult> analyzeThreat(SecurityEvent event);
}