package com.multiagent.platform.agents.security.ai.twodot.platform.agents.securitymonitor.security;

import java.util.concurrent.CompletableFuture;

public interface SecurityEventProcessor {
    String getStatus();

    void startEventStreaming();

    void stopEventStreaming();

    CompletableFuture<SecurityEventResult> processEvent(SecurityEvent event);

    record SecurityEventResult(boolean success, String eventId, String message) {
        public static SecurityEventResult success(String eventId, String message) {
            return new SecurityEventResult(true, eventId, message);
        }

        public boolean isSuccess() { return success; }
        public String getEventId() { return eventId; }
        public String getMessage() { return message; }
    }
}