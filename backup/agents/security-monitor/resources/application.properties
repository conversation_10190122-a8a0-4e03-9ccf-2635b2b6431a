# Security Monitor Agent Configuration
server.port=8082
spring.application.name=security-monitor-agent

# PostgreSQL Database Configuration
spring.datasource.url=****************************************************
spring.datasource.driverClassName=org.postgresql.Driver
spring.datasource.username=${DB_USERNAME:koneti}
spring.datasource.password=${DB_PASSWORD:}
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=true

# Redis Configuration
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.timeout=2000ms

# Management and Actuator
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=always
management.metrics.export.prometheus.enabled=true

# Security Monitor Specific
security.monitor.threat.detection.enabled=true
security.monitor.behavioral.analysis.enabled=true
security.monitor.incident.response.enabled=true
security.monitor.ai.enabled=true
security.monitor.ai.service.url=http://localhost:9082

# Logging
logging.level.ai.twodot.platform.agents.securitymonitor=INFO
logging.level.org.springframework.web=INFO
logging.level.root=INFO