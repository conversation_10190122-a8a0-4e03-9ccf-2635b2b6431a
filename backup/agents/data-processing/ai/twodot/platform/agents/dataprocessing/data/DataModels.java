package com.multiagent.platform.agents.data.ai.twodot.platform.agents.dataprocessing.data;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.Instant;
import java.util.List;
import java.util.Map;

/**
 * Core data models for the Data Processing Agent
 * 
 * Defines all data structures used for data processing, transformation, and quality analysis.
 */
public class DataModels {

    // Data Pipeline Models

    /**
     * Data Pipeline Definition
     */
    public record DataPipeline(
        @JsonProperty("pipeline_id") String pipelineId,
        @JsonProperty("name") String name,
        @JsonProperty("description") String description,
        @JsonProperty("source") DataSource source,
        @JsonProperty("destination") DataDestination destination,
        @JsonProperty("transformations") List<DataTransformation> transformations,
        @JsonProperty("quality_checks") List<QualityCheck> qualityChecks,
        @JsonProperty("schedule") PipelineSchedule schedule,
        @JsonProperty("configuration") PipelineConfiguration configuration,
        @JsonProperty("metadata") Map<String, Object> metadata,
        @JsonProperty("created_at") Instant createdAt,
        @JsonProperty("updated_at") Instant updatedAt
    ) {}

    /**
     * Data Source Configuration
     */
    public record DataSource(
        @JsonProperty("source_id") String sourceId,
        @JsonProperty("type") SourceType type,
        @JsonProperty("connection_config") ConnectionConfig connectionConfig,
        @JsonProperty("schema") DataSchema schema,
        @JsonProperty("batch_config") BatchConfig batchConfig,
        @JsonProperty("stream_config") StreamConfig streamConfig,
        @JsonProperty("retry_policy") RetryPolicy retryPolicy
    ) {}

    /**
     * Data Destination Configuration
     */
    public record DataDestination(
        @JsonProperty("destination_id") String destinationId,
        @JsonProperty("type") DestinationType type,
        @JsonProperty("connection_config") ConnectionConfig connectionConfig,
        @JsonProperty("schema") DataSchema schema,
        @JsonProperty("partition_config") PartitionConfig partitionConfig,
        @JsonProperty("write_mode") WriteMode writeMode,
        @JsonProperty("compression") CompressionType compression
    ) {}

    /**
     * Data Transformation Definition
     */
    public record DataTransformation(
        @JsonProperty("transformation_id") String transformationId,
        @JsonProperty("name") String name,
        @JsonProperty("type") TransformationType type,
        @JsonProperty("configuration") Map<String, Object> configuration,
        @JsonProperty("sql_expression") String sqlExpression,
        @JsonProperty("ai_model_config") AiModelConfig aiModelConfig,
        @JsonProperty("input_fields") List<String> inputFields,
        @JsonProperty("output_fields") List<String> outputFields,
        @JsonProperty("order") int order
    ) {}

    /**
     * Data Quality Check Definition
     */
    public record QualityCheck(
        @JsonProperty("check_id") String checkId,
        @JsonProperty("name") String name,
        @JsonProperty("type") QualityCheckType type,
        @JsonProperty("field") String field,
        @JsonProperty("rule") QualityRule rule,
        @JsonProperty("threshold") double threshold,
        @JsonProperty("severity") Severity severity,
        @JsonProperty("action") QualityAction action
    ) {}

    /**
     * Data Schema Definition
     */
    public record DataSchema(
        @JsonProperty("schema_id") String schemaId,
        @JsonProperty("name") String name,
        @JsonProperty("version") String version,
        @JsonProperty("fields") List<SchemaField> fields,
        @JsonProperty("primary_keys") List<String> primaryKeys,
        @JsonProperty("indexes") List<SchemaIndex> indexes,
        @JsonProperty("constraints") List<SchemaConstraint> constraints
    ) {}

    /**
     * Schema Field Definition
     */
    public record SchemaField(
        @JsonProperty("name") String name,
        @JsonProperty("type") FieldType type,
        @JsonProperty("nullable") boolean nullable,
        @JsonProperty("default_value") Object defaultValue,
        @JsonProperty("format") String format,
        @JsonProperty("validation") FieldValidation validation,
        @JsonProperty("description") String description,
        @JsonProperty("metadata") Map<String, Object> metadata
    ) {}

    // Processing Results

    /**
     * Data Processing Result
     */
    public record ProcessingResult(
        @JsonProperty("processing_id") String processingId,
        @JsonProperty("pipeline_id") String pipelineId,
        @JsonProperty("status") ProcessingStatus status,
        @JsonProperty("records_processed") long recordsProcessed,
        @JsonProperty("records_succeeded") long recordsSucceeded,
        @JsonProperty("records_failed") long recordsFailed,
        @JsonProperty("processing_time_ms") long processingTimeMs,
        @JsonProperty("quality_metrics") QualityMetrics qualityMetrics,
        @JsonProperty("error_summary") ErrorSummary errorSummary,
        @JsonProperty("lineage") DataLineage lineage,
        @JsonProperty("started_at") Instant startedAt,
        @JsonProperty("completed_at") Instant completedAt
    ) {}

    /**
     * Data Quality Metrics
     */
    public record QualityMetrics(
        @JsonProperty("completeness_score") double completenessScore,
        @JsonProperty("accuracy_score") double accuracyScore,
        @JsonProperty("consistency_score") double consistencyScore,
        @JsonProperty("validity_score") double validityScore,
        @JsonProperty("uniqueness_score") double uniquenessScore,
        @JsonProperty("timeliness_score") double timelinessScore,
        @JsonProperty("overall_quality_score") double overallQualityScore,
        @JsonProperty("quality_issues") List<QualityIssue> qualityIssues,
        @JsonProperty("anomalies_detected") List<AnomalyDetection> anomaliesDetected
    ) {}

    /**
     * Quality Issue
     */
    public record QualityIssue(
        @JsonProperty("issue_id") String issueId,
        @JsonProperty("type") QualityIssueType type,
        @JsonProperty("field") String field,
        @JsonProperty("description") String description,
        @JsonProperty("severity") Severity severity,
        @JsonProperty("count") long count,
        @JsonProperty("sample_values") List<Object> sampleValues
    ) {}

    /**
     * Anomaly Detection Result
     */
    public record AnomalyDetection(
        @JsonProperty("anomaly_id") String anomalyId,
        @JsonProperty("type") AnomalyType type,
        @JsonProperty("field") String field,
        @JsonProperty("anomaly_score") double anomalyScore,
        @JsonProperty("threshold") double threshold,
        @JsonProperty("description") String description,
        @JsonProperty("affected_records") long affectedRecords,
        @JsonProperty("detected_at") Instant detectedAt
    ) {}

    /**
     * Data Lineage
     */
    public record DataLineage(
        @JsonProperty("lineage_id") String lineageId,
        @JsonProperty("source_datasets") List<String> sourceDatasets,
        @JsonProperty("transformations_applied") List<String> transformationsApplied,
        @JsonProperty("destination_datasets") List<String> destinationDatasets,
        @JsonProperty("lineage_graph") Map<String, Object> lineageGraph,
        @JsonProperty("created_at") Instant createdAt
    ) {}

    // Configuration Models

    /**
     * Pipeline Configuration
     */
    public record PipelineConfiguration(
        @JsonProperty("parallelism") int parallelism,
        @JsonProperty("batch_size") int batchSize,
        @JsonProperty("timeout_seconds") int timeoutSeconds,
        @JsonProperty("retry_attempts") int retryAttempts,
        @JsonProperty("checkpoint_interval") int checkpointInterval,
        @JsonProperty("monitoring_enabled") boolean monitoringEnabled,
        @JsonProperty("quality_checks_enabled") boolean qualityChecksEnabled,
        @JsonProperty("lineage_tracking_enabled") boolean lineageTrackingEnabled
    ) {}

    /**
     * Connection Configuration
     */
    public record ConnectionConfig(
        @JsonProperty("host") String host,
        @JsonProperty("port") int port,
        @JsonProperty("database") String database,
        @JsonProperty("username") String username,
        @JsonProperty("password") String password,
        @JsonProperty("connection_params") Map<String, String> connectionParams,
        @JsonProperty("pool_config") ConnectionPoolConfig poolConfig,
        @JsonProperty("ssl_config") SslConfig sslConfig
    ) {}

    /**
     * AI Model Configuration
     */
    public record AiModelConfig(
        @JsonProperty("model_type") AiModelType modelType,
        @JsonProperty("model_name") String modelName,
        @JsonProperty("endpoint_url") String endpointUrl,
        @JsonProperty("parameters") Map<String, Object> parameters,
        @JsonProperty("confidence_threshold") double confidenceThreshold,
        @JsonProperty("fallback_strategy") FallbackStrategy fallbackStrategy
    ) {}

    // Enums

    public enum SourceType {
        DATABASE, FILE, API, STREAM, QUEUE, OBJECT_STORAGE
    }

    public enum DestinationType {
        DATABASE, FILE, API, STREAM, QUEUE, OBJECT_STORAGE, DATA_WAREHOUSE
    }

    public enum TransformationType {
        MAP, FILTER, AGGREGATE, JOIN, SPLIT, MERGE, AI_TRANSFORM, SQL_TRANSFORM
    }

    public enum QualityCheckType {
        COMPLETENESS, ACCURACY, CONSISTENCY, VALIDITY, UNIQUENESS, TIMELINESS, CUSTOM
    }

    public enum ProcessingStatus {
        PENDING, RUNNING, COMPLETED, FAILED, CANCELLED, PAUSED
    }

    public enum FieldType {
        STRING, INTEGER, LONG, DOUBLE, BOOLEAN, DATE, TIMESTAMP, JSON, BINARY
    }

    public enum Severity {
        LOW, MEDIUM, HIGH, CRITICAL
    }

    public enum QualityAction {
        LOG, ALERT, QUARANTINE, REJECT, REPAIR
    }

    public enum WriteMode {
        APPEND, OVERWRITE, UPSERT, ERROR_IF_EXISTS
    }

    public enum CompressionType {
        NONE, GZIP, SNAPPY, LZ4, BROTLI
    }

    public enum AnomalyType {
        STATISTICAL_OUTLIER, PATTERN_DEVIATION, VALUE_DISTRIBUTION, TEMPORAL_ANOMALY
    }

    public enum QualityIssueType {
        NULL_VALUE, INVALID_FORMAT, OUT_OF_RANGE, DUPLICATE_VALUE, MISSING_REFERENCE
    }

    public enum AiModelType {
        CLASSIFICATION, REGRESSION, CLUSTERING, ANOMALY_DETECTION, NLP, COMPUTER_VISION
    }

    public enum FallbackStrategy {
        DEFAULT_VALUE, SKIP_RECORD, USE_PREVIOUS_VALUE, MANUAL_REVIEW
    }

    // Supporting classes

    public record PipelineSchedule(
        @JsonProperty("cron_expression") String cronExpression,
        @JsonProperty("timezone") String timezone,
        @JsonProperty("enabled") boolean enabled
    ) {}

    public record BatchConfig(
        @JsonProperty("batch_size") int batchSize,
        @JsonProperty("fetch_size") int fetchSize,
        @JsonProperty("parallel_readers") int parallelReaders
    ) {}

    public record StreamConfig(
        @JsonProperty("topic") String topic,
        @JsonProperty("consumer_group") String consumerGroup,
        @JsonProperty("offset_reset") String offsetReset,
        @JsonProperty("window_size_ms") long windowSizeMs
    ) {}

    public record RetryPolicy(
        @JsonProperty("max_attempts") int maxAttempts,
        @JsonProperty("initial_delay_ms") long initialDelayMs,
        @JsonProperty("max_delay_ms") long maxDelayMs,
        @JsonProperty("backoff_multiplier") double backoffMultiplier
    ) {}

    public record PartitionConfig(
        @JsonProperty("partition_by") List<String> partitionBy,
        @JsonProperty("partition_count") int partitionCount,
        @JsonProperty("distribution_strategy") String distributionStrategy
    ) {}

    public record SchemaIndex(
        @JsonProperty("name") String name,
        @JsonProperty("fields") List<String> fields,
        @JsonProperty("unique") boolean unique
    ) {}

    public record SchemaConstraint(
        @JsonProperty("name") String name,
        @JsonProperty("type") String type,
        @JsonProperty("fields") List<String> fields,
        @JsonProperty("expression") String expression
    ) {}

    public record FieldValidation(
        @JsonProperty("min_length") Integer minLength,
        @JsonProperty("max_length") Integer maxLength,
        @JsonProperty("pattern") String pattern,
        @JsonProperty("allowed_values") List<Object> allowedValues
    ) {}

    public record QualityRule(
        @JsonProperty("expression") String expression,
        @JsonProperty("expected_value") Object expectedValue,
        @JsonProperty("tolerance") Double tolerance
    ) {}

    public record ErrorSummary(
        @JsonProperty("total_errors") long totalErrors,
        @JsonProperty("error_types") Map<String, Long> errorTypes,
        @JsonProperty("sample_errors") List<String> sampleErrors
    ) {}

    public record ConnectionPoolConfig(
        @JsonProperty("min_connections") int minConnections,
        @JsonProperty("max_connections") int maxConnections,
        @JsonProperty("connection_timeout_ms") long connectionTimeoutMs
    ) {}

    public record SslConfig(
        @JsonProperty("enabled") boolean enabled,
        @JsonProperty("trust_store_path") String trustStorePath,
        @JsonProperty("key_store_path") String keyStorePath
    ) {}

    // Stream Processing Models

    public record StreamMetrics(
        @JsonProperty("stream_id") String streamId,
        @JsonProperty("records_processed") long recordsProcessed,
        @JsonProperty("records_failed") long recordsFailed,
        @JsonProperty("processing_rate") double processingRate,
        @JsonProperty("lag_ms") long lagMs,
        @JsonProperty("error_rate") double errorRate,
        @JsonProperty("last_update") java.time.Instant lastUpdate
    ) {}

    public record WindowConfig(
        @JsonProperty("type") String type,
        @JsonProperty("size_ms") long sizeMs,
        @JsonProperty("slide_ms") long slideMs
    ) {}
}