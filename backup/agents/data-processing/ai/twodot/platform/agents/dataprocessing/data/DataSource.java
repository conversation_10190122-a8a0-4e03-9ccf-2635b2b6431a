package com.multiagent.platform.agents.data.ai.twodot.platform.agents.dataprocessing.data;

import java.util.Map;

public class DataSource {
    private final String sourceId;
    private final String type;
    private final Map<String, Object> connectionParams;

    public DataSource(String sourceId, String type, Map<String, Object> connectionParams) {
        this.sourceId = sourceId;
        this.type = type;
        this.connectionParams = connectionParams;
    }

    public String sourceId() {
        return sourceId;
    }

    public String type() {
        return type;
    }

    public Map<String, Object> connectionParams() {
        return connectionParams;
    }
}
