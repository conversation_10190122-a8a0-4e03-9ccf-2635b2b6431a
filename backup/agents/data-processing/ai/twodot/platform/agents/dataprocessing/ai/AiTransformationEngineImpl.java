package com.multiagent.platform.agents.data.ai.twodot.platform.agents.dataprocessing.ai;

import com.multiagent.platform.agents.dataprocessing.agent.DataProcessingAgent;
import org.springframework.stereotype.Service;

@Service
public class AiTransformationEngineImpl implements DataProcessingAgent.AiTransformationEngine {
    @Override
    public DataProcessingAgent.AiTransformationResult executeTransformation(DataProcessingAgent.AiTransformationRequest request) {
        return null;
    }

    @Override
    public boolean isHealthy() {
        return true;
    }
}
