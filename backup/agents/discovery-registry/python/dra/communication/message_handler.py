"""
Message handler for DRA inter-agent communication.

This module provides message handling logic for communication
between the DRA and other agents in the platform.
"""

import asyncio
from typing import Any, Dict, Optional
from datetime import datetime

import structlog

from dra.core.types import AgentMessage, ServiceInfo, ServiceRequirements
from dra.communication.cba_client import CBAClient


logger = structlog.get_logger(__name__)


class MessageHandler:
    """
    Handles incoming messages from other agents via the CBA.
    """

    def __init__(self, cba_client: CBAClient, registry_manager) -> None:
        self.cba_client = cba_client
        self.registry_manager = registry_manager
        
        # Message statistics
        self.stats = {
            "messages_processed": 0,
            "service_requests": 0,
            "health_requests": 0,
            "optimization_requests": 0,
            "errors": 0,
        }

    async def initialize(self) -> None:
        """Initialize the message handler."""
        logger.info("Initializing DRA message handler")
        
        # Register message handlers with CBA client
        await self.cba_client.register_message_handler(
            "service_discovery_request",
            self.handle_service_discovery_request,
        )
        
        await self.cba_client.register_message_handler(
            "service_registration_request",
            self.handle_service_registration_request,
        )
        
        await self.cba_client.register_message_handler(
            "service_health_request",
            self.handle_service_health_request,
        )
        
        await self.cba_client.register_message_handler(
            "optimization_request",
            self.handle_optimization_request,
        )
        
        await self.cba_client.register_message_handler(
            "service_status_update",
            self.handle_service_status_update,
        )
        
        logger.info("DRA message handler initialized")

    async def handle_service_discovery_request(self, message: AgentMessage) -> None:
        """Handle service discovery requests from other agents."""
        try:
            logger.info(
                "Handling service discovery request",
                from_agent=message.from_agent_id,
                correlation_id=message.correlation_id,
            )
            
            # Parse requirements from message
            requirements_data = message.payload.get("requirements")
            if not requirements_data:
                await self._send_error_response(
                    message, "Missing requirements in service discovery request"
                )
                return
            
            requirements = ServiceRequirements.model_validate(requirements_data)
            
            # Perform service discovery
            matches = await self.registry_manager.discover_services(
                requirements=requirements,
                include_unhealthy=message.payload.get("include_unhealthy", False),
            )
            
            # Send response
            response_payload = {
                "matches": [match.to_dict() for match in matches],
                "total_matches": len(matches),
                "timestamp": datetime.utcnow().isoformat(),
            }
            
            await self.cba_client.send_message(
                to_agent_id=message.from_agent_id,
                message_type="service_discovery_response",
                payload=response_payload,
                correlation_id=message.correlation_id,
            )
            
            self.stats["service_requests"] += 1
            self.stats["messages_processed"] += 1
            
            logger.info(
                "Service discovery request completed",
                matches_found=len(matches),
                from_agent=message.from_agent_id,
            )
            
        except Exception as e:
            logger.error(
                "Service discovery request failed",
                error=str(e),
                from_agent=message.from_agent_id,
            )
            await self._send_error_response(message, f"Service discovery failed: {str(e)}")
            self.stats["errors"] += 1

    async def handle_service_registration_request(self, message: AgentMessage) -> None:
        """Handle service registration requests from other agents."""
        try:
            logger.info(
                "Handling service registration request",
                from_agent=message.from_agent_id,
                correlation_id=message.correlation_id,
            )
            
            # Parse service info from message
            service_data = message.payload.get("service")
            if not service_data:
                await self._send_error_response(
                    message, "Missing service info in registration request"
                )
                return
            
            service_info = ServiceInfo.model_validate(service_data)
            
            # Create registration
            from dra.core.types import ServiceRegistration
            registration = ServiceRegistration(
                service=service_info,
                ttl=message.payload.get("ttl"),
                auto_health_check=message.payload.get("auto_health_check", True),
            )
            
            # Register service
            success = await self.registry_manager.register_service(registration)
            
            # Send response
            response_payload = {
                "success": success,
                "service_id": service_info.id,
                "message": "Service registered successfully" if success else "Service registration failed",
                "timestamp": datetime.utcnow().isoformat(),
            }
            
            await self.cba_client.send_message(
                to_agent_id=message.from_agent_id,
                message_type="service_registration_response",
                payload=response_payload,
                correlation_id=message.correlation_id,
            )
            
            # Publish service registration event
            if success:
                await self.cba_client.publish_service_event(
                    "registered",
                    service_info.to_dict(),
                )
            
            self.stats["service_requests"] += 1
            self.stats["messages_processed"] += 1
            
            logger.info(
                "Service registration request completed",
                success=success,
                service_id=service_info.id,
                from_agent=message.from_agent_id,
            )
            
        except Exception as e:
            logger.error(
                "Service registration request failed",
                error=str(e),
                from_agent=message.from_agent_id,
            )
            await self._send_error_response(message, f"Service registration failed: {str(e)}")
            self.stats["errors"] += 1

    async def handle_service_health_request(self, message: AgentMessage) -> None:
        """Handle service health requests from other agents."""
        try:
            logger.info(
                "Handling service health request",
                from_agent=message.from_agent_id,
                correlation_id=message.correlation_id,
            )
            
            service_id = message.payload.get("service_id")
            if not service_id:
                await self._send_error_response(
                    message, "Missing service_id in health request"
                )
                return
            
            # Get service health information
            health_info = await self.registry_manager.get_service_health(service_id)
            
            if health_info is None:
                await self._send_error_response(
                    message, f"Service {service_id} not found or health unavailable"
                )
                return
            
            # Send response
            response_payload = {
                "service_id": service_id,
                "health_info": health_info,
                "timestamp": datetime.utcnow().isoformat(),
            }
            
            await self.cba_client.send_message(
                to_agent_id=message.from_agent_id,
                message_type="service_health_response",
                payload=response_payload,
                correlation_id=message.correlation_id,
            )
            
            self.stats["health_requests"] += 1
            self.stats["messages_processed"] += 1
            
            logger.info(
                "Service health request completed",
                service_id=service_id,
                from_agent=message.from_agent_id,
            )
            
        except Exception as e:
            logger.error(
                "Service health request failed",
                error=str(e),
                from_agent=message.from_agent_id,
            )
            await self._send_error_response(message, f"Health request failed: {str(e)}")
            self.stats["errors"] += 1

    async def handle_optimization_request(self, message: AgentMessage) -> None:
        """Handle optimization recommendation requests from other agents."""
        try:
            logger.info(
                "Handling optimization request",
                from_agent=message.from_agent_id,
                correlation_id=message.correlation_id,
            )
            
            context = message.payload.get("context", {})
            
            # Get optimization recommendations
            recommendations = await self.registry_manager.get_optimization_recommendations(
                context
            )
            
            # Send response
            response_payload = {
                "recommendations": recommendations,
                "context": context,
                "timestamp": datetime.utcnow().isoformat(),
            }
            
            await self.cba_client.send_message(
                to_agent_id=message.from_agent_id,
                message_type="optimization_response",
                payload=response_payload,
                correlation_id=message.correlation_id,
            )
            
            self.stats["optimization_requests"] += 1
            self.stats["messages_processed"] += 1
            
            logger.info(
                "Optimization request completed",
                recommendations_count=len(recommendations),
                from_agent=message.from_agent_id,
            )
            
        except Exception as e:
            logger.error(
                "Optimization request failed",
                error=str(e),
                from_agent=message.from_agent_id,
            )
            await self._send_error_response(message, f"Optimization request failed: {str(e)}")
            self.stats["errors"] += 1

    async def handle_service_status_update(self, message: AgentMessage) -> None:
        """Handle service status updates from other agents."""
        try:
            logger.info(
                "Handling service status update",
                from_agent=message.from_agent_id,
            )
            
            service_id = message.payload.get("service_id")
            new_status = message.payload.get("status")
            
            if not service_id or not new_status:
                logger.warning(
                    "Invalid service status update",
                    service_id=service_id,
                    status=new_status,
                )
                return
            
            # Get current service
            service = await self.registry_manager.get_service(service_id)
            if not service:
                logger.warning("Service not found for status update", service_id=service_id)
                return
            
            # Update service status
            service.status = new_status
            service.last_updated = datetime.utcnow()
            
            success = await self.registry_manager.update_service(service)
            
            if success:
                # Publish status change event
                await self.cba_client.publish_service_event(
                    "status_changed",
                    {
                        "service_id": service_id,
                        "old_status": service.status,
                        "new_status": new_status,
                        "timestamp": datetime.utcnow().isoformat(),
                    },
                )
            
            self.stats["messages_processed"] += 1
            
            logger.info(
                "Service status update completed",
                service_id=service_id,
                new_status=new_status,
                success=success,
            )
            
        except Exception as e:
            logger.error(
                "Service status update failed",
                error=str(e),
                from_agent=message.from_agent_id,
            )
            self.stats["errors"] += 1

    async def _send_error_response(
        self, original_message: AgentMessage, error_message: str
    ) -> None:
        """Send an error response to the requesting agent."""
        try:
            error_payload = {
                "error": True,
                "message": error_message,
                "original_message_type": original_message.message_type,
                "timestamp": datetime.utcnow().isoformat(),
            }
            
            await self.cba_client.send_message(
                to_agent_id=original_message.from_agent_id,
                message_type="error_response",
                payload=error_payload,
                correlation_id=original_message.correlation_id,
            )
            
        except Exception as e:
            logger.error("Failed to send error response", error=str(e))

    async def get_statistics(self) -> Dict[str, Any]:
        """Get message handler statistics."""
        return {
            "message_stats": self.stats,
            "registered_handlers": [
                "service_discovery_request",
                "service_registration_request",
                "service_health_request",
                "optimization_request",
                "service_status_update",
            ],
        }