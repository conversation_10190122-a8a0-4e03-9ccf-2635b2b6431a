"""
Discovery Registry Agent (DRA-002) - Main implementation.

Provides AI-powered service discovery and registry management with intelligent
health monitoring, capability matching, and service mesh optimization.
"""

import asyncio
from datetime import datetime
from typing import Any, Dict, List, Optional

import structlog

from dra.ai.capability_matcher import CapabilityMatchingAI
from dra.ai.health_predictor import HealthPredictionAI
from dra.ai.service_optimizer import ServiceOptimizationAI
from dra.core.platform_agent import PlatformAgent
from dra.core.types import (
    AgentIntelligenceLevel,
    AgentMessage,
    ServiceInfo,
    ServiceRequirements,
    ServiceMatch,
    HealthPrediction,
    OptimizationRecommendation,
)
from dra.discovery.service_catalog import IntelligentServiceCatalog
from dra.discovery.health_monitor import PredictiveHealthMonitor
from dra.discovery.registry_manager import RegistryManager
from dra.monitoring.metrics_collector import MetricsCollector


logger = structlog.get_logger(__name__)


class DiscoveryRegistryAgent(PlatformAgent):
    """
    Discovery Registry Agent implementation providing AI-powered service discovery,
    health monitoring, and capability matching.
    """

    def __init__(self) -> None:
        super().__init__(
            agent_id="DRA-002",
            name="Discovery Registry Agent",
            version="1.0.0",
            intelligence_level=AgentIntelligenceLevel.MEDIUM_HIGH,
            description="AI-powered service discovery and registry management agent",
        )

        # AI Components (initialized in _initialize_agent)
        self.health_predictor: Optional[HealthPredictionAI] = None
        self.capability_matcher: Optional[CapabilityMatchingAI] = None
        self.service_optimizer: Optional[ServiceOptimizationAI] = None

        # Core Components
        self.service_catalog: Optional[IntelligentServiceCatalog] = None
        self.health_monitor: Optional[PredictiveHealthMonitor] = None
        self.registry_manager: Optional[RegistryManager] = None
        self.metrics_collector: Optional[MetricsCollector] = None

    async def _initialize_agent(self) -> None:
        """Initialize DRA-specific components."""
        self.logger.info("Initializing Discovery Registry Agent components")

        try:
            # Initialize AI components
            self.health_predictor = HealthPredictionAI()
            self.capability_matcher = CapabilityMatchingAI()
            self.service_optimizer = ServiceOptimizationAI()

            # Initialize core components
            self.service_catalog = IntelligentServiceCatalog()
            self.health_monitor = PredictiveHealthMonitor(self.health_predictor)
            self.registry_manager = RegistryManager()
            self.metrics_collector = MetricsCollector()

            # Initialize AI components
            await self.health_predictor.initialize()
            await self.capability_matcher.initialize()
            await self.service_optimizer.initialize()

            # Initialize core components
            await self.service_catalog.initialize()
            await self.health_monitor.initialize()
            await self.registry_manager.initialize()
            await self.metrics_collector.initialize()

            self.logger.info("DRA components initialized successfully")

        except Exception as e:
            self.logger.error("Failed to initialize DRA components", error=str(e))
            raise

    async def _start_agent(self) -> None:
        """Start DRA-specific functionality."""
        self.logger.info("Starting Discovery Registry Agent services")

        try:
            # Start core services
            await self.service_catalog.start()
            await self.health_monitor.start()
            await self.registry_manager.start()
            await self.metrics_collector.start()

            self.logger.info("DRA services started successfully")

        except Exception as e:
            self.logger.error("Failed to start DRA services", error=str(e))
            raise

    async def _stop_agent(self) -> None:
        """Stop DRA-specific functionality."""
        self.logger.info("Stopping Discovery Registry Agent services")

        try:
            # Stop services in reverse order
            if self.metrics_collector:
                await self.metrics_collector.stop()
            if self.registry_manager:
                await self.registry_manager.stop()
            if self.health_monitor:
                await self.health_monitor.stop()
            if self.service_catalog:
                await self.service_catalog.stop()

            self.logger.info("DRA services stopped successfully")

        except Exception as e:
            self.logger.error("Error stopping DRA services", error=str(e))

    async def _get_agent_health(self) -> Dict[str, Any]:
        """Get DRA-specific health information."""
        health_details = {
            "service_catalog_health": "unknown",
            "health_monitor_health": "unknown",
            "registry_manager_health": "unknown",
            "ai_components_health": "unknown",
        }

        checks = {}

        try:
            # Check service catalog
            if self.service_catalog:
                catalog_health = await self.service_catalog.get_health()
                health_details["service_catalog_health"] = catalog_health.status
                checks["service_catalog"] = catalog_health.status == "healthy"

            # Check health monitor
            if self.health_monitor:
                monitor_health = await self.health_monitor.get_health()
                health_details["health_monitor_health"] = monitor_health.status
                checks["health_monitor"] = monitor_health.status == "healthy"

            # Check registry manager
            if self.registry_manager:
                registry_health = await self.registry_manager.get_health()
                health_details["registry_manager_health"] = registry_health.status
                checks["registry_manager"] = registry_health.status == "healthy"

            # Check AI components
            ai_healthy = all([
                self.health_predictor and await self.health_predictor.is_healthy(),
                self.capability_matcher and await self.capability_matcher.is_healthy(),
                self.service_optimizer and await self.service_optimizer.is_healthy(),
            ])
            health_details["ai_components_health"] = "healthy" if ai_healthy else "degraded"
            checks["ai_components"] = ai_healthy

            # Overall status
            all_healthy = all(checks.values())
            status = "healthy" if all_healthy else "degraded"

            return {
                "status": status,
                "details": health_details,
                "checks": checks,
            }

        except Exception as e:
            self.logger.error("Health check failed", error=str(e))
            return {
                "status": "unhealthy",
                "details": {"error": str(e)},
                "checks": {"health_check": False},
            }

    async def _process_message(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Process DRA-specific messages."""
        try:
            message_type = message.message_type
            payload = message.payload

            if message_type == "register_service":
                return await self._handle_register_service(message)
            elif message_type == "deregister_service":
                return await self._handle_deregister_service(message)
            elif message_type == "discover_services":
                return await self._handle_discover_services(message)
            elif message_type == "get_health_prediction":
                return await self._handle_get_health_prediction(message)
            elif message_type == "get_optimization_recommendations":
                return await self._handle_get_optimization_recommendations(message)
            else:
                self.logger.warning("Unknown message type", message_type=message_type)
                return None

        except Exception as e:
            self.logger.error("Message processing failed", error=str(e))
            # Return error response
            return AgentMessage(
                from_agent_id=self.agent_id,
                to_agent_id=message.from_agent_id,
                message_type="error",
                payload={"error": str(e), "original_message_id": message.id},
                correlation_id=message.correlation_id,
            )

    async def _send_message(self, message: AgentMessage) -> None:
        """Send message via Communication Broker Agent."""
        # TODO: Implement CBA communication
        # For now, we'll use a placeholder
        self.logger.debug("Sending message via CBA", to_agent=message.to_agent_id)

    def _get_background_tasks(self) -> list:
        """Get DRA-specific background tasks."""
        return [
            self._service_health_monitoring_loop,
            self._metrics_collection_loop,
            self._service_cleanup_loop,
        ]

    # Service Discovery API Methods

    async def register_service(self, service_info: ServiceInfo) -> bool:
        """Register a new service."""
        try:
            if not self.service_catalog:
                raise RuntimeError("Service catalog not initialized")

            # Register the service
            success = await self.service_catalog.register_service(service_info)

            if success:
                # Start health monitoring for the service
                if self.health_monitor:
                    await self.health_monitor.start_monitoring_service(service_info.id)

                self.logger.info("Service registered successfully", service_id=service_info.id)
                self.metrics.services_registered += 1

            return success

        except Exception as e:
            self.logger.error("Service registration failed", error=str(e))
            raise

    async def deregister_service(self, service_id: str) -> bool:
        """Deregister a service."""
        try:
            if not self.service_catalog:
                raise RuntimeError("Service catalog not initialized")

            # Stop health monitoring
            if self.health_monitor:
                await self.health_monitor.stop_monitoring_service(service_id)

            # Deregister the service
            success = await self.service_catalog.deregister_service(service_id)

            if success:
                self.logger.info("Service deregistered successfully", service_id=service_id)

            return success

        except Exception as e:
            self.logger.error("Service deregistration failed", error=str(e))
            raise

    async def discover_services(
        self, requirements: ServiceRequirements
    ) -> List[ServiceMatch]:
        """Discover services matching the given requirements."""
        try:
            if not self.service_catalog or not self.capability_matcher:
                raise RuntimeError("Required components not initialized")

            # Get available services
            available_services = await self.service_catalog.get_services_by_requirements(
                requirements
            )

            # Use AI to match capabilities
            matches = await self.capability_matcher.match_services_to_requirements(
                requirements, available_services
            )

            self.logger.info(
                "Service discovery completed",
                requirements_count=len(requirements.capabilities),
                matches_found=len(matches),
            )

            self.metrics.capability_matches_performed += 1
            return matches

        except Exception as e:
            self.logger.error("Service discovery failed", error=str(e))
            raise

    async def get_health_prediction(self, service_id: str) -> Optional[HealthPrediction]:
        """Get health prediction for a service."""
        try:
            if not self.health_predictor:
                raise RuntimeError("Health predictor not initialized")

            prediction = await self.health_predictor.predict_service_health(service_id)

            if prediction:
                self.logger.info("Health prediction generated", service_id=service_id)
                self.metrics.health_predictions_made += 1

            return prediction

        except Exception as e:
            self.logger.error("Health prediction failed", error=str(e))
            raise

    async def get_optimization_recommendations(
        self, context: Dict[str, Any]
    ) -> List[OptimizationRecommendation]:
        """Get service mesh optimization recommendations."""
        try:
            if not self.service_optimizer:
                raise RuntimeError("Service optimizer not initialized")

            recommendations = await self.service_optimizer.generate_recommendations(context)

            self.logger.info(
                "Optimization recommendations generated",
                recommendation_count=len(recommendations),
            )

            return recommendations

        except Exception as e:
            self.logger.error("Optimization recommendations failed", error=str(e))
            raise

    # Background Task Methods

    async def _service_health_monitoring_loop(self) -> None:
        """Background task for continuous service health monitoring."""
        while not self._shutdown_event.is_set():
            try:
                if self.health_monitor:
                    await self.health_monitor.monitor_all_services()
                await asyncio.sleep(60)  # Monitor every minute
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("Health monitoring loop error", error=str(e))
                await asyncio.sleep(30)

    async def _metrics_collection_loop(self) -> None:
        """Background task for metrics collection."""
        while not self._shutdown_event.is_set():
            try:
                if self.metrics_collector:
                    await self.metrics_collector.collect_metrics()
                await asyncio.sleep(30)  # Collect every 30 seconds
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("Metrics collection loop error", error=str(e))
                await asyncio.sleep(10)

    async def _service_cleanup_loop(self) -> None:
        """Background task for cleaning up stale services."""
        while not self._shutdown_event.is_set():
            try:
                if self.service_catalog:
                    await self.service_catalog.cleanup_stale_services()
                await asyncio.sleep(300)  # Cleanup every 5 minutes
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("Service cleanup loop error", error=str(e))
                await asyncio.sleep(60)

    # Message Handlers

    async def _handle_register_service(self, message: AgentMessage) -> AgentMessage:
        """Handle service registration message."""
        service_data = message.payload.get("service_info")
        if not service_data:
            raise ValueError("Missing service_info in payload")

        service_info = ServiceInfo.model_validate(service_data)
        success = await self.register_service(service_info)

        return AgentMessage(
            from_agent_id=self.agent_id,
            to_agent_id=message.from_agent_id,
            message_type="register_service_response",
            payload={"success": success, "service_id": service_info.id},
            correlation_id=message.correlation_id,
        )

    async def _handle_deregister_service(self, message: AgentMessage) -> AgentMessage:
        """Handle service deregistration message."""
        service_id = message.payload.get("service_id")
        if not service_id:
            raise ValueError("Missing service_id in payload")

        success = await self.deregister_service(service_id)

        return AgentMessage(
            from_agent_id=self.agent_id,
            to_agent_id=message.from_agent_id,
            message_type="deregister_service_response",
            payload={"success": success, "service_id": service_id},
            correlation_id=message.correlation_id,
        )

    async def _handle_discover_services(self, message: AgentMessage) -> AgentMessage:
        """Handle service discovery message."""
        requirements_data = message.payload.get("requirements")
        if not requirements_data:
            raise ValueError("Missing requirements in payload")

        requirements = ServiceRequirements.model_validate(requirements_data)
        matches = await self.discover_services(requirements)

        return AgentMessage(
            from_agent_id=self.agent_id,
            to_agent_id=message.from_agent_id,
            message_type="discover_services_response",
            payload={
                "matches": [match.model_dump() for match in matches],
                "match_count": len(matches),
            },
            correlation_id=message.correlation_id,
        )

    async def _handle_get_health_prediction(self, message: AgentMessage) -> AgentMessage:
        """Handle health prediction request."""
        service_id = message.payload.get("service_id")
        if not service_id:
            raise ValueError("Missing service_id in payload")

        prediction = await self.get_health_prediction(service_id)

        return AgentMessage(
            from_agent_id=self.agent_id,
            to_agent_id=message.from_agent_id,
            message_type="health_prediction_response",
            payload={
                "prediction": prediction.model_dump() if prediction else None,
                "service_id": service_id,
            },
            correlation_id=message.correlation_id,
        )

    async def _handle_get_optimization_recommendations(
        self, message: AgentMessage
    ) -> AgentMessage:
        """Handle optimization recommendations request."""
        context = message.payload.get("context", {})
        recommendations = await self.get_optimization_recommendations(context)

        return AgentMessage(
            from_agent_id=self.agent_id,
            to_agent_id=message.from_agent_id,
            message_type="optimization_recommendations_response",
            payload={
                "recommendations": [rec.model_dump() for rec in recommendations],
                "recommendation_count": len(recommendations),
            },
            correlation_id=message.correlation_id,
        )