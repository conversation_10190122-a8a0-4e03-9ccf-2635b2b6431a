"""
Anomaly detection component for performance analytics.

This module provides statistical anomaly detection capabilities for service
performance metrics using various techniques including statistical analysis,
isolation forests, and time series analysis.
"""

import asyncio
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass

import structlog
import numpy as np
from scipy import stats
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler

from dra.analytics.performance_engine import PerformanceMetric


logger = structlog.get_logger(__name__)


@dataclass
class Anomaly:
    """Detected anomaly data point."""
    timestamp: datetime
    service_id: str
    metric_name: str
    value: float
    expected_value: float
    confidence: float  # 0.0 to 1.0
    anomaly_type: str  # statistical, isolation, seasonal
    severity: str      # low, medium, high


class AnomalyDetector:
    """
    Multi-method anomaly detector for service performance metrics.
    
    Uses statistical analysis, isolation forests, and time series techniques
    to identify anomalous behavior in service metrics.
    """

    def __init__(self) -> None:
        self.initialized = False
        
        # Configuration
        self.statistical_threshold = 3.0  # Standard deviations
        self.isolation_contamination = 0.1  # Expected anomaly rate
        self.min_data_points = 20
        self.window_size = 50  # For rolling statistics
        
        # Models and state
        self.isolation_forests: Dict[str, IsolationForest] = {}
        self.scalers: Dict[str, StandardScaler] = {}
        self.baselines: Dict[str, Dict[str, float]] = {}

    async def initialize(self) -> None:
        """Initialize the anomaly detector."""
        logger.info("Initializing Anomaly Detector")
        self.initialized = True
        logger.info("Anomaly Detector initialized successfully")

    async def detect_anomalies(
        self,
        service_id: str,
        metric_name: str,
        metrics: List[PerformanceMetric],
    ) -> List[Anomaly]:
        """Detect anomalies in performance metrics."""
        try:
            if len(metrics) < self.min_data_points:
                return []
            
            # Sort metrics by timestamp
            sorted_metrics = sorted(metrics, key=lambda m: m.timestamp)
            values = np.array([m.value for m in sorted_metrics])
            timestamps = [m.timestamp for m in sorted_metrics]
            
            anomalies = []
            
            # Statistical anomaly detection
            statistical_anomalies = await self._detect_statistical_anomalies(
                service_id, metric_name, values, timestamps
            )
            anomalies.extend(statistical_anomalies)
            
            # Isolation forest anomaly detection
            if len(values) >= 30:  # Need more data for isolation forest
                isolation_anomalies = await self._detect_isolation_anomalies(
                    service_id, metric_name, values, timestamps
                )
                anomalies.extend(isolation_anomalies)
            
            # Seasonal anomaly detection (basic)
            if len(values) >= 100:  # Need substantial data for seasonal analysis
                seasonal_anomalies = await self._detect_seasonal_anomalies(
                    service_id, metric_name, values, timestamps
                )
                anomalies.extend(seasonal_anomalies)
            
            # Deduplicate anomalies (same timestamp)
            unique_anomalies = self._deduplicate_anomalies(anomalies)
            
            return unique_anomalies
            
        except Exception as e:
            logger.error(
                "Anomaly detection failed",
                service_id=service_id,
                metric_name=metric_name,
                error=str(e),
            )
            return []

    async def _detect_statistical_anomalies(
        self,
        service_id: str,
        metric_name: str,
        values: np.ndarray,
        timestamps: List[datetime],
    ) -> List[Anomaly]:
        """Detect anomalies using statistical methods."""
        try:
            anomalies = []
            
            # Use rolling window for dynamic baseline
            window_size = min(self.window_size, len(values) // 2)
            if window_size < 10:
                window_size = min(10, len(values) - 5)
            
            for i in range(window_size, len(values)):
                # Calculate statistics for window
                window_values = values[i - window_size:i]
                mean = np.mean(window_values)
                std = np.std(window_values)
                
                if std == 0:  # No variation, skip
                    continue
                
                # Check if current value is anomalous
                current_value = values[i]
                z_score = abs((current_value - mean) / std)
                
                if z_score > self.statistical_threshold:
                    # Determine severity based on z-score
                    if z_score > 5.0:
                        severity = "high"
                        confidence = min(0.95, z_score / 10.0)
                    elif z_score > 4.0:
                        severity = "medium"
                        confidence = min(0.85, z_score / 8.0)
                    else:
                        severity = "low"
                        confidence = min(0.75, z_score / 6.0)
                    
                    anomaly = Anomaly(
                        timestamp=timestamps[i],
                        service_id=service_id,
                        metric_name=metric_name,
                        value=current_value,
                        expected_value=mean,
                        confidence=confidence,
                        anomaly_type="statistical",
                        severity=severity,
                    )
                    anomalies.append(anomaly)
            
            return anomalies
            
        except Exception as e:
            logger.error("Statistical anomaly detection failed", error=str(e))
            return []

    async def _detect_isolation_anomalies(
        self,
        service_id: str,
        metric_name: str,
        values: np.ndarray,
        timestamps: List[datetime],
    ) -> List[Anomaly]:
        """Detect anomalies using Isolation Forest."""
        try:
            anomalies = []
            key = f"{service_id}:{metric_name}"
            
            # Prepare features (value and time-based features)
            features = self._prepare_features(values, timestamps)
            
            # Get or create isolation forest
            if key not in self.isolation_forests:
                self.isolation_forests[key] = IsolationForest(
                    contamination=self.isolation_contamination,
                    random_state=42,
                    n_estimators=100,
                )
                self.scalers[key] = StandardScaler()
            
            # Scale features
            scaler = self.scalers[key]
            try:
                scaled_features = scaler.fit_transform(features)
            except ValueError:
                # Handle case where all values are the same
                return []
            
            # Fit and predict
            isolation_forest = self.isolation_forests[key]
            isolation_forest.fit(scaled_features)
            predictions = isolation_forest.predict(scaled_features)
            anomaly_scores = isolation_forest.decision_function(scaled_features)
            
            # Convert predictions to anomalies
            for i, (pred, score) in enumerate(zip(predictions, anomaly_scores)):
                if pred == -1:  # Anomaly detected
                    # Calculate confidence from anomaly score
                    # More negative scores indicate stronger anomalies
                    confidence = min(0.95, max(0.5, abs(score) / 0.5))
                    
                    # Determine severity based on score
                    if score < -0.4:
                        severity = "high"
                    elif score < -0.2:
                        severity = "medium"
                    else:
                        severity = "low"
                    
                    # Estimate expected value using local mean
                    window_start = max(0, i - 10)
                    window_end = min(len(values), i + 10)
                    local_values = values[window_start:window_end]
                    local_values = local_values[local_values != values[i]]  # Exclude current value
                    expected_value = np.mean(local_values) if len(local_values) > 0 else values[i]
                    
                    anomaly = Anomaly(
                        timestamp=timestamps[i],
                        service_id=service_id,
                        metric_name=metric_name,
                        value=values[i],
                        expected_value=expected_value,
                        confidence=confidence,
                        anomaly_type="isolation",
                        severity=severity,
                    )
                    anomalies.append(anomaly)
            
            return anomalies
            
        except Exception as e:
            logger.error("Isolation forest anomaly detection failed", error=str(e))
            return []

    async def _detect_seasonal_anomalies(
        self,
        service_id: str,
        metric_name: str,
        values: np.ndarray,
        timestamps: List[datetime],
    ) -> List[Anomaly]:
        """Detect seasonal anomalies using simple time-based patterns."""
        try:
            anomalies = []
            
            # Group values by hour of day for daily seasonality
            hourly_patterns = {}
            for i, timestamp in enumerate(timestamps):
                hour = timestamp.hour
                if hour not in hourly_patterns:
                    hourly_patterns[hour] = []
                hourly_patterns[hour].append(values[i])
            
            # Calculate statistics for each hour
            hourly_stats = {}
            for hour, hour_values in hourly_patterns.items():
                if len(hour_values) >= 3:  # Need at least 3 points
                    hourly_stats[hour] = {
                        "mean": np.mean(hour_values),
                        "std": np.std(hour_values),
                    }
            
            # Check for seasonal anomalies
            for i, timestamp in enumerate(timestamps):
                hour = timestamp.hour
                if hour in hourly_stats:
                    stats_data = hourly_stats[hour]
                    if stats_data["std"] > 0:
                        z_score = abs((values[i] - stats_data["mean"]) / stats_data["std"])
                        
                        if z_score > 2.5:  # More lenient threshold for seasonal
                            confidence = min(0.8, z_score / 5.0)
                            severity = "medium" if z_score > 3.5 else "low"
                            
                            anomaly = Anomaly(
                                timestamp=timestamp,
                                service_id=service_id,
                                metric_name=metric_name,
                                value=values[i],
                                expected_value=stats_data["mean"],
                                confidence=confidence,
                                anomaly_type="seasonal",
                                severity=severity,
                            )
                            anomalies.append(anomaly)
            
            return anomalies
            
        except Exception as e:
            logger.error("Seasonal anomaly detection failed", error=str(e))
            return []

    def _prepare_features(
        self, values: np.ndarray, timestamps: List[datetime]
    ) -> np.ndarray:
        """Prepare feature matrix for machine learning models."""
        try:
            features = []
            
            for i, timestamp in enumerate(timestamps):
                feature_vector = [
                    values[i],  # Current value
                    timestamp.hour,  # Hour of day
                    timestamp.weekday(),  # Day of week
                    timestamp.day,  # Day of month
                ]
                
                # Add recent values as features (if available)
                if i > 0:
                    feature_vector.append(values[i - 1])  # Previous value
                else:
                    feature_vector.append(values[i])
                
                if i > 1:
                    feature_vector.append(values[i - 2])  # Value before previous
                else:
                    feature_vector.append(values[i])
                
                # Add moving average
                window_start = max(0, i - 5)
                window_values = values[window_start:i + 1]
                feature_vector.append(np.mean(window_values))
                
                features.append(feature_vector)
            
            return np.array(features)
            
        except Exception as e:
            logger.error("Feature preparation failed", error=str(e))
            return np.array([])

    def _deduplicate_anomalies(self, anomalies: List[Anomaly]) -> List[Anomaly]:
        """Remove duplicate anomalies at the same timestamp."""
        try:
            if not anomalies:
                return []
            
            # Group by timestamp
            timestamp_groups = {}
            for anomaly in anomalies:
                timestamp_key = anomaly.timestamp.isoformat()
                if timestamp_key not in timestamp_groups:
                    timestamp_groups[timestamp_key] = []
                timestamp_groups[timestamp_key].append(anomaly)
            
            # Keep the anomaly with highest confidence for each timestamp
            unique_anomalies = []
            for group in timestamp_groups.values():
                if len(group) == 1:
                    unique_anomalies.append(group[0])
                else:
                    # Keep the one with highest confidence
                    best_anomaly = max(group, key=lambda a: a.confidence)
                    unique_anomalies.append(best_anomaly)
            
            # Sort by timestamp
            unique_anomalies.sort(key=lambda a: a.timestamp)
            return unique_anomalies
            
        except Exception as e:
            logger.error("Anomaly deduplication failed", error=str(e))
            return anomalies

    async def update_baseline(
        self,
        service_id: str,
        metric_name: str,
        metrics: List[PerformanceMetric],
    ) -> None:
        """Update baseline statistics for a metric."""
        try:
            if len(metrics) < 10:
                return
            
            values = [m.value for m in metrics]
            key = f"{service_id}:{metric_name}"
            
            self.baselines[key] = {
                "mean": np.mean(values),
                "std": np.std(values),
                "min": np.min(values),
                "max": np.max(values),
                "p50": np.percentile(values, 50),
                "p95": np.percentile(values, 95),
                "count": len(values),
                "updated_at": datetime.utcnow().isoformat(),
            }
            
        except Exception as e:
            logger.error("Baseline update failed", error=str(e))

    async def get_anomaly_statistics(self) -> Dict[str, Any]:
        """Get anomaly detection statistics."""
        try:
            return {
                "models": {
                    "isolation_forests": len(self.isolation_forests),
                    "scalers": len(self.scalers),
                    "baselines": len(self.baselines),
                },
                "configuration": {
                    "statistical_threshold": self.statistical_threshold,
                    "isolation_contamination": self.isolation_contamination,
                    "min_data_points": self.min_data_points,
                    "window_size": self.window_size,
                },
                "status": {
                    "initialized": self.initialized,
                },
            }
            
        except Exception as e:
            logger.error("Failed to get anomaly statistics", error=str(e))
            return {"error": str(e)}