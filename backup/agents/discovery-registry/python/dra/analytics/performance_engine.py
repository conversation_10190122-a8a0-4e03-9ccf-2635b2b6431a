"""
Performance analytics engine for the Discovery Registry Agent.

This module provides comprehensive performance analysis, trend detection,
and predictive analytics for service discovery operations and mesh performance.
"""

import asyncio
import json
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import defaultdict, deque

import structlog
import numpy as np
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import DBSCAN

from dra.core.types import ServiceInfo, ServiceMetrics


logger = structlog.get_logger(__name__)


@dataclass
class PerformanceMetric:
    """Performance metric data point."""
    timestamp: datetime
    service_id: str
    metric_name: str
    value: float
    tags: Dict[str, str] = field(default_factory=dict)


@dataclass
class PerformanceAlert:
    """Performance alert."""
    alert_id: str
    service_id: str
    metric_name: str
    alert_type: str  # threshold, anomaly, trend
    severity: str    # low, medium, high, critical
    message: str
    timestamp: datetime
    current_value: float
    threshold_value: Optional[float] = None
    confidence: float = 0.0


@dataclass
class PerformanceTrend:
    """Performance trend analysis result."""
    service_id: str
    metric_name: str
    trend_direction: str  # increasing, decreasing, stable
    trend_strength: float  # 0.0 to 1.0
    trend_duration: timedelta
    forecast: Optional[List[float]] = None
    confidence_intervals: Optional[List[Tuple[float, float]]] = None


class PerformanceAnalyticsEngine:
    """
    Comprehensive performance analytics engine that provides:
    - Real-time performance monitoring
    - Trend analysis and forecasting
    - Anomaly detection
    - Performance alerting
    - Capacity planning insights
    """

    def __init__(
        self,
        retention_period: timedelta = timedelta(days=30),
        analysis_interval: int = 60,  # seconds
    ) -> None:
        self.retention_period = retention_period
        self.analysis_interval = analysis_interval
        
        # Data storage
        self.metrics_buffer: Dict[str, deque] = defaultdict(
            lambda: deque(maxlen=10000)
        )
        self.performance_baselines: Dict[str, Dict[str, float]] = {}
        self.alerts_history: List[PerformanceAlert] = []
        
        # Analysis components
        self.anomaly_detector = None  # Will be initialized
        self.trend_analyzer = None    # Will be initialized
        
        # Configuration
        self.alert_thresholds = {
            "response_time_p95": {
                "warning": 1000.0,   # ms
                "critical": 5000.0,  # ms
            },
            "error_rate": {
                "warning": 0.05,     # 5%
                "critical": 0.1,     # 10%
            },
            "throughput": {
                "warning": 100.0,    # req/s (minimum)
                "critical": 50.0,    # req/s (minimum)
            },
            "availability": {
                "warning": 0.95,     # 95%
                "critical": 0.90,    # 90%
            },
        }
        
        # Background tasks
        self.analysis_task: Optional[asyncio.Task] = None
        self.cleanup_task: Optional[asyncio.Task] = None
        self.running = False
        
        # Statistics
        self.stats = {
            "metrics_processed": 0,
            "alerts_generated": 0,
            "trends_analyzed": 0,
            "anomalies_detected": 0,
        }

    async def initialize(self) -> None:
        """Initialize the performance analytics engine."""
        logger.info("Initializing Performance Analytics Engine")
        
        try:
            # Initialize analysis components
            from dra.analytics.anomaly_detector import AnomalyDetector
            from dra.analytics.trend_analyzer import TrendAnalyzer
            
            self.anomaly_detector = AnomalyDetector()
            self.trend_analyzer = TrendAnalyzer()
            
            await self.anomaly_detector.initialize()
            await self.trend_analyzer.initialize()
            
            self.running = True
            
            # Start background tasks
            self.analysis_task = asyncio.create_task(self._analysis_loop())
            self.cleanup_task = asyncio.create_task(self._cleanup_loop())
            
            logger.info("Performance Analytics Engine initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize Performance Analytics Engine", error=str(e))
            raise

    async def shutdown(self) -> None:
        """Shutdown the performance analytics engine."""
        logger.info("Shutting down Performance Analytics Engine")
        
        self.running = False
        
        # Stop background tasks
        if self.analysis_task:
            self.analysis_task.cancel()
            try:
                await self.analysis_task
            except asyncio.CancelledError:
                pass
        
        if self.cleanup_task:
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Performance Analytics Engine shutdown complete")

    async def record_metric(
        self,
        service_id: str,
        metric_name: str,
        value: float,
        tags: Optional[Dict[str, str]] = None,
    ) -> None:
        """Record a performance metric."""
        try:
            metric = PerformanceMetric(
                timestamp=datetime.utcnow(),
                service_id=service_id,
                metric_name=metric_name,
                value=value,
                tags=tags or {},
            )
            
            # Store metric
            key = f"{service_id}:{metric_name}"
            self.metrics_buffer[key].append(metric)
            
            # Check for immediate alerts
            await self._check_threshold_alerts(metric)
            
            self.stats["metrics_processed"] += 1
            
        except Exception as e:
            logger.error("Failed to record metric", error=str(e))

    async def record_service_metrics(self, service: ServiceInfo) -> None:
        """Record all metrics for a service."""
        try:
            if not service.metrics:
                return
            
            metrics_to_record = [
                ("response_time_p95", service.metrics.response_time_p95),
                ("error_rate", service.metrics.error_rate),
                ("availability", service.metrics.availability),
                ("throughput", service.metrics.throughput),
            ]
            
            for metric_name, value in metrics_to_record:
                await self.record_metric(
                    service.id,
                    metric_name,
                    value,
                    tags={"namespace": service.namespace, "version": service.version},
                )
            
        except Exception as e:
            logger.error("Failed to record service metrics", error=str(e))

    async def get_performance_summary(
        self, service_id: str, time_range: timedelta = timedelta(hours=1)
    ) -> Dict[str, Any]:
        """Get performance summary for a service."""
        try:
            since = datetime.utcnow() - time_range
            summary = {
                "service_id": service_id,
                "time_range": time_range.total_seconds(),
                "metrics": {},
                "alerts": [],
                "trends": [],
            }
            
            # Gather metrics
            for metric_name in ["response_time_p95", "error_rate", "availability", "throughput"]:
                key = f"{service_id}:{metric_name}"
                metrics = [
                    m for m in self.metrics_buffer[key]
                    if m.timestamp >= since
                ]
                
                if metrics:
                    values = [m.value for m in metrics]
                    summary["metrics"][metric_name] = {
                        "current": values[-1],
                        "average": np.mean(values),
                        "min": np.min(values),
                        "max": np.max(values),
                        "std": np.std(values),
                        "count": len(values),
                    }
            
            # Gather recent alerts
            summary["alerts"] = [
                alert for alert in self.alerts_history[-50:]
                if alert.service_id == service_id and alert.timestamp >= since
            ]
            
            # Get trends
            summary["trends"] = await self.get_service_trends(service_id)
            
            return summary
            
        except Exception as e:
            logger.error("Failed to get performance summary", error=str(e))
            return {"error": str(e)}

    async def get_service_trends(self, service_id: str) -> List[PerformanceTrend]:
        """Get performance trends for a service."""
        try:
            if not self.trend_analyzer:
                return []
            
            trends = []
            
            for metric_name in ["response_time_p95", "error_rate", "availability", "throughput"]:
                key = f"{service_id}:{metric_name}"
                metrics = list(self.metrics_buffer[key])
                
                if len(metrics) >= 10:  # Minimum data points for trend analysis
                    trend = await self.trend_analyzer.analyze_trend(
                        service_id, metric_name, metrics
                    )
                    if trend:
                        trends.append(trend)
            
            return trends
            
        except Exception as e:
            logger.error("Failed to get service trends", error=str(e))
            return []

    async def detect_anomalies(
        self, service_id: str, metric_name: str
    ) -> List[PerformanceAlert]:
        """Detect anomalies in service metrics."""
        try:
            if not self.anomaly_detector:
                return []
            
            key = f"{service_id}:{metric_name}"
            metrics = list(self.metrics_buffer[key])
            
            if len(metrics) < 20:  # Minimum data points for anomaly detection
                return []
            
            anomalies = await self.anomaly_detector.detect_anomalies(
                service_id, metric_name, metrics
            )
            
            alerts = []
            for anomaly in anomalies:
                alert = PerformanceAlert(
                    alert_id=f"anomaly_{service_id}_{metric_name}_{int(anomaly.timestamp.timestamp())}",
                    service_id=service_id,
                    metric_name=metric_name,
                    alert_type="anomaly",
                    severity="medium",
                    message=f"Anomalous {metric_name} detected: {anomaly.value:.2f}",
                    timestamp=anomaly.timestamp,
                    current_value=anomaly.value,
                    confidence=anomaly.confidence,
                )
                alerts.append(alert)
                self.alerts_history.append(alert)
            
            if alerts:
                self.stats["anomalies_detected"] += len(alerts)
                self.stats["alerts_generated"] += len(alerts)
            
            return alerts
            
        except Exception as e:
            logger.error("Failed to detect anomalies", error=str(e))
            return []

    async def generate_capacity_recommendations(
        self, service_id: str
    ) -> List[Dict[str, Any]]:
        """Generate capacity planning recommendations."""
        try:
            recommendations = []
            
            # Analyze throughput trends
            throughput_trends = await self.get_service_trends(service_id)
            throughput_trend = next(
                (t for t in throughput_trends if t.metric_name == "throughput"), None
            )
            
            if throughput_trend and throughput_trend.trend_direction == "increasing":
                recommendations.append({
                    "type": "capacity_scaling",
                    "priority": "medium",
                    "message": f"Throughput trending upward ({throughput_trend.trend_strength:.1%}), consider scaling",
                    "forecast_horizon": "7 days",
                    "confidence": throughput_trend.trend_strength,
                })
            
            # Analyze response time trends
            response_time_trend = next(
                (t for t in throughput_trends if t.metric_name == "response_time_p95"), None
            )
            
            if response_time_trend and response_time_trend.trend_direction == "increasing":
                recommendations.append({
                    "type": "performance_optimization",
                    "priority": "high",
                    "message": f"Response time trending upward ({response_time_trend.trend_strength:.1%}), optimization needed",
                    "forecast_horizon": "3 days",
                    "confidence": response_time_trend.trend_strength,
                })
            
            return recommendations
            
        except Exception as e:
            logger.error("Failed to generate capacity recommendations", error=str(e))
            return []

    async def get_system_health_score(self) -> Dict[str, Any]:
        """Calculate overall system health score."""
        try:
            if not self.metrics_buffer:
                return {"score": 1.0, "status": "unknown", "details": "No metrics available"}
            
            # Calculate health scores for each service
            service_scores = {}
            total_score = 0.0
            service_count = 0
            
            services = set()
            for key in self.metrics_buffer.keys():
                service_id = key.split(":")[0]
                services.add(service_id)
            
            for service_id in services:
                service_score = await self._calculate_service_health_score(service_id)
                service_scores[service_id] = service_score
                total_score += service_score
                service_count += 1
            
            overall_score = total_score / service_count if service_count > 0 else 1.0
            
            # Determine status
            if overall_score >= 0.9:
                status = "excellent"
            elif overall_score >= 0.8:
                status = "good"
            elif overall_score >= 0.7:
                status = "fair"
            elif overall_score >= 0.5:
                status = "poor"
            else:
                status = "critical"
            
            return {
                "score": overall_score,
                "status": status,
                "service_count": service_count,
                "service_scores": service_scores,
                "recent_alerts": len([
                    a for a in self.alerts_history[-100:]
                    if a.timestamp >= datetime.utcnow() - timedelta(hours=1)
                ]),
            }
            
        except Exception as e:
            logger.error("Failed to calculate system health score", error=str(e))
            return {"score": 0.0, "status": "error", "error": str(e)}

    async def get_analytics_statistics(self) -> Dict[str, Any]:
        """Get analytics engine statistics."""
        try:
            return {
                "engine_stats": self.stats,
                "data_stats": {
                    "metrics_buffer_size": sum(len(buffer) for buffer in self.metrics_buffer.values()),
                    "tracked_services": len(set(key.split(":")[0] for key in self.metrics_buffer.keys())),
                    "tracked_metrics": len(self.metrics_buffer),
                    "alerts_history_size": len(self.alerts_history),
                },
                "configuration": {
                    "retention_period_days": self.retention_period.days,
                    "analysis_interval_seconds": self.analysis_interval,
                    "alert_thresholds": self.alert_thresholds,
                },
                "component_status": {
                    "anomaly_detector": self.anomaly_detector is not None,
                    "trend_analyzer": self.trend_analyzer is not None,
                    "running": self.running,
                },
            }
            
        except Exception as e:
            logger.error("Failed to get analytics statistics", error=str(e))
            return {"error": str(e)}

    # Private methods

    async def _analysis_loop(self) -> None:
        """Background analysis loop."""
        logger.info("Starting performance analytics loop")
        
        while self.running:
            try:
                # Run periodic analysis
                await self._run_periodic_analysis()
                
                await asyncio.sleep(self.analysis_interval)
                
            except Exception as e:
                logger.error("Error in analytics loop", error=str(e))
                await asyncio.sleep(self.analysis_interval)

    async def _cleanup_loop(self) -> None:
        """Background cleanup loop."""
        logger.info("Starting analytics cleanup loop")
        
        while self.running:
            try:
                # Clean up old data
                await self._cleanup_old_data()
                
                # Sleep for 1 hour between cleanups
                await asyncio.sleep(3600)
                
            except Exception as e:
                logger.error("Error in cleanup loop", error=str(e))
                await asyncio.sleep(3600)

    async def _run_periodic_analysis(self) -> None:
        """Run periodic analysis tasks."""
        try:
            # Get all services with recent metrics
            services = set()
            recent_time = datetime.utcnow() - timedelta(minutes=10)
            
            for key, metrics in self.metrics_buffer.items():
                service_id = key.split(":")[0]
                if any(m.timestamp >= recent_time for m in metrics):
                    services.add(service_id)
            
            # Run analysis for each service
            for service_id in services:
                try:
                    # Detect anomalies
                    for metric_name in ["response_time_p95", "error_rate", "availability", "throughput"]:
                        await self.detect_anomalies(service_id, metric_name)
                    
                    # Update baselines
                    await self._update_performance_baselines(service_id)
                    
                except Exception as e:
                    logger.error(
                        "Analysis failed for service",
                        service_id=service_id,
                        error=str(e),
                    )
            
            # Analyze trends
            self.stats["trends_analyzed"] += len(services)
            
        except Exception as e:
            logger.error("Periodic analysis failed", error=str(e))

    async def _cleanup_old_data(self) -> None:
        """Clean up old performance data."""
        try:
            cutoff_time = datetime.utcnow() - self.retention_period
            
            # Clean metrics buffer
            for key, metrics in self.metrics_buffer.items():
                original_size = len(metrics)
                # Remove old metrics
                while metrics and metrics[0].timestamp < cutoff_time:
                    metrics.popleft()
                
                if len(metrics) < original_size:
                    logger.debug(
                        "Cleaned old metrics",
                        key=key,
                        removed=original_size - len(metrics),
                    )
            
            # Clean alerts history
            self.alerts_history = [
                alert for alert in self.alerts_history
                if alert.timestamp >= cutoff_time
            ]
            
        except Exception as e:
            logger.error("Data cleanup failed", error=str(e))

    async def _check_threshold_alerts(self, metric: PerformanceMetric) -> None:
        """Check if metric violates thresholds."""
        try:
            thresholds = self.alert_thresholds.get(metric.metric_name)
            if not thresholds:
                return
            
            severity = None
            threshold_value = None
            
            # Check thresholds based on metric type
            if metric.metric_name in ["response_time_p95"]:
                # Higher is worse
                if metric.value >= thresholds["critical"]:
                    severity = "critical"
                    threshold_value = thresholds["critical"]
                elif metric.value >= thresholds["warning"]:
                    severity = "warning"
                    threshold_value = thresholds["warning"]
            
            elif metric.metric_name in ["error_rate"]:
                # Higher is worse
                if metric.value >= thresholds["critical"]:
                    severity = "critical"
                    threshold_value = thresholds["critical"]
                elif metric.value >= thresholds["warning"]:
                    severity = "warning"
                    threshold_value = thresholds["warning"]
            
            elif metric.metric_name in ["throughput", "availability"]:
                # Lower is worse
                if metric.value <= thresholds["critical"]:
                    severity = "critical"
                    threshold_value = thresholds["critical"]
                elif metric.value <= thresholds["warning"]:
                    severity = "warning"
                    threshold_value = thresholds["warning"]
            
            if severity:
                alert = PerformanceAlert(
                    alert_id=f"threshold_{metric.service_id}_{metric.metric_name}_{int(metric.timestamp.timestamp())}",
                    service_id=metric.service_id,
                    metric_name=metric.metric_name,
                    alert_type="threshold",
                    severity=severity,
                    message=f"{metric.metric_name} {severity} threshold exceeded: {metric.value:.2f}",
                    timestamp=metric.timestamp,
                    current_value=metric.value,
                    threshold_value=threshold_value,
                    confidence=1.0,
                )
                
                self.alerts_history.append(alert)
                self.stats["alerts_generated"] += 1
                
                logger.warning(
                    "Threshold alert generated",
                    service_id=metric.service_id,
                    metric_name=metric.metric_name,
                    severity=severity,
                    value=metric.value,
                    threshold=threshold_value,
                )
            
        except Exception as e:
            logger.error("Threshold check failed", error=str(e))

    async def _update_performance_baselines(self, service_id: str) -> None:
        """Update performance baselines for a service."""
        try:
            baselines = {}
            
            for metric_name in ["response_time_p95", "error_rate", "availability", "throughput"]:
                key = f"{service_id}:{metric_name}"
                metrics = [
                    m for m in self.metrics_buffer[key]
                    if m.timestamp >= datetime.utcnow() - timedelta(days=7)
                ]
                
                if len(metrics) >= 10:
                    values = [m.value for m in metrics]
                    baselines[metric_name] = {
                        "mean": np.mean(values),
                        "std": np.std(values),
                        "p50": np.percentile(values, 50),
                        "p95": np.percentile(values, 95),
                        "p99": np.percentile(values, 99),
                    }
            
            if baselines:
                self.performance_baselines[service_id] = baselines
            
        except Exception as e:
            logger.error("Baseline update failed", error=str(e))

    async def _calculate_service_health_score(self, service_id: str) -> float:
        """Calculate health score for a service (0.0 to 1.0)."""
        try:
            scores = []
            
            # Check recent metrics
            recent_time = datetime.utcnow() - timedelta(minutes=30)
            
            for metric_name in ["response_time_p95", "error_rate", "availability", "throughput"]:
                key = f"{service_id}:{metric_name}"
                recent_metrics = [
                    m for m in self.metrics_buffer[key]
                    if m.timestamp >= recent_time
                ]
                
                if recent_metrics:
                    current_value = recent_metrics[-1].value
                    thresholds = self.alert_thresholds.get(metric_name)
                    
                    if thresholds:
                        if metric_name in ["response_time_p95", "error_rate"]:
                            # Lower is better
                            if current_value <= thresholds["warning"]:
                                scores.append(1.0)
                            elif current_value <= thresholds["critical"]:
                                scores.append(0.5)
                            else:
                                scores.append(0.1)
                        else:
                            # Higher is better
                            if current_value >= thresholds["warning"]:
                                scores.append(1.0)
                            elif current_value >= thresholds["critical"]:
                                scores.append(0.5)
                            else:
                                scores.append(0.1)
            
            return np.mean(scores) if scores else 1.0
            
        except Exception as e:
            logger.error("Health score calculation failed", error=str(e))
            return 0.5