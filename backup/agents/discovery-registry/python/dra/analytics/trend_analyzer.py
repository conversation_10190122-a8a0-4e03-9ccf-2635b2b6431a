"""
Trend analysis component for performance analytics.

This module provides trend detection, forecasting, and analysis
capabilities for service performance metrics.
"""

import asyncio
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timedelta

import structlog
import numpy as np
from scipy import stats
from scipy.signal import savgol_filter

from dra.analytics.performance_engine import PerformanceMetric, PerformanceTrend


logger = structlog.get_logger(__name__)


class TrendAnalyzer:
    """
    Analyzes performance trends and provides forecasting capabilities.
    """

    def __init__(self) -> None:
        self.initialized = False
        
        # Configuration
        self.min_data_points = 10
        self.smoothing_window = 5
        self.trend_threshold = 0.1  # Minimum correlation for trend detection

    async def initialize(self) -> None:
        """Initialize the trend analyzer."""
        logger.info("Initializing Trend Analyzer")
        self.initialized = True
        logger.info("Trend Analyzer initialized successfully")

    async def analyze_trend(
        self,
        service_id: str,
        metric_name: str,
        metrics: List[PerformanceMetric],
    ) -> Optional[PerformanceTrend]:
        """Analyze trend in performance metrics."""
        try:
            if len(metrics) < self.min_data_points:
                return None
            
            # Extract values and timestamps
            values = np.array([m.value for m in metrics])
            timestamps = np.array([m.timestamp.timestamp() for m in metrics])
            
            # Sort by timestamp
            sort_indices = np.argsort(timestamps)
            values = values[sort_indices]
            timestamps = timestamps[sort_indices]
            
            # Smooth values if needed
            if len(values) >= self.smoothing_window:
                smoothed_values = savgol_filter(
                    values, 
                    min(self.smoothing_window, len(values) // 2 * 2 + 1), 
                    2
                )
            else:
                smoothed_values = values
            
            # Calculate trend
            x = np.arange(len(smoothed_values))
            slope, intercept, r_value, p_value, std_err = stats.linregress(x, smoothed_values)
            
            # Determine trend direction and strength
            trend_strength = abs(r_value)
            
            if trend_strength < self.trend_threshold or p_value > 0.05:
                trend_direction = "stable"
                trend_strength = 0.0
            elif slope > 0:
                trend_direction = "increasing"
            else:
                trend_direction = "decreasing"
            
            # Calculate trend duration
            trend_duration = timedelta(
                seconds=timestamps[-1] - timestamps[0]
            )
            
            # Generate forecast if trend is significant
            forecast = None
            confidence_intervals = None
            
            if trend_strength > self.trend_threshold and len(values) >= 20:
                forecast, confidence_intervals = self._generate_forecast(
                    values, timestamps, horizon=10
                )
            
            return PerformanceTrend(
                service_id=service_id,
                metric_name=metric_name,
                trend_direction=trend_direction,
                trend_strength=trend_strength,
                trend_duration=trend_duration,
                forecast=forecast,
                confidence_intervals=confidence_intervals,
            )
            
        except Exception as e:
            logger.error(
                "Trend analysis failed",
                service_id=service_id,
                metric_name=metric_name,
                error=str(e),
            )
            return None

    def _generate_forecast(
        self,
        values: np.ndarray,
        timestamps: np.ndarray,
        horizon: int = 10,
    ) -> Tuple[List[float], List[Tuple[float, float]]]:
        """Generate simple linear forecast with confidence intervals."""
        try:
            # Use last 50% of data for forecasting
            n = len(values)
            start_idx = max(0, n // 2)
            
            forecast_values = values[start_idx:]
            x = np.arange(len(forecast_values))
            
            # Linear regression
            slope, intercept, r_value, p_value, std_err = stats.linregress(x, forecast_values)
            
            # Generate forecast points
            forecast_x = np.arange(len(forecast_values), len(forecast_values) + horizon)
            forecast = [slope * x + intercept for x in forecast_x]
            
            # Simple confidence intervals based on standard error
            confidence_intervals = [
                (f - 2 * std_err, f + 2 * std_err) for f in forecast
            ]
            
            return forecast, confidence_intervals
            
        except Exception as e:
            logger.error("Forecast generation failed", error=str(e))
            return [], []