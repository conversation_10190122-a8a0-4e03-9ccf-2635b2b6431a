"""
Discovery Registry Agent (DRA-002)

AI-powered service discovery and registry management with intelligent health monitoring,
capability matching, and service mesh optimization.
"""

__version__ = "1.0.0"
__agent_id__ = "DRA-002"
__intelligence_level__ = "medium-high"

from dra.core.agent import DiscoveryRegistryAgent
from dra.core.platform_agent import PlatformAgent
from dra.core.types import (
    AgentIntelligenceLevel,
    AgentStatus,
    ServiceInfo,
    ServiceRequirements,
    HealthStatus,
)

__all__ = [
    "DiscoveryRegistryAgent",
    "PlatformAgent", 
    "AgentIntelligenceLevel",
    "AgentStatus",
    "ServiceInfo",
    "ServiceRequirements",
    "HealthStatus",
]