"""
API request/response models for the Discovery Registry Agent.

This module defines Pydantic models for API request/response validation
and OpenAPI documentation generation.
"""

from typing import Any, Dict, List, Optional
from datetime import datetime
from enum import Enum

from pydantic import BaseModel, Field

from dra.core.types import (
    ServiceStatus,
    ServiceProtocol,
    ServiceInfo,
    ServiceRequirements,
    ServiceMatch,
    ServiceRegistration,
)


# Request models

class ServiceRegistrationRequest(BaseModel):
    """Request model for service registration."""
    
    service: ServiceInfo = Field(..., description="Service information to register")
    ttl: Optional[int] = Field(None, description="Time-to-live in seconds")
    auto_health_check: bool = Field(True, description="Enable automatic health checks")
    
    class Config:
        json_schema_extra = {
            "example": {
                "service": {
                    "id": "user-service-001",
                    "name": "User Management Service",
                    "namespace": "production",
                    "description": "Handles user authentication and management",
                    "version": "2.1.0",
                    "status": "running",
                    "endpoints": [
                        {
                            "url": "https://api.example.com/users",
                            "protocol": "https",
                            "port": 443,
                            "health_check_path": "/health"
                        }
                    ],
                    "capabilities": [
                        {
                            "name": "user_authentication",
                            "description": "OAuth2 and JWT authentication"
                        }
                    ],
                    "tags": ["authentication", "users", "core"],
                    "metadata": {
                        "team": "platform",
                        "environment": "production"
                    }
                },
                "ttl": 3600,
                "auto_health_check": True
            }
        }


class ServiceUpdateRequest(BaseModel):
    """Request model for service updates."""
    
    service: ServiceInfo = Field(..., description="Updated service information")
    force: bool = Field(False, description="Force update even if service doesn't exist")


class ServiceDiscoveryRequest(BaseModel):
    """Request model for service discovery."""
    
    requirements: ServiceRequirements = Field(..., description="Service requirements")
    include_unhealthy: bool = Field(False, description="Include unhealthy services")
    max_results: int = Field(20, description="Maximum results to return")
    
    class Config:
        json_schema_extra = {
            "example": {
                "requirements": {
                    "namespace": "production",
                    "capabilities": ["user_authentication"],
                    "protocols": ["https"],
                    "tags": ["authentication"],
                    "min_availability": 0.99,
                    "max_response_time": 500,
                    "max_error_rate": 0.01
                },
                "include_unhealthy": False,
                "max_results": 10
            }
        }


class ServiceSearchRequest(BaseModel):
    """Request model for service search."""
    
    query: str = Field(..., description="Search query")
    namespace: Optional[str] = Field(None, description="Namespace filter")
    limit: int = Field(20, description="Maximum results")
    
    class Config:
        json_schema_extra = {
            "example": {
                "query": "user authentication service with OAuth2 support",
                "namespace": "production",
                "limit": 10
            }
        }


class OptimizationAnalysisRequest(BaseModel):
    """Request model for optimization analysis."""
    
    context: Dict[str, Any] = Field(..., description="Service mesh context")
    focus_areas: Optional[List[str]] = Field(None, description="Specific areas to analyze")
    
    class Config:
        json_schema_extra = {
            "example": {
                "context": {
                    "service_mesh_config": {},
                    "performance_metrics": {},
                    "error_rates": {}
                },
                "focus_areas": ["routing", "load_balancing", "performance"]
            }
        }


# Response models

class ServiceRegistrationResponse(BaseModel):
    """Response model for service registration."""
    
    success: bool = Field(..., description="Registration success status")
    service_id: str = Field(..., description="Registered service ID")
    message: str = Field(..., description="Status message")
    health_monitoring_enabled: bool = Field(..., description="Health monitoring status")


class ServiceDeregistrationResponse(BaseModel):
    """Response model for service deregistration."""
    
    success: bool = Field(..., description="Deregistration success status")
    service_id: str = Field(..., description="Deregistered service ID")
    message: str = Field(..., description="Status message")


class ServiceListResponse(BaseModel):
    """Response model for service listing."""
    
    services: List[ServiceInfo] = Field(..., description="List of services")
    total_count: int = Field(..., description="Total number of services")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Page size")


class ServiceDiscoveryResponse(BaseModel):
    """Response model for service discovery."""
    
    matches: List[ServiceMatch] = Field(..., description="Matching services")
    total_evaluated: int = Field(..., description="Total services evaluated")
    ai_powered: bool = Field(..., description="Whether AI matching was used")


class HealthStatusResponse(BaseModel):
    """Response model for health status."""
    
    service_id: str = Field(..., description="Service ID")
    current_status: Optional[str] = Field(None, description="Current health status")
    last_check: Optional[datetime] = Field(None, description="Last health check time")
    recent_events: int = Field(..., description="Number of recent health events")
    prediction: Optional[Dict[str, Any]] = Field(None, description="Health prediction")


class OptimizationRecommendationsResponse(BaseModel):
    """Response model for optimization recommendations."""
    
    recommendations: List[Dict[str, Any]] = Field(..., description="Optimization recommendations")
    analysis_timestamp: datetime = Field(..., description="Analysis timestamp")
    ai_powered: bool = Field(..., description="Whether AI analysis was used")


class RegistryStatisticsResponse(BaseModel):
    """Response model for registry statistics."""
    
    registry_info: Dict[str, Any] = Field(..., description="Registry information")
    service_catalog: Dict[str, Any] = Field(..., description="Service catalog statistics")
    health_monitoring: Optional[Dict[str, Any]] = Field(None, description="Health monitoring statistics")
    operations: Dict[str, int] = Field(..., description="Operation counters")


class ErrorResponse(BaseModel):
    """Error response model."""
    
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Error timestamp")


# Filter models

class ServiceListFilters(BaseModel):
    """Filters for service listing."""
    
    namespace: Optional[str] = Field(None, description="Namespace filter")
    status: Optional[ServiceStatus] = Field(None, description="Status filter")
    tags: Optional[List[str]] = Field(None, description="Tag filters (any match)")
    capabilities: Optional[List[str]] = Field(None, description="Capability filters (any match)")
    limit: int = Field(100, ge=1, le=1000, description="Maximum results")
    offset: int = Field(0, ge=0, description="Pagination offset")


class HealthHistoryFilters(BaseModel):
    """Filters for health history."""
    
    since: Optional[datetime] = Field(None, description="Only events after this time")
    limit: int = Field(100, ge=1, le=1000, description="Maximum events")
    status_filter: Optional[List[str]] = Field(None, description="Filter by health status")


# Utility models

class PaginationInfo(BaseModel):
    """Pagination information."""
    
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Page size")
    total_items: int = Field(..., description="Total number of items")
    total_pages: int = Field(..., description="Total number of pages")
    has_next: bool = Field(..., description="Whether there's a next page")
    has_previous: bool = Field(..., description="Whether there's a previous page")


class HealthcheckResponse(BaseModel):
    """API healthcheck response."""
    
    status: str = Field(..., description="Health status")