"""
API routes for the Discovery Registry Agent.

This module defines FastAPI route handlers for all service discovery operations.
"""

from typing import List, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, Query, Path
from fastapi.responses import JSONResponse
import structlog

from dra.api.models import (
    ServiceRegistrationRequest,
    ServiceRegistrationResponse,
    ServiceDeregistrationResponse,
    ServiceUpdateRequest,
    ServiceListResponse,
    ServiceListFilters,
    ServiceDiscoveryRequest,
    ServiceDiscoveryResponse,
    ServiceSearchRequest,
    HealthStatusResponse,
    HealthHistoryFilters,
    OptimizationAnalysisRequest,
    OptimizationRecommendationsResponse,
    RegistryStatisticsResponse,
    ErrorResponse,
    HealthcheckResponse,
)
from dra.core.types import ServiceInfo, ServiceStatus, ServiceRegistration
from dra.discovery.registry_manager import RegistryManager


logger = structlog.get_logger(__name__)

# Global registry manager instance (will be injected)
registry_manager: Optional[RegistryManager] = None


def get_registry_manager() -> RegistryManager:
    """Dependency to get registry manager."""
    if not registry_manager:
        raise HTTPException(
            status_code=503,
            detail="Registry manager not initialized"
        )
    return registry_manager


def set_registry_manager(manager: RegistryManager) -> None:
    """Set the global registry manager instance."""
    global registry_manager
    registry_manager = manager


# Create router
router = APIRouter()


# Health and status endpoints

@router.get(
    "/health",
    response_model=HealthcheckResponse,
    summary="API Health Check",
    description="Check the health status of the Discovery Registry Agent API",
)
async def health_check():
    """Check API health status."""
    return HealthcheckResponse(status="UP")


@router.get(
    "/statistics",
    response_model=RegistryStatisticsResponse,
    summary="Registry Statistics",
    description="Get comprehensive statistics about the service registry",
)
async def get_statistics(
    manager: RegistryManager = Depends(get_registry_manager)
):
    """Get registry statistics."""
    try:
        stats = await manager.get_registry_statistics()
        return RegistryStatisticsResponse(**stats)
        
    except Exception as e:
        logger.error("Failed to get statistics", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to retrieve statistics")


# Service registration endpoints

@router.post(
    "/services",
    response_model=ServiceRegistrationResponse,
    status_code=201,
    summary="Register Service",
    description="Register a new service in the discovery registry",
)
async def register_service(
    request: ServiceRegistrationRequest,
    manager: RegistryManager = Depends(get_registry_manager)
):
    """Register a new service."""
    try:
        # Create registration object
        registration = ServiceRegistration(
            service=request.service,
            ttl=request.ttl,
            auto_health_check=request.auto_health_check,
        )
        
        # Register service
        success = await manager.register_service(registration)
        
        if not success:
            raise HTTPException(
                status_code=400,
                detail="Service registration failed"
            )
        
        return ServiceRegistrationResponse(
            success=True,
            service_id=request.service.id,
            message="Service registered successfully",
            health_monitoring_enabled=request.auto_health_check and manager.enable_health_monitoring,
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Service registration failed", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put(
    "/services/{service_id}",
    response_model=ServiceRegistrationResponse,
    summary="Update Service",
    description="Update service information in the registry",
)
async def update_service(
    service_id: str = Path(..., description="Service ID to update"),
    request: ServiceUpdateRequest = None,
    manager: RegistryManager = Depends(get_registry_manager)
):
    """Update service information."""
    try:
        # Ensure service ID matches
        if request.service.id != service_id:
            raise HTTPException(
                status_code=400,
                detail="Service ID in path must match service ID in body"
            )
        
        # Update service
        success = await manager.update_service(request.service)
        
        if not success and not request.force:
            raise HTTPException(
                status_code=404,
                detail="Service not found"
            )
        
        return ServiceRegistrationResponse(
            success=True,
            service_id=service_id,
            message="Service updated successfully",
            health_monitoring_enabled=manager.enable_health_monitoring,
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Service update failed", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete(
    "/services/{service_id}",
    response_model=ServiceDeregistrationResponse,
    summary="Deregister Service",
    description="Remove a service from the discovery registry",
)
async def deregister_service(
    service_id: str = Path(..., description="Service ID to deregister"),
    manager: RegistryManager = Depends(get_registry_manager)
):
    """Deregister a service."""
    try:
        success = await manager.deregister_service(service_id)
        
        if not success:
            raise HTTPException(
                status_code=404,
                detail="Service not found"
            )
        
        return ServiceDeregistrationResponse(
            success=True,
            service_id=service_id,
            message="Service deregistered successfully",
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Service deregistration failed", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


# Service discovery endpoints

@router.get(
    "/services",
    response_model=ServiceListResponse,
    summary="List Services",
    description="List services with optional filtering and pagination",
)
async def list_services(
    namespace: Optional[str] = Query(None, description="Filter by namespace"),
    status: Optional[ServiceStatus] = Query(None, description="Filter by status"),
    tags: Optional[List[str]] = Query(None, description="Filter by tags"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum results"),
    offset: int = Query(0, ge=0, description="Pagination offset"),
    manager: RegistryManager = Depends(get_registry_manager)
):
    """List services with filtering."""
    try:
        services = await manager.list_services(
            namespace=namespace,
            status=status,
            limit=limit,
            offset=offset,
        )
        
        # For simplicity, we'll return total_count as len(services)
        # In production, you'd want to get the actual total count
        total_count = len(services)
        page = offset // limit + 1
        
        return ServiceListResponse(
            services=services,
            total_count=total_count,
            page=page,
            page_size=limit,
        )
        
    except Exception as e:
        logger.error("Failed to list services", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get(
    "/services/{service_id}",
    response_model=ServiceInfo,
    summary="Get Service",
    description="Get detailed information about a specific service",
)
async def get_service(
    service_id: str = Path(..., description="Service ID to retrieve"),
    manager: RegistryManager = Depends(get_registry_manager)
):
    """Get service by ID."""
    try:
        service = await manager.get_service(service_id)
        
        if not service:
            raise HTTPException(
                status_code=404,
                detail="Service not found"
            )
        
        return service
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get service", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post(
    "/services/discover",
    response_model=ServiceDiscoveryResponse,
    summary="Discover Services",
    description="Discover services based on requirements using AI-powered matching",
)
async def discover_services(
    request: ServiceDiscoveryRequest,
    manager: RegistryManager = Depends(get_registry_manager)
):
    """Discover services based on requirements."""
    try:
        matches = await manager.discover_services(
            requirements=request.requirements,
            include_unhealthy=request.include_unhealthy,
        )
        
        # Limit results
        limited_matches = matches[:request.max_results]
        
        return ServiceDiscoveryResponse(
            matches=limited_matches,
            total_evaluated=len(matches),
            ai_powered=manager.enable_ai_capabilities,
        )
        
    except Exception as e:
        logger.error("Service discovery failed", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post(
    "/services/search",
    response_model=List[ServiceInfo],
    summary="Search Services",
    description="Search services using natural language queries",
)
async def search_services(
    request: ServiceSearchRequest,
    manager: RegistryManager = Depends(get_registry_manager)
):
    """Search services using natural language."""
    try:
        services = await manager.search_services(
            query=request.query,
            namespace=request.namespace,
            limit=request.limit,
        )
        
        return services
        
    except Exception as e:
        logger.error("Service search failed", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


# Health monitoring endpoints

@router.get(
    "/services/health/all",
    summary="Get All Services Health",
    description="Get health status for all registered services in the platform",
)
async def get_all_services_health(
    manager: RegistryManager = Depends(get_registry_manager)
):
    """Get health status for all services in the platform."""
    try:
        # Define all 14 services in the platform
        ALL_SERVICES = [
            {"name": "Communication Broker", "port": 8080, "technology": "Go", "health_endpoint": "/health"},
            {"name": "Discovery Registry", "port": 8081, "technology": "Python", "health_endpoint": "/api/v1/health"},
            {"name": "Resource Manager", "port": 8083, "technology": "Java", "health_endpoint": "/actuator/health"},
            {"name": "Resource Manager AI", "port": 9083, "technology": "Python", "health_endpoint": "/health"},
            {"name": "Knowledge Base", "port": 8086, "technology": "Java", "health_endpoint": "/actuator/health"},
            {"name": "Knowledge Base AI", "port": 9088, "technology": "Python", "health_endpoint": "/health"},
            {"name": "Task Orchestrator", "port": 8090, "technology": "Java", "health_endpoint": "/actuator/health"},
            {"name": "Task Orchestrator AI", "port": 9090, "technology": "Python", "health_endpoint": "/health"},
            {"name": "Agent Factory", "port": 8091, "technology": "Java", "health_endpoint": "/actuator/health"},
            {"name": "Agent Factory AI", "port": 9091, "technology": "Python", "health_endpoint": "/health"},
            {"name": "Supreme Intelligence", "port": 8092, "technology": "Java", "health_endpoint": "/actuator/health"},
            {"name": "Supreme Intelligence AI", "port": 9092, "technology": "Python", "health_endpoint": "/health"},
            {"name": "Security Monitor AI", "port": 9082, "technology": "Python", "health_endpoint": "/health"},
            {"name": "Data Processing AI", "port": 9084, "technology": "Python", "health_endpoint": "/health"},
        ]
        
        import httpx
        import asyncio
        
        async def check_service_health(service):
            """Check health of a single service."""
            url = f"http://localhost:{service['port']}{service['health_endpoint']}"
            start_time = datetime.utcnow()
            
            try:
                timeout = httpx.Timeout(3.0)
                async with httpx.AsyncClient(timeout=timeout) as client:
                    response = await client.get(url)
                    response_time = (datetime.utcnow() - start_time).total_seconds() * 1000
                    
                    return {
                        "name": service["name"],
                        "port": service["port"],
                        "technology": service["technology"],
                        "url": f"http://localhost:{service['port']}",
                        "health_endpoint": service["health_endpoint"],
                        "status": "running" if response.status_code == 200 else "error",
                        "response_time": int(response_time),
                        "http_status": response.status_code
                    }
            except Exception as e:
                response_time = (datetime.utcnow() - start_time).total_seconds() * 1000
                return {
                    "name": service["name"],
                    "port": service["port"],
                    "technology": service["technology"],
                    "url": f"http://localhost:{service['port']}",
                    "health_endpoint": service["health_endpoint"],
                    "status": "stopped",
                    "response_time": int(response_time),
                    "error": str(e)
                }
        
        # Check all services concurrently
        tasks = [check_service_health(service) for service in ALL_SERVICES]
        results = await asyncio.gather(*tasks)
        
        # Calculate summary statistics
        running_count = sum(1 for result in results if result["status"] == "running")
        stopped_count = sum(1 for result in results if result["status"] == "stopped")
        error_count = sum(1 for result in results if result["status"] == "error")
        
        avg_response_time = 0
        if running_count > 0:
            total_response_time = sum(result.get("response_time", 0) for result in results if result["status"] == "running")
            avg_response_time = total_response_time / running_count
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "total_services": len(ALL_SERVICES),
            "running_services": running_count,
            "stopped_services": stopped_count,
            "error_services": error_count,
            "health_score": int((running_count / len(ALL_SERVICES)) * 100),
            "avg_response_time": int(avg_response_time),
            "services": results
        }
        
    except Exception as e:
        logger.error("Failed to get all services health", error=str(e))
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get(
    "/services/{service_id}/health",
    response_model=HealthStatusResponse,
    summary="Get Service Health",
    description="Get comprehensive health information for a service",
)
async def get_service_health(
    service_id: str = Path(..., description="Service ID to check"),
    manager: RegistryManager = Depends(get_registry_manager)
):
    """Get service health information."""
    try:
        if not manager.enable_health_monitoring:
            raise HTTPException(
                status_code=503,
                detail="Health monitoring not enabled"
            )
        
        health_info = await manager.get_service_health(service_id)
        
        if not health_info:
            raise HTTPException(
                status_code=404,
                detail="Service not found or health information unavailable"
            )
        
        return HealthStatusResponse(**health_info)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get service health", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post(
    "/services/{service_id}/health/check",
    summary="Trigger Health Check",
    description="Trigger an immediate health check for a service",
)
async def trigger_health_check(
    service_id: str = Path(..., description="Service ID to check"),
    manager: RegistryManager = Depends(get_registry_manager)
):
    """Trigger immediate health check."""
    try:
        if not manager.enable_health_monitoring:
            raise HTTPException(
                status_code=503,
                detail="Health monitoring not enabled"
            )
        
        success = await manager.trigger_health_check(service_id)
        
        if not success:
            raise HTTPException(
                status_code=404,
                detail="Service not found or health check failed"
            )
        
        return {"message": "Health check triggered successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Health check trigger failed", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


# AI and optimization endpoints

@router.post(
    "/optimization/analyze",
    response_model=OptimizationRecommendationsResponse,
    summary="Get Optimization Recommendations",
    description="Get AI-powered optimization recommendations for service mesh",
)
async def get_optimization_recommendations(
    request: OptimizationAnalysisRequest,
    manager: RegistryManager = Depends(get_registry_manager)
):
    """Get optimization recommendations."""
    try:
        if not manager.enable_ai_capabilities:
            raise HTTPException(
                status_code=503,
                detail="AI capabilities not enabled"
            )
        
        recommendations = await manager.get_optimization_recommendations(
            request.context
        )
        
        return OptimizationRecommendationsResponse(
            recommendations=recommendations,
            analysis_timestamp=datetime.utcnow(),
            ai_powered=True,
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Optimization analysis failed", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


# Error handlers will be added at the app level in main.py