"""
Main FastAPI application for the Discovery Registry Agent.

This module creates and configures the FastAPI application with all
routes, middleware, and dependencies.
"""

import asyncio
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
import structlog

from dra.api.routes import router, set_registry_manager
from dra.discovery.registry_manager import RegistryManager
from dra.discovery.service_catalog import CatalogBackend


logger = structlog.get_logger(__name__)

# Global registry manager
registry_manager: RegistryManager = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    global registry_manager
    
    logger.info("Starting Discovery Registry Agent API")
    
    try:
        # Initialize registry manager
        registry_manager = RegistryManager(
            catalog_backend=CatalogBackend.REDIS,  # Can be configured
            enable_health_monitoring=True,
            enable_ai_capabilities=True,
        )
        
        await registry_manager.initialize()
        
        # Set in routes module
        set_registry_manager(registry_manager)
        
        logger.info("Discovery Registry Agent API started successfully")
        
        yield
        
    except Exception as e:
        logger.error("Failed to start Discovery Registry Agent API", error=str(e))
        raise
    finally:
        # Shutdown
        logger.info("Shutting down Discovery Registry Agent API")
        
        if registry_manager:
            await registry_manager.shutdown()
        
        logger.info("Discovery Registry Agent API shutdown complete")


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    
    app = FastAPI(
        title="Discovery Registry Agent",
        description="""
AI-Native Service Discovery and Registry Agent

The Discovery Registry Agent (DRA) provides intelligent service discovery capabilities
with AI-powered health prediction, capability matching, and optimization recommendations.

## Features

- **Intelligent Service Discovery**: AI-powered service matching based on capabilities and requirements
- **Predictive Health Monitoring**: Real-time health monitoring with predictive analytics
- **Capability Matching**: Semantic understanding of service capabilities and requirements  
- **Service Mesh Optimization**: AI-driven recommendations for performance and reliability
- **Multi-Backend Support**: Redis, etcd, Consul, and Kubernetes integration
- **Real-time Analytics**: Comprehensive metrics and monitoring

## AI Capabilities

- **Health Prediction**: Predict service failures before they occur
- **Smart Matching**: Semantic similarity matching for service discovery
- **Optimization Analysis**: AI-powered service mesh optimization recommendations
- **Anomaly Detection**: Automated detection of service performance issues

## Architecture

The DRA is built as an AI-Native agent that integrates with:
- Communication Broker Agent (CBA) for inter-agent communication
- Various service registry backends (Redis, etcd, Consul, K8s)
- Multiple AI providers (Anthropic Claude, OpenAI, Google Gemini)
- Service mesh platforms and monitoring systems
        """.strip(),
        version="1.0.0",
        contact={
            "name": "AI Platform Team",
            "email": "<EMAIL>",
        },
        license_info={
            "name": "MIT",
            "url": "https://opensource.org/licenses/MIT",
        },
        lifespan=lifespan,
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json",
    )
    
    # Add middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    
    # Include routers
    app.include_router(
        router,
        prefix="/api/v1",
        tags=["Discovery Registry"],
    )
    
    # Root endpoint
    @app.get("/", include_in_schema=False)
    async def root():
        """Root endpoint with basic information."""
        return {
            "service": "Discovery Registry Agent",
            "version": "1.0.0",
            "status": "running",
            "api_docs": "/docs",
            "health_check": "/api/v1/health",
        }
    
    # Global exception handler
    @app.exception_handler(Exception)
    async def global_exception_handler(request, exc: Exception):
        """Global exception handler."""
        logger.error(
            "Unhandled exception",
            path=request.url.path,
            method=request.method,
            error=str(exc),
        )
        
        return JSONResponse(
            status_code=500,
            content={
                "error": "internal_server_error",
                "message": "An unexpected error occurred",
                "path": request.url.path,
            }
        )
    
    return app


# Create the app instance
app = create_app()


# CLI runner
if __name__ == "__main__":
    import uvicorn
    
    # Configure logging
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer(),
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Run the server
    import os
    port = int(os.getenv("DRA_PORT", 9081))
    uvicorn.run(
        "dra.api.main:app",
        host="0.0.0.0",
        port=port,
        reload=False,
        log_level="info",
    )