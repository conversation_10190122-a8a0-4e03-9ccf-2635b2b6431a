#!/bin/bash

echo "Fixing package declarations and imports..."

# Function to fix package declarations in a directory
fix_packages() {
    local dir=$1
    local new_base_package=$2
    
    echo "Processing directory: $dir"
    echo "New base package: $new_base_package"
    
    # Find all Java files
    find "$dir" -name "*.java" -type f | while read -r file; do
        echo "Processing: $file"
        
        # Get the relative path from the base directory
        relative_path=$(dirname "$file" | sed "s|$dir||" | sed 's|^/||' | sed 's|/|.|g')
        
        if [ -n "$relative_path" ]; then
            new_package="$new_base_package.$relative_path"
        else
            new_package="$new_base_package"
        fi
        
        # Update package declaration
        sed -i '' "s/^package ai\.twodot\.platform\.agents\.[^;]*/package $new_package/" "$file"
        
        # Update imports (basic fixes)
        sed -i '' 's/import ai\.twodot\.platform\.agents\./import com.multiagent.platform.agents./g' "$file"
        sed -i '' 's/import ai\.twodot\.platform\.core\./import com.multiagent.platform.core./g' "$file"
        sed -i '' 's/import ai\.twodot\.platform\.facade\./import com.multiagent.platform.facade./g' "$file"
        
        echo "  Updated package to: $new_package"
    done
}

# Fix migrated agent packages
fix_packages "src/agents/security-monitor" "com.multiagent.platform.agents.security"
fix_packages "src/agents/resource-manager" "com.multiagent.platform.agents.resource"
fix_packages "src/agents/data-processing" "com.multiagent.platform.agents.data"
fix_packages "src/agents/knowledge-base" "com.multiagent.platform.agents.knowledge"
fix_packages "src/agents/task-orchestrator" "com.multiagent.platform.agents.orchestration"
fix_packages "src/agents/agent-factory" "com.multiagent.platform.agents.factory"
fix_packages "src/agents/supreme-intelligence" "com.multiagent.platform.agents.intelligence"

# Fix core framework packages
fix_packages "meta-agent-core/src/main/java" "com.multiagent.platform.core"
fix_packages "meta-agent-facade/src/main/java" "com.multiagent.platform.facade"

echo "Package fixes completed!"

# Create a summary of changes
echo ""
echo "Summary of package changes:"
echo "- Security Monitor: ai.twodot.platform.agents.security -> com.multiagent.platform.agents.security"
echo "- Resource Manager: ai.twodot.platform.agents.resource -> com.multiagent.platform.agents.resource"
echo "- Data Processing: ai.twodot.platform.agents.data -> com.multiagent.platform.agents.data"
echo "- Knowledge Base: ai.twodot.platform.agents.knowledge -> com.multiagent.platform.agents.knowledge"
echo "- Task Orchestrator: ai.twodot.platform.agents.orchestration -> com.multiagent.platform.agents.orchestration"
echo "- Agent Factory: ai.twodot.platform.agents.factory -> com.multiagent.platform.agents.factory"
echo "- Supreme Intelligence: ai.twodot.platform.agents.intelligence -> com.multiagent.platform.agents.intelligence"
echo ""
echo "Additional fixes needed:"
echo "1. Manual verification of complex imports"
echo "2. Update Spring component scan paths"
echo "3. Fix any remaining compilation errors"
echo "4. Update test package declarations"